import { Platform } from 'react-native';
import { openBrowserAsync } from 'expo-web-browser';

/**
 * Opens a URI in a new tab for browser platforms or in an in-app browser for non-browser platforms
 * @param uri The URI to open
 */
export const openUri = async (uri: string): Promise<void> => {
  // For browser platforms, open in a new tab
  if (Platform.OS === 'web') {
    window.open(uri, '_blank');
    return;
  }

  // For non-browser platforms, open in in-app browser
  await openBrowserAsync(uri);
};
