import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from './logger';

// Token manager utility functions
const tokenManager = {
  // Clear JWT token and user data
  clearTokens: async () => {
    try {
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');
      logger.info('JWT token and user data cleared successfully');
      return true;
    } catch (error) {
      logger.error('Error clearing JWT token and user data:', error);
      return false;
    }
  },

  // Set JWT token and user data
  setTokens: async (jwt, userData) => {
    try {
      await AsyncStorage.setItem('auth_token', jwt);
      await AsyncStorage.setItem('user_data', JSON.stringify(userData));
      logger.info('JWT token and user data set successfully');
      return true;
    } catch (error) {
      logger.error('Error setting JWT token and user data:', error);
      return false;
    }
  },

  // Get JWT token
  getToken: async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      return token;
    } catch (error) {
      logger.error('Error getting JWT token:', error);
      return null;
    }
  },

  // Get user data
  getUserData: async () => {
    try {
      const userData = await AsyncStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      logger.error('Error getting user data:', error);
      return null;
    }
  }
};

export default tokenManager;
