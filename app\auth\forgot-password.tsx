import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  ImageBackground,
  Dimensions,
  Animated,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import authService from '@/services/authService';
import Footer from '@/components/layout/Footer';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [emailFocused, setEmailFocused] = useState(false);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { width } = Dimensions.get('window');

  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];

  // Animate the screen when it mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const handleSendOtp = async () => {
    // Basic validation
    if (!email) {
      setError('Lütfen e-posta adresinizi girin');
      return;
    }

    if (!email.includes('@')) {
      setError('Lütfen geçerli bir e-posta adresi girin');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      console.log('Sending forgot password OTP to email:', email);

      // Send request to forgot password endpoint
      const response = await authService.forgotPassword(email);

      console.log('Forgot password response:', response);

      setSuccess(true);

      // Store a flag to indicate this is a password reset flow
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        AsyncStorage.setItem('password_reset_flow', 'true');
      } catch (error) {
        console.error('Failed to store password reset flag:', error);
      }

      // Show success message and immediately navigate
      Alert.alert(
        'Doğrulama Kodu Gönderildi',
        'Şifre sıfırlama kodunuz e-posta adresinize gönderildi. Doğrulama sayfasına yönlendiriliyorsunuz.'
      );

      // Navigate to the existing OTP verification screen with the email
      console.log('Immediately navigating to OTP verification screen with email:', email);
      setTimeout(() => {
        router.push({
          pathname: '/auth/verify-otp',
          params: {
            email,
            isPasswordReset: 'true' // Add a parameter to indicate this is a password reset flow
          }
        });
      }, 500); // Short delay to ensure the alert is shown
    } catch (err: any) {
      setIsLoading(false);

      console.error('Forgot password error details:', {
        message: err.message,
        response: err.response ? {
          status: err.response.status,
          data: err.response.data
        } : 'No response',
        request: err.request ? 'Request exists' : 'No request'
      });

      // Show appropriate error message
      if (err.response && err.response.status === 400) {
        setError('Geçersiz e-posta adresi. Lütfen kontrol edip tekrar deneyin.');
      } else if (err.response && err.response.data && err.response.data.message) {
        setError(err.response.data.message);
      } else if (err.message && err.message.includes('Network Error')) {
        setError('Sunucuya bağlanılamıyor. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        setError('Bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <View style={{flex: 1, width: '100%', display: 'flex', flexDirection: 'column'}}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <ImageBackground
            source={require('../../assets/images/law-background.jpg')}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          >
            <View style={styles.mainContainer}>
              <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
              >
                <Animated.View
                  style={[
                    styles.formWrapper,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                  <View style={styles.formHeader}>
                    <Text style={styles.formTitle}>Şifremi Unuttum</Text>
                    <View style={styles.formTitleUnderline} />
                  </View>

                  <View style={styles.formContainer}>
                    {/* Info Message */}
                    <View style={styles.infoContainer}>
                      <Ionicons name="information-circle" size={24} color="#4A78B0" />
                      <Text style={styles.infoText}>
                        Şifrenizi sıfırlamak için kayıtlı e-posta adresinizi girin. Size bir doğrulama kodu göndereceğiz.
                      </Text>
                    </View>

                    {/* Error Message */}
                    {error ? (
                      <Animated.View style={styles.errorContainer}>
                        <Ionicons name="alert-circle" size={20} color="#EF4444" />
                        <Text style={styles.errorText}>{error}</Text>
                      </Animated.View>
                    ) : null}

                    {/* Email Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="mail-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>E-posta</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        emailFocused && styles.inputWrapperFocused
                      ]}>
                        <TextInput
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="E-posta adresinizi girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={email}
                          onChangeText={setEmail}
                          autoCapitalize="none"
                          keyboardType="email-address"
                          onFocus={() => setEmailFocused(true)}
                          onBlur={() => setEmailFocused(false)}
                          returnKeyType="done"
                          onSubmitEditing={handleSendOtp}
                        />
                      </View>
                    </View>

                    {/* Send OTP Button */}
                    <TouchableOpacity
                      style={[styles.button, isLoading && styles.buttonDisabled]}
                      onPress={handleSendOtp}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <View style={styles.loadingContainer}>
                          <Text style={styles.buttonText}>Gönderiliyor</Text>
                          <View style={styles.loadingDots}>
                            <View style={styles.loadingDot} />
                            <View style={[styles.loadingDot, styles.loadingDotMiddle]} />
                            <View style={styles.loadingDot} />
                          </View>
                        </View>
                      ) : (
                        <Text style={styles.buttonText}>Doğrulama Kodu Gönder</Text>
                      )}
                    </TouchableOpacity>

                    {/* Back to Login Link */}
                    <View style={styles.footer}>
                      <Link href="/auth/login" asChild>
                        <TouchableOpacity>
                          <View style={styles.backToLoginContainer}>
                            <Ionicons name="arrow-back" size={16} color="#4A78B0" />
                            <ThemedText style={styles.backToLoginText}>Giriş Sayfasına Dön</ThemedText>
                          </View>
                        </TouchableOpacity>
                      </Link>
                    </View>
                  </View>
                </Animated.View>
              </ScrollView>
            </View>
          </ImageBackground>
        </KeyboardAvoidingView>
        {width > 768 && (
          <View style={styles.footerContainer}>
            <Footer />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  footerContainer: {
    width: '100%',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingBottom: Platform.OS === 'web' ? 0 : 50,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  mainContainer: {
    flexDirection: 'column',
    position: 'relative',
    overflow: 'hidden',
    width: '90%',
    maxWidth: 500,
    borderRadius: 20,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
    marginVertical: Platform.OS === 'web' ? 0 : 20,
    backgroundColor: '#FFFFFF',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 30 : 20,
    paddingVertical: Platform.OS === 'web' ? 50 : 30,
    minHeight: Platform.OS === 'web' ? '100%' : 'auto',
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginBottom: 8,
  },
  formTitleUnderline: {
    width: 40,
    height: 4,
    backgroundColor: '#4A78B0',
    borderRadius: 2,
  },
  formContainer: {
    width: '100%',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(74, 120, 176, 0.1)',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  infoText: {
    color: '#1E293B',
    marginLeft: 12,
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 20,
  },
  errorText: {
    color: '#EF4444',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    marginRight: 8,
    color: '#1E3A8A',
  },
  label: {
    fontWeight: '600',
    fontSize: 16,
    color: '#1E3A8A',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#CBD5E1',
    backgroundColor: '#FFFFFF',
    zIndex: 10,
  },
  inputWrapperFocused: {
    borderColor: '#1E3A8A',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 0px 5px rgba(30, 58, 138, 0.2)' }
      : {
          shadowColor: '#1E3A8A',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.2,
          shadowRadius: 5,
        }
    ),
    elevation: 2,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'transparent',
    color: '#1E293B',
    zIndex: 20,
  },
  inputLight: {
    color: '#1E293B',
  },
  inputDark: {
    color: '#1E293B',
  },
  button: {
    backgroundColor: '#4A78B0',
    height: 50,
    borderRadius: 25,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 4px 8px rgba(74, 120, 176, 0.3)' }
      : {
          shadowColor: '#4A78B0',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }
    ),
    elevation: 5,
    paddingHorizontal: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    marginLeft: 8,
    alignItems: 'center',
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
  loadingDotMiddle: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  footer: {
    alignItems: 'center',
    marginTop: 16,
  },
  backToLoginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  backToLoginText: {
    color: '#4A78B0',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
});