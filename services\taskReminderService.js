import notificationService from './notificationService';
import taskService from './taskService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Task Reminder servisi
const taskReminderService = {
  // Tüm görevler için hatırlatıcıları planla
  scheduleAllTaskReminders: async () => {
    try {
      // Önce tüm bildirimleri iptal et
      await notificationService.cancelAllNotifications();
      
      // Bildirim ayarlarını kontrol et
      const settings = await notificationService.getNotificationSettings();
      if (!settings || !settings.enabled) {
        return;
      }
      
      // Tüm görevleri getir
      const tasks = await taskService.getAllTasks();
      
      // Her görev için hatırlatıcı planla
      for (const task of tasks) {
        // Tamamlanmış görevler için hatırlatıcı planlanmaz
        if (task.status === 'COMPLETED' || task.completed) {
          continue;
        }
        
        // Görev zamanı geçmişse hatırlatıcı planlanmaz
        const taskDate = new Date(task.dueDate);
        if (taskDate < new Date()) {
          continue;
        }
        
        // Görev tipine göre hatırlatıcı planla
        if (task.type === 'DURUŞMA' && settings.hearingReminders) {
          await notificationService.scheduleTaskReminder(task);
        } else if (task.type === 'HATIRLATMA' && settings.deadlineReminders) {
          await notificationService.scheduleTaskReminder(task);
        } else if (settings.taskReminders) {
          await notificationService.scheduleTaskReminder(task);
        }
      }
      
      return true;
    } catch (error) {
      console.error('Schedule all task reminders error:', error);
      return false;
    }
  },
  
  // Belirli bir görev için hatırlatıcı planla
  scheduleTaskReminder: async (task) => {
    try {
      // Bildirim ayarlarını kontrol et
      const settings = await notificationService.getNotificationSettings();
      if (!settings || !settings.enabled) {
        return null;
      }
      
      // Tamamlanmış görev için hatırlatıcı planlanmaz
      if (task.status === 'COMPLETED' || task.completed) {
        return null;
      }
      
      // Görev zamanı geçmişse hatırlatıcı planlanmaz
      const taskDate = new Date(task.dueDate);
      if (taskDate < new Date()) {
        return null;
      }
      
      // Görev tipine göre hatırlatıcı planla
      if (task.type === 'DURUŞMA' && !settings.hearingReminders) {
        return null;
      } else if (task.type === 'HATIRLATMA' && !settings.deadlineReminders) {
        return null;
      } else if (!settings.taskReminders) {
        return null;
      }
      
      // Hatırlatıcı planla
      const notificationId = await notificationService.scheduleTaskReminder(task);
      
      // Hatırlatıcı ID'sini kaydet
      if (notificationId) {
        await taskReminderService.saveTaskReminderMapping(task.id, notificationId);
      }
      
      return notificationId;
    } catch (error) {
      console.error(`Schedule task reminder error for task ${task.id}:`, error);
      return null;
    }
  },
  
  // Görev-Hatırlatıcı eşleştirmesini kaydet
  saveTaskReminderMapping: async (taskId, notificationId) => {
    try {
      const mappings = await taskReminderService.getTaskReminderMappings();
      mappings[taskId] = notificationId;
      await AsyncStorage.setItem('taskReminderMappings', JSON.stringify(mappings));
      return true;
    } catch (error) {
      console.error('Save task reminder mapping error:', error);
      return false;
    }
  },
  
  // Görev-Hatırlatıcı eşleştirmelerini getir
  getTaskReminderMappings: async () => {
    try {
      const mappings = await AsyncStorage.getItem('taskReminderMappings');
      return mappings ? JSON.parse(mappings) : {};
    } catch (error) {
      console.error('Get task reminder mappings error:', error);
      return {};
    }
  },
  
  // Belirli bir görevin hatırlatıcısını iptal et
  cancelTaskReminder: async (taskId) => {
    try {
      const mappings = await taskReminderService.getTaskReminderMappings();
      const notificationId = mappings[taskId];
      
      if (notificationId) {
        await notificationService.cancelNotification(notificationId);
        
        // Eşleştirmeyi kaldır
        delete mappings[taskId];
        await AsyncStorage.setItem('taskReminderMappings', JSON.stringify(mappings));
      }
      
      return true;
    } catch (error) {
      console.error(`Cancel task reminder error for task ${taskId}:`, error);
      return false;
    }
  },
  
  // Tüm görev hatırlatıcılarını iptal et
  cancelAllTaskReminders: async () => {
    try {
      await notificationService.cancelAllNotifications();
      await AsyncStorage.removeItem('taskReminderMappings');
      return true;
    } catch (error) {
      console.error('Cancel all task reminders error:', error);
      return false;
    }
  },
  
  // Görev güncellendiğinde hatırlatıcıyı güncelle
  updateTaskReminder: async (task) => {
    try {
      // Önce mevcut hatırlatıcıyı iptal et
      await taskReminderService.cancelTaskReminder(task.id);
      
      // Yeni hatırlatıcı planla
      return await taskReminderService.scheduleTaskReminder(task);
    } catch (error) {
      console.error(`Update task reminder error for task ${task.id}:`, error);
      return null;
    }
  },
};

export default taskReminderService;
