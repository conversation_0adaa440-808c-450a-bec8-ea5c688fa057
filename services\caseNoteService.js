import apiClient from './api';

// Dava notu servisi
const caseNoteService = {
  // Tüm dava notlarını getir
  getAllCaseNotes: async () => {
    try {
      const response = await apiClient.get('/api/user/cases/notes');
      return response.data;
    } catch (error) {
      console.error('Get all case notes error:', error);
      throw error;
    }
  },

  // Belirli bir davanın notlarını getir
  getCaseNotesByCaseNumber: async (caseNumber) => {
    try {
      const response = await apiClient.get(`/api/user/cases/notes/case?caseNumber=${caseNumber}`);
      return response.data;
    } catch (error) {
      console.error(`Get case notes for case ${caseNumber} error:`, error);
      throw error;
    }
  },

  // Dava notu detayını getir
  getCaseNoteById: async (noteId) => {
    try {
      const response = await apiClient.get(`/api/user/cases/notes/by-id?id=${noteId}`);
      return response.data;
    } catch (error) {
      console.error(`Get case note ${noteId} error:`, error);
      throw error;
    }
  },

  // Yeni dava notu oluştur
  createCaseNote: async (noteData) => {
    try {
      const response = await apiClient.post('/api/user/cases/notes', noteData);
      return response.data;
    } catch (error) {
      console.error('Create case note error:', error);
      throw error;
    }
  },

  // Dava notu güncelle
  updateCaseNote: async (noteId, noteData) => {
    try {
      const response = await apiClient.put(`/api/user/cases/notes/update?id=${noteId}`, noteData);
      return response.data;
    } catch (error) {
      console.error(`Update case note ${noteId} error:`, error);
      throw error;
    }
  },

  // Dava notu sil
  deleteCaseNote: async (noteId) => {
    try {
      const response = await apiClient.delete(`/api/user/cases/notes/${noteId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete case note ${noteId} error:`, error);
      throw error;
    }
  }
};

export default caseNoteService;
