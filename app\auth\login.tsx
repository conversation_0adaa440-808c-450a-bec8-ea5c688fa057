import React, { useState, useContext, useRef } from 'react';
import { StyleSheet, TextInput, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView, View, Text, Dimensions, Animated, SafeAreaView, Alert, ImageBackground } from 'react-native';
import { Link, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import authService from '@/services/authService';
import { AuthContext } from '@/app/_layout';
import Footer from '@/components/layout/Footer';

export default function LoginScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { login: contextLogin } = useContext(AuthContext);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);

  const { width, height } = Dimensions.get('window');
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];

  // Input referansı
  const passwordRef = useRef(null);

  // Animate the login screen when it mounts and check for verified email
  React.useEffect(() => {
    // Animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();

    // Check for verified email in AsyncStorage
    const checkVerifiedEmail = async () => {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const verifiedEmail = await AsyncStorage.getItem('verifiedEmail');
        const showVerificationSuccess = await AsyncStorage.getItem('show_verification_success');

        if (verifiedEmail) {
          console.log('Found verified email in storage:', verifiedEmail);
          setEmail(verifiedEmail);

          // Show welcome message with specific instructions if coming from verification
          if (showVerificationSuccess === 'true') {
            Alert.alert(
              'E-posta Doğrulandı',
              'E-posta adresiniz başarıyla doğrulandı. Lütfen kayıt olurken oluşturduğunuz şifre ile giriş yapın.',
              [{ text: 'Tamam' }]
            );

            // Clear the verification success flag
            await AsyncStorage.removeItem('show_verification_success');
          }

          // Clear the verified email from storage after showing the message
          await AsyncStorage.removeItem('verifiedEmail');
        }
      } catch (error) {
        console.error('Error checking for verified email:', error);
      }
    };

    checkVerifiedEmail();
  }, []);

  const handleLogin = async () => {
    // Basic validation
    if (!email || !password) {
      setError('Lütfen tüm alanları doldurun');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      console.log('Attempting login with email:', email);

      // Backend API'ye istek gönder
      const response = await authService.login(email, password);

      console.log('Login successful, response:', response);

      // Context'i güncelle
      contextLogin();

      // Ana sayfaya yönlendir
      router.replace('/(tabs)');
    } catch (err) {
      setIsLoading(false);

      console.error('Login error details:', {
        message: err.message,
        response: err.response ? {
          status: err.response.status,
          data: err.response.data
        } : 'No response',
        request: err.request ? 'Request exists' : 'No request'
      });

      // Hata mesajını göster
      if (err.response && err.response.status === 401) {
        setError('Geçersiz e-posta veya şifre.');
      } else if (err.response && err.response.data && err.response.data.message) {
        setError(err.response.data.message);
      } else if (err.message && err.message.includes('Network Error')) {
        setError('Sunucuya bağlanılamıyor. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        setError('Giriş başarısız. Lütfen daha sonra tekrar deneyin. Hata: ' + err.message);
      }

      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleSecureEntry = () => {
    setSecureTextEntry(!secureTextEntry);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <View style={{flex: 1, width: '100%', display: 'flex', flexDirection: 'column'}}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <ImageBackground
            source={require('../../assets/images/law-background.jpg')}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          >
            <View style={styles.mainContainer}>
            {Platform.OS === 'web' ? (
              <>
                {/* Arka plan gradient - Web görünümü için */}
                <LinearGradient
                  colors={['#4A78B0', '#8FB8DE']}
                  style={styles.backgroundGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                />

                {/* Dekoratif şekiller - Web görünümü için */}
                <Animated.View style={[styles.decorativeCircle, styles.decorativeCircle1, {
                  transform: [{ scale: fadeAnim }]
                }]} />
                <Animated.View style={[styles.decorativeCircle, styles.decorativeCircle2, {
                  transform: [{ scale: fadeAnim }]
                }]} />
                <Animated.View style={[styles.decorativeCircle, styles.decorativeCircle3, {
                  transform: [{ scale: fadeAnim }]
                }]} />

                {/* Sol Taraf - Logo ve Motto - Web görünümü için */}
                <View style={styles.leftSection}>
                  <LinearGradient
                    colors={['rgba(255,255,255,0.15)', 'rgba(255,255,255,0.05)']}
                    style={styles.leftGradientOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  />
                  <View style={styles.leftContent}>
                    <View style={styles.logoContainer}>
                      <Animated.View
                        style={[styles.logoIconContainer, {
                          transform: [{ scale: fadeAnim }]
                        }]}
                      >
                        <MaterialCommunityIcons name="scale-balance" size={60} color="#FFFFFF" />
                      </Animated.View>
                      <Animated.Text style={[styles.appTitle, { opacity: fadeAnim }]}>AVAS</Animated.Text>
                      <Animated.Text style={[styles.motto, { opacity: fadeAnim }]}>Adaletin Dijital Asistanı</Animated.Text>
                      <Animated.View style={[styles.divider, { opacity: fadeAnim, transform: [{ scaleX: fadeAnim }] }]} />
                      <Animated.Text style={[styles.descriptionText, { opacity: fadeAnim }]}>
                        Modern avukatlık süreçlerinizi dijitalleştirin,
                        dava ve müvekkil yönetimini kolaylaştırın.
                      </Animated.Text>
                    </View>
                  </View>
                </View>
              </>
            ) : (
              // Mobil için sadece minimal bir header göster
              <View style={styles.mobileHeader}>
                <View style={styles.mobileLogoContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={30} color="#4A78B0" />
                  <Text style={styles.mobileAppTitle}>AVAS</Text>
                </View>
                <Text style={styles.mobileSubtitle}>Avukatlık Asistanı</Text>
              </View>
            )}

            {/* Sağ Taraf - Giriş Formu */}
            <View style={[styles.rightSection, Platform.OS !== 'web' && styles.mobileRightSection]}>
              <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
              >
                <Animated.View
                  style={[
                    styles.formWrapper,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                <View style={styles.formHeader}>
                  <Text style={styles.formTitle}>Giriş Yapın</Text>
                  <View style={styles.formTitleUnderline} />
                </View>

                <View style={styles.formContainer}>
                    {/* Error Message */}
                    {error ? (
                      <Animated.View style={styles.errorContainer}>
                        <Ionicons name="alert-circle" size={20} color="#EF4444" />
                        <Text style={styles.errorText}>{error}</Text>
                      </Animated.View>
                    ) : null}

                    {/* Email Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="mail-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>E-posta</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        emailFocused && styles.inputWrapperFocused
                      ]}>
                        <TextInput
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="E-posta adresinizi girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={email}
                          onChangeText={setEmail}
                          autoCapitalize="none"
                          keyboardType="email-address"
                          onFocus={() => setEmailFocused(true)}
                          onBlur={() => setEmailFocused(false)}
                          returnKeyType="next"
                          onSubmitEditing={() => passwordRef.current?.focus()}
                        />
                      </View>
                    </View>

                    {/* Password Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="lock-closed-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Şifre</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        passwordFocused && styles.inputWrapperFocused
                      ]}>
                        <TextInput
                          ref={passwordRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Şifrenizi girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={password}
                          onChangeText={setPassword}
                          secureTextEntry={secureTextEntry}
                          onFocus={() => setPasswordFocused(true)}
                          onBlur={() => setPasswordFocused(false)}
                          returnKeyType="done"
                          onSubmitEditing={handleLogin}
                        />
                        <TouchableOpacity
                          onPress={toggleSecureEntry}
                          style={styles.eyeIcon}
                          activeOpacity={0.7}
                        >
                          <Ionicons
                            name={secureTextEntry ? "eye-outline" : "eye-off-outline"}
                            size={20}
                            color={isDark ? '#94a3b8' : '#64748b'}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>

                {/* Forgot Password */}
                <Link href="/auth/forgot-password" asChild>
                  <TouchableOpacity style={styles.forgotPasswordContainer}>
                    <ThemedText style={styles.forgotPasswordText}>Şifremi Unuttum?</ThemedText>
                  </TouchableOpacity>
                </Link>

                    {/* Login Button */}
                    <TouchableOpacity
                      style={[styles.button, isLoading && styles.buttonDisabled]}
                      onPress={handleLogin}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <View style={styles.loadingContainer}>
                          <Text style={styles.buttonText}>Giriş yapılıyor</Text>
                          <View style={styles.loadingDots}>
                            <View style={styles.loadingDot} />
                            <View style={[styles.loadingDot, styles.loadingDotMiddle]} />
                            <View style={styles.loadingDot} />
                          </View>
                        </View>
                      ) : (
                        <Text style={styles.buttonText}>Giriş Yap</Text>
                      )}
                    </TouchableOpacity>



                    {/* Sign Up Link */}
                    <View style={styles.footer}>
                      <ThemedText style={styles.footerText}>Hesabınız yok mu? </ThemedText>
                      <Link href="/auth/signup" asChild>
                        <TouchableOpacity>
                          <ThemedText style={styles.link}>Kayıt Ol</ThemedText>
                        </TouchableOpacity>
                      </Link>
                    </View>
                  </View>
                </Animated.View>
              </ScrollView>
            </View>
          </View>
            </ImageBackground>
          </KeyboardAvoidingView>
          {width > 768 && (
            <View style={styles.footerContainer}>
              <Footer />
            </View>
          )}
        </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  footerContainer: {
    width: '100%',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingBottom: Platform.OS === 'web' ? 0 : 50,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  mainContainer: {
    flexDirection: Platform.OS === 'web' ? 'row' : 'column',
    position: 'relative',
    overflow: 'hidden',
    width: '90%',
    maxWidth: 1000,
    height: Platform.OS === 'web' ? '90%' : 'auto',
    maxHeight: Platform.OS === 'web' ? 700 : undefined,
    borderRadius: 20,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
    marginVertical: Platform.OS === 'web' ? 0 : 20,
    backgroundColor: Platform.OS === 'web' ? 'transparent' : '#FFFFFF',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  decorativeCircle: {
    position: 'absolute',
    borderRadius: 500,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    zIndex: 1,
  },
  decorativeCircle1: {
    width: 300,
    height: 300,
    top: -100,
    left: -50,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  decorativeCircle2: {
    width: 200,
    height: 200,
    bottom: -50,
    right: '40%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle3: {
    width: 150,
    height: 150,
    top: '40%',
    right: -30,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  leftSection: {
    flex: Platform.OS === 'web' ? 1 : undefined,
    height: Platform.OS === 'web' ? undefined : 180,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    zIndex: 2,
    overflow: 'hidden',
    borderTopLeftRadius: 20,
    borderTopRightRadius: Platform.OS === 'web' ? 0 : 20,
    borderBottomLeftRadius: Platform.OS === 'web' ? 20 : 0,
  },
  leftGradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  leftContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
    position: 'relative',
    zIndex: 2,
  },
  rightSection: {
    flex: Platform.OS === 'web' ? 1 : undefined,
    backgroundColor: '#FFFFFF',
    padding: 0,
    zIndex: 2,
    borderTopRightRadius: Platform.OS === 'web' ? 20 : 0,
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: Platform.OS === 'web' ? 0 : 20,
    paddingBottom: Platform.OS === 'web' ? 0 : 20,
  },
  mobileRightSection: {
    borderRadius: 20,
    marginTop: 10,
    zIndex: 5,
  },
  mobileHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    backgroundColor: '#F8FAFC',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  mobileLogoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  mobileAppTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginLeft: 8,
  },
  mobileSubtitle: {
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 30 : 20,
    paddingVertical: Platform.OS === 'web' ? 50 : 30,
    minHeight: Platform.OS === 'web' ? '100%' : 'auto',
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
  },
  logoContainer: {
    alignItems: 'center',
    width: '100%',
    paddingVertical: Platform.OS === 'web' ? 0 : 20,
  },
  logoIconContainer: {
    width: Platform.OS === 'web' ? 120 : 80,
    height: Platform.OS === 'web' ? 120 : 80,
    borderRadius: Platform.OS === 'web' ? 60 : 40,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Platform.OS === 'web' ? 24 : 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 5px 10px rgba(0, 0, 0, 0.2)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 5 },
          shadowOpacity: 0.2,
          shadowRadius: 10,
        }
    ),
    elevation: 5,
  },
  appTitle: {
    fontSize: Platform.OS === 'web' ? 48 : 36,
    fontWeight: 'bold',
    marginBottom: Platform.OS === 'web' ? 8 : 4,
    letterSpacing: 1,
    color: '#FFFFFF',
    textAlign: 'center',
    ...(Platform.OS === 'web'
      ? { textShadow: '0px 2px 3px rgba(0, 0, 0, 0.2)' }
      : {
          textShadowColor: 'rgba(0, 0, 0, 0.2)',
          textShadowOffset: { width: 0, height: 2 },
          textShadowRadius: 3,
        }
    ),
  },
  appSubtitle: {
    fontSize: Platform.OS === 'web' ? 22 : 18,
    fontWeight: '500',
    letterSpacing: 0.5,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: Platform.OS === 'web' ? 16 : 8,
    opacity: 0.9,
  },
  motto: {
    fontSize: Platform.OS === 'web' ? 18 : 16,
    fontStyle: 'italic',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: Platform.OS === 'web' ? 40 : 20,
    letterSpacing: 0.5,
    opacity: 0.8,
  },
  divider: {
    width: 60,
    height: 2,
    backgroundColor: '#FFFFFF',
    marginVertical: 20,
    opacity: 0.7,
  },
  descriptionText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'web' ? 16 : 14,
    textAlign: 'center',
    lineHeight: Platform.OS === 'web' ? 24 : 20,
    maxWidth: 300,
    paddingHorizontal: 10,
    opacity: 0.8,
    display: Platform.OS === 'web' ? 'flex' : 'none', // Mobil görünümde gizle
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginBottom: 8,
  },
  formTitleUnderline: {
    width: 40,
    height: 4,
    backgroundColor: '#4A78B0',
    borderRadius: 2,
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 20,
  },
  errorText: {
    color: '#EF4444',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    marginRight: 8,
    color: '#1E3A8A',
  },
  label: {
    fontWeight: '600',
    fontSize: 16,
    color: '#1E3A8A',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#CBD5E1',
    backgroundColor: '#FFFFFF',
    zIndex: 10,
  },
  inputWrapperFocused: {
    borderColor: '#1E3A8A',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 0px 5px rgba(30, 58, 138, 0.2)' }
      : {
          shadowColor: '#1E3A8A',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.2,
          shadowRadius: 5,
        }
    ),
    elevation: 2,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'transparent',
    color: '#1E293B',
    zIndex: 20,
  },
  inputLight: {
    color: '#1E293B',
  },
  inputDark: {
    color: '#1E293B',
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    padding: 8,
    zIndex: 30,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: Colors.light.tint,
    fontWeight: '600',
    fontSize: 14,
  },
  button: {
    backgroundColor: '#4A78B0',
    height: 50,
    borderRadius: 25, // Daha yuvarlak buton
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 4px 8px rgba(74, 120, 176, 0.3)' }
      : {
          shadowColor: '#4A78B0',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }
    ),
    elevation: 5,
    paddingHorizontal: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    marginLeft: 8,
    alignItems: 'center',
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
  loadingDotMiddle: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  socialContainer: {
    marginBottom: 24,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(203, 213, 225, 0.5)',
  },
  dividerText: {
    marginHorizontal: 12,
    fontSize: 14,
    color: '#64748b',
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  socialButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(203, 213, 225, 0.3)',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: Platform.OS === 'web' ? 0 : 30,
    paddingBottom: Platform.OS === 'web' ? 0 : 20,
  },
  footerText: {
    fontSize: 16,
    color: '#64748B',
  },
  link: {
    color: '#4A78B0',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 4,
  },

});


