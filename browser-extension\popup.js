// API endpoint
const API_URL = 'http://193.35.154.97:4244';

// DOM Elements
const loginForm = document.getElementById('loginForm');
const userInfo = document.getElementById('userInfo');
const kvkkModal = document.getElementById('kvkkModal');

const showLoginKvkkLink = document.getElementById('showLoginKvkk');
const showUserKvkkLink = document.getElementById('showUserKvkk');
const closeModalBtn = document.querySelector('.close');
const acceptKvkkBtn = document.getElementById('acceptKvkk');

const loginButton = document.getElementById('loginButton');
const syncButton = document.getElementById('syncButton');
const logoutButton = document.getElementById('logoutButton');

const loginError = document.getElementById('loginError');
const loginKvkkConsent = document.getElementById('loginKvkkConsent');

const userFullName = document.getElementById('userFullName');
const userEmail = document.getElementById('userEmail');
const connectionStatus = document.getElementById('connectionStatus');

// KVKK Modal
showLoginKvkkLink.addEventListener('click', function(e) {
  e.preventDefault();
  kvkkModal.classList.remove('hidden');
});

showUserKvkkLink.addEventListener('click', function(e) {
  e.preventDefault();
  kvkkModal.classList.remove('hidden');
});

closeModalBtn.addEventListener('click', function() {
  kvkkModal.classList.add('hidden');
});

acceptKvkkBtn.addEventListener('click', function() {
  loginKvkkConsent.checked = true;
  kvkkModal.classList.add('hidden');
});

// Check if user is logged in
function checkLoginStatus() {
  chrome.storage.local.get(['token', 'user'], function(result) {
    if (result.token && result.user) {
      // User is logged in
      loginForm.classList.add('hidden');
      userInfo.classList.remove('hidden');

      // Display user info
      userFullName.textContent = result.user.fullName || 'Kullanıcı';
      userEmail.textContent = result.user.email || '';

      // Check connection status
      checkConnectionStatus();
    } else {
      // User is not logged in
      loginForm.classList.remove('hidden');
      userInfo.classList.add('hidden');
    }
  });
}

// Check connection status
function checkConnectionStatus() {
  // Tüm cookie'leri al
  chrome.cookies.getAll({}, function(allCookies) {
    // UYAP domain'indeki cookie'leri filtrele
    const uyapCookies = allCookies.filter(cookie =>
      cookie.domain.includes('uyap.gov.tr')
    );
    console.log('UYAP cookie\'ler:', uyapCookies);

    // JSESSIONID cookie'sini bul
    const jsessionCookie = uyapCookies.find(cookie =>
      cookie.name === 'JSESSIONID'
    );
    console.log('JSESSIONID cookie:', jsessionCookie);

    // Guest cookie'sini bul
    const guestCookie = uyapCookies.find(cookie =>
      cookie.name === 'guest'
    );
    console.log('Guest cookie:', guestCookie);

    // Eğer JSESSIONID cookie'si varsa ve guest cookie'si yoksa, kullanıcı giriş yapmış demektir
    if (jsessionCookie && !guestCookie) {
      // Kullanıcı giriş yapmış
      connectionStatus.textContent = 'Bağlı (UYAP Oturumu Açık)';
      connectionStatus.classList.remove('disconnected');
      console.log('UYAP oturumu açık, JSESSIONID:', jsessionCookie.value);
    } else if (guestCookie) {
      // Guest cookie varsa kullanıcı giriş yapmamış
      connectionStatus.textContent = 'Giriş Yapılmamış';
      connectionStatus.classList.add('disconnected');
      console.log('UYAP\'a giriş yapılmamış, guest cookie mevcut');
    } else {
      // UYAP cookie'leri bulunamadı
      connectionStatus.textContent = 'Bağlı Değil';
      connectionStatus.classList.add('disconnected');
      console.log('UYAP cookie\'leri bulunamadı');
    }
  });
}

// Login
loginButton.addEventListener('click', function() {
  const email = document.getElementById('email').value;
  const password = document.getElementById('password').value;
  const kvkkAccepted = loginKvkkConsent.checked;

  if (!email || !password) {
    loginError.textContent = 'Lütfen tüm alanları doldurun';
    return;
  }

  if (!kvkkAccepted) {
    loginError.textContent = 'KVKK metnini kabul etmeniz gerekmektedir';
    return;
  }

  loginError.textContent = '';
  loginButton.disabled = true;
  loginButton.textContent = 'Giriş Yapılıyor...';

  // HukApp API'sine istek gönder
  fetch(`${API_URL}/auth/user/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: email,
      password: password
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    loginButton.disabled = false;
    loginButton.textContent = 'Giriş Yap';

    // JWT token kontrolü
    if (data && data.jwt) {
      // Kullanıcı bilgilerini oluştur
      const user = {
        id: data.id,
        fullName: `${data.name} ${data.surname}`,
        email: email,
        isNewUser: data.isNewUser
      };

      // Token ve kullanıcı bilgilerini kaydet
      chrome.storage.local.set({
        token: data.jwt,
        user: user,
        userId: data.id
      }, function() {
        console.log('Login successful');
        checkLoginStatus();
      });
    } else {
      loginError.textContent = 'Giriş başarısız. Geçersiz yanıt alındı.';
    }
  })
  .catch(error => {
    loginButton.disabled = false;
    loginButton.textContent = 'Giriş Yap';

    // Hata mesajını göster
    if (error.message && error.message.includes('401')) {
      loginError.textContent = 'Geçersiz e-posta veya şifre.';
    } else {
      loginError.textContent = 'Bir hata oluştu, lütfen tekrar deneyin';
    }

    console.error('Login error:', error);
  });
});

// Senkronize Et butonu
syncButton.addEventListener('click', function() {
  // Tüm cookie'leri al
  chrome.cookies.getAll({}, function(allCookies) {
    // UYAP domain'indeki cookie'leri filtrele
    const uyapCookies = allCookies.filter(cookie =>
      cookie.domain.includes('uyap.gov.tr')
    );
    console.log('UYAP cookie\'ler (sync):', uyapCookies);

    // JSESSIONID cookie'sini bul
    const jsessionCookie = uyapCookies.find(cookie =>
      cookie.name === 'JSESSIONID'
    );
    console.log('JSESSIONID cookie (sync):', jsessionCookie);

    // Guest cookie'sini bul
    const guestCookie = uyapCookies.find(cookie =>
      cookie.name === 'guest'
    );
    console.log('Guest cookie (sync):', guestCookie);

    // Eğer JSESSIONID cookie'si yoksa veya guest cookie'si varsa, kullanıcı giriş yapmamış demektir
    if (!jsessionCookie) {
      alert('JSESSIONID cookie\'si bulunamadı. Lütfen önce UYAP sistemine giriş yapın.');
      return;
    }

    if (guestCookie) {
      alert('UYAP\'a henüz giriş yapmamışsınız. Lütfen önce UYAP sistemine giriş yapın.');
      return;
    }

    // JSESSIONID cookie'sini kullan
    const uyapCookie = jsessionCookie;

    // Kullanıcı bilgilerini al
    chrome.storage.local.get(['token', 'userId'], function(result) {
      if (!result.token || !result.userId) {
        alert('Kullanıcı bilgileri bulunamadı. Lütfen tekrar giriş yapın.');
        return;
      }

      // Senkronizasyon başladı
      syncButton.disabled = true;
      syncButton.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Senkronize Ediliyor...';
      syncButton.classList.add('syncing');

      // UYAP Avukat cookie'sini kullan

      // API'ye gönder - yeni endpoint: /api/user/sync
      fetch(`${API_URL}/api/user/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${result.token}`
        },
        body: JSON.stringify({
          jsid: uyapCookie.value
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        // Senkronizasyon tamamlandı
        syncButton.disabled = false;
        syncButton.innerHTML = '<i class="fas fa-sync-alt"></i> Senkronize Et';
        syncButton.classList.remove('syncing');

        // Başarı mesajı
        alert('UYAP Avukat oturum bilgileriniz başarıyla senkronize edildi.');
        console.log('Senkronizasyon başarılı:', data);
      })
      .catch(error => {
        // Hata durumu
        syncButton.disabled = false;
        syncButton.innerHTML = '<i class="fas fa-sync-alt"></i> Senkronize Et';
        syncButton.classList.remove('syncing');

        alert('Senkronizasyon sırasında bir hata oluştu. Lütfen tekrar deneyin.');
        console.error('Senkronizasyon hatası:', error);
      });
    });
  });
});

// Logout
logoutButton.addEventListener('click', function() {
  chrome.storage.local.remove(['token', 'user', 'userId'], function() {
    console.log('Logged out');
    checkLoginStatus();
  });
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
  checkLoginStatus();

  // Popup açıldığında bağlantı durumunu kontrol et
  setTimeout(checkConnectionStatus, 500);
});
