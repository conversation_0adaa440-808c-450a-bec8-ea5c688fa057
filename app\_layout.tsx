import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Redirect, Stack, usePathname } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import 'react-native-reanimated';
import { Platform, useColorScheme as useDeviceColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '@/utils/logger';
import { initLogger } from '@/utils/initLogger';
import tokenManager from '@/utils/tokenManager';
import FlashMessage from 'react-native-flash-message';

import { ThemeProvider } from '@/contexts/ThemeContext';
import { CasePartiesProvider } from '@/contexts/CasePartiesContext';
import authService from '@/services/authService';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Auth context for managing authentication state
export const AuthContext = React.createContext({
  isAuthenticated: false,
  login: () => {},
  logout: () => {},
  user: null,
});

export default function RootLayout() {
  // Initialize logger as early as possible
  initLogger();

  const deviceColorScheme = useDeviceColorScheme();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const pathname = usePathname();

  // Commented out token clearing on startup as requested
  // useEffect(() => {
  //   const clearTokenOnStartup = async () => {
  //     try {
  //       // Clear JWT token and user data
  //       await tokenManager.clearTokens();
  //       logger.info('JWT token cleared on app startup');
  //     } catch (error) {
  //       logger.error('Error clearing JWT token on app startup:', error);
  //     }
  //   };

  //   clearTokenOnStartup();
  // }, []);

  // Sayfa değiştiğinde sayfayı en üste kaydır
  useEffect(() => {
    if (Platform.OS === 'web') {
      window.scrollTo(0, 0);
    }
  }, [pathname]);

  // Uygulama başladığında oturum durumunu kontrol et
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Oturum durumunu kontrol et
        const isAuth = await authService.isAuthenticated();
        logger.info('Oturum durumu:', isAuth ? 'Giriş yapılmış' : 'Giriş yapılmamış');
        setIsAuthenticated(isAuth);

        if (isAuth) {
          const userData = await authService.getCurrentUser();
          setUser(userData);
        }
      } catch (error) {
        logger.error('Auth check error:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();

    // Server yeniden başlatıldığında oturum durumunu kontrol et
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        logger.info('Sayfa görünür oldu, oturum kontrol ediliyor...');
        checkAuthStatus();
      }
    };

    // Web için visibility change event'ini dinle
    if (Platform.OS === 'web') {
      document.addEventListener('visibilitychange', handleVisibilityChange);
    }

    return () => {
      if (Platform.OS === 'web') {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      }
    };
  }, []);

  // 401 hatası durumunda otomatik logout için listener
  useEffect(() => {
    const checkAuthLogout = async () => {
      try {
        // AsyncStorage'dan auth_logout flag'ini kontrol et
        const shouldLogout = await AsyncStorage.getItem('auth_logout');

        if (shouldLogout === 'true') {
          // 401 hatası nedeniyle otomatik çıkış yapılıyor
          // Flag'i temizle
          await AsyncStorage.removeItem('auth_logout');
          // Kullanıcıyı çıkış yaptır
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (error) {
        logger.error('Auth logout check error:', error);
      }
    };

    // Periyodik olarak kontrol et (her 5 saniyede bir)
    const interval = setInterval(checkAuthLogout, 5000);

    // Web için event listener
    const handleLogoutEvent = async () => {
      // Kullanıcıyı çıkış yaptır
      setIsAuthenticated(false);
      setUser(null);
    };

    if (Platform.OS === 'web') {
      window.addEventListener('auth_logout', handleLogoutEvent);
    }

    // Cleanup
    return () => {
      clearInterval(interval);
      if (Platform.OS === 'web') {
        window.removeEventListener('auth_logout', handleLogoutEvent);
      }
    };
  }, []);

  useEffect(() => {
    if (loaded && !isLoading) {
      SplashScreen.hideAsync();
    }
  }, [loaded, isLoading]);

  if (!loaded || isLoading) {
    return null;
  }

  const authContext = {
    isAuthenticated,
    user,
    login: async () => {
      setIsAuthenticated(true);
      const userData = await authService.getCurrentUser();
      setUser(userData);
    },
    logout: async () => {
      await authService.logout();
      setIsAuthenticated(false);
      setUser(null);
    },
  };

  // Mobil cihazlarda ilk açılışta splash ekranını göster
  if (Platform.OS !== 'web' && !isAuthenticated) {
    return (
      <AuthContext.Provider value={authContext}>
        <ThemeProvider>
          <CasePartiesProvider>
            <NavigationThemeProvider value={deviceColorScheme === 'dark' ? DarkTheme : DefaultTheme}>
              <Stack>
                <Stack.Screen name="splash" options={{ headerShown: false }} />
                <Stack.Screen name="auth" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
              <FlashMessage position="center" />
            </NavigationThemeProvider>
          </CasePartiesProvider>
        </ThemeProvider>
      </AuthContext.Provider>
    );
  }

  // Web'de oturum yoksa login sayfasına yönlendir
  if (Platform.OS === 'web' && !isAuthenticated) {
    return (
      <AuthContext.Provider value={authContext}>
        <ThemeProvider>
          <CasePartiesProvider>
            <NavigationThemeProvider value={deviceColorScheme === 'dark' ? DarkTheme : DefaultTheme}>
              <Stack>
                <Stack.Screen name="auth" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
              <FlashMessage position="center" />
            </NavigationThemeProvider>
          </CasePartiesProvider>
        </ThemeProvider>
      </AuthContext.Provider>
    );
  }

  // Kullanıcı giriş yapmışsa ana sayfaya yönlendir
  return (
    <AuthContext.Provider value={authContext}>
      <ThemeProvider>
        <CasePartiesProvider>
          <NavigationThemeProvider value={deviceColorScheme === 'dark' ? DarkTheme : DefaultTheme}>
            <Stack>
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="auth" options={{ headerShown: false }} />
              <Stack.Screen name="cases/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="trials/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="tasks/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="lawbot" />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="auto" />
            <FlashMessage position="center" />
          </NavigationThemeProvider>
        </CasePartiesProvider>
      </ThemeProvider>
    </AuthContext.Provider>
  );
}
