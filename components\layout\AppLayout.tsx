import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Platform, Dimensions, SafeAreaView, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { usePathname, useNavigation } from 'expo-router';

import { useColorScheme } from '@/hooks/useColorScheme';
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from '@/components/layout/Footer';

interface AppLayoutProps {
  children: React.ReactNode;
  title?: string;
  hideHeader?: boolean;
  hideFooter?: boolean;
  disableScrollView?: boolean; // Add this prop to disable ScrollView when using FlatList
}

export default function AppLayout({ children, title, hideHeader = false, hideFooter = false, disableScrollView = false }: AppLayoutProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [isWideScreen, setIsWideScreen] = useState(false);
  const currentPath = usePathname();
  const scrollViewRef = useRef<ScrollView>(null);
  const navigation = useNavigation();

  // Sayfa başlığını belirle
  const getPageTitle = () => {
    if (title) return title;

    if (currentPath.includes('/cases')) return 'Dosyalar';
    if (currentPath.includes('/clients')) return 'Müvekkiller';
    if (currentPath.includes('/tasks')) return 'Görevler';
    if (currentPath.includes('/trials')) return 'Duruşmalar';
    if (currentPath.includes('/lawbot')) return 'Lawbot';
    if (currentPath.includes('/profile')) return 'Profil';
    return 'AVAS';
  };

  // Ekran genişliğini kontrol et
  useEffect(() => {
    const checkScreenWidth = () => {
      const { width } = Dimensions.get('window');
      setIsWideScreen(Platform.OS === 'web' && width > 768);
    };

    checkScreenWidth();

    // Web için resize event listener ekle
    if (Platform.OS === 'web') {
      window.addEventListener('resize', checkScreenWidth);
      return () => window.removeEventListener('resize', checkScreenWidth);
    }
  }, []);

  // Sayfa değiştiğinde ScrollView'i en üste kaydır
  useEffect(() => {
    // Web için window.scrollTo kullan
    if (Platform.OS === 'web') {
      window.scrollTo(0, 0);
    }
    // Native için ScrollView referansını kullan
    else if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
    }
  }, [currentPath]);

  // Navigation state değiştiğinde de kontrol et
  useEffect(() => {
    const unsubscribe = navigation.addListener('state', () => {
      // Web için window.scrollTo kullan
      if (Platform.OS === 'web') {
        window.scrollTo(0, 0);
      }
      // Native için ScrollView referansını kullan
      else if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
      }
    });

    return unsubscribe;
  }, [navigation]);

  const toggleSidebar = () => {
    setIsSidebarVisible(!isSidebarVisible);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {!hideHeader && (
        <Header
          title={getPageTitle()}
          showMenuButton={isWideScreen}
          onMenuPress={toggleSidebar}
        />
      )}

      <View style={styles.contentContainer}>
        {isWideScreen && (
          <Sidebar
            isVisible={isSidebarVisible}
            onClose={() => setIsSidebarVisible(false)}
          />
        )}

        <View style={[
          styles.mainContentWrapper,
          isWideScreen && isSidebarVisible && styles.mainContentWithSidebar
        ]}>
          {disableScrollView ? (
            // When disableScrollView is true, don't wrap content in ScrollView
            // This allows FlatList to be the main scrollable component
            <View style={[styles.mainContent, { flex: 1 }]}>
              {children}
            </View>
          ) : (
            // Use ScrollView when disableScrollView is false (default)
            <ScrollView
              ref={scrollViewRef}
              style={styles.scrollView}
              contentContainerStyle={styles.scrollViewContent}
              scrollsToTop={true}
            >
              <View style={styles.mainContent}>
                <View style={styles.contentWrapper}>
                  {children}
                </View>
              </View>
            </ScrollView>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    display: 'flex',
    flexDirection: 'column',
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  mainContentWrapper: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    transition: 'margin-left 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)', // Daha yumuşak geçiş için cubic-bezier eğrisi
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  mainContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100%',
  },
  contentWrapper: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  mainContentWithSidebar: {
    marginLeft: 280, // Sidebar genişliği
  },
});
