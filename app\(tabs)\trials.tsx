import React, { useState, useEffect, useLayoutEffect, useMemo, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
  RefreshControl,
  TextInput
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, usePathname } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import trialService, { Trial } from '@/services/trialService';

import Pagination from '@/components/Pagination';
import Footer from '@/components/layout/Footer';
import BackgroundWrapper from '@/components/BackgroundWrapper';

export default function TrialsScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const pathname = usePathname(); // Get current pathname for navigation detection
  const [trials, setTrials] = useState<Trial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);


  // Filtreleme ve sıralama state'leri
  const [activeFilter, setActiveFilter] = useState('All'); // All, Upcoming, Past
  const [sortBy, setSortBy] = useState('tarihSaat');
  const [sortOrder, setSortOrder] = useState('asc'); // En yakın duruşma en üstte olsun
  const [searchText, setSearchText] = useState('');
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [isSortModalVisible, setIsSortModalVisible] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Show 10 items per page
  const flatListRef = useRef<FlatList>(null); // FlatList için ref

  // Mobil için yükleme state'i
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedTrials, setDisplayedTrials] = useState<Trial[]>([]);

  // Sayfa yüklenirken sayfayı en üste kaydır
  useLayoutEffect(() => {
    if (Platform.OS === 'web') {
      window.scrollTo(0, 0);
    }
  }, []);

  // Add custom CSS to remove focus outline and add transitions
  useEffect(() => {
    if (Platform.OS === 'web') {
      // Create a style element
      const style = document.createElement('style');
      style.textContent = `
        .search-input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
        input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
        .search-container {
          transition: all 0.3s ease !important;
        }
        .search-container:focus-within {
          border-color: ${Colors.light.tint} !important;
          box-shadow: 0 0 0 1px ${Colors.light.tint} !important;
        }
      `;
      // Add it to the document head
      document.head.appendChild(style);

      // Clean up when component unmounts
      return () => {
        document.head.removeChild(style);
      };
    }
  }, []);



  // Duruşmaları getir
  const fetchTrials = async () => {
    try {
      setIsLoading(true);

      // API'den duruşmaları getir
      const data = await trialService.getUserTrials();
      console.log('API\'den gelen duruşmalar:', data);

      // Gelen veri bir dizi değilse, dizi içine al
      const trialsArray = Array.isArray(data) ? data : [data];

      setTrials(trialsArray);
    } catch (error) {
      console.error('Duruşmalar alınırken hata oluştu:', error);
      // Hata durumunda boş dizi göster
      setTrials([]);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  // Track initial mount and reset state
  const [initialMount, setInitialMount] = useState(true);
  const [needsReset, setNeedsReset] = useState(false);

  // Check if reset is needed when navigating from other tabs
  useEffect(() => {
    const checkNeedsReset = async () => {
      if (Platform.OS === 'web') {
        // For web, check localStorage
        const needsResetFlag = localStorage.getItem('trialsNeedsReset');
        if (needsResetFlag === 'true') {
          console.log('Duruşmalar page needs reset');
          setNeedsReset(true);
          // Clear the flag
          localStorage.removeItem('trialsNeedsReset');
        }
      } else {
        // For mobile, check AsyncStorage
        try {
          const needsResetFlag = await AsyncStorage.getItem('trialsNeedsReset');
          if (needsResetFlag === 'true') {
            console.log('Mobile: Duruşmalar page needs reset');
            setNeedsReset(true);
            // Clear the flag
            await AsyncStorage.removeItem('trialsNeedsReset');
          }
        } catch (error) {
          console.error('Error checking reset state:', error);
        }
      }
    };

    checkNeedsReset();
  }, []);

  // Always reset on first mount, but allow pagination to work
  useEffect(() => {
    // This effect runs when component mounts or when needsReset changes
    if (initialMount || needsReset) {
      console.log('Duruşmalar page mounted or needs reset - resetting to default state');

      // For web: Check if we're coming from pagination (URL has query parameters)
      const isPaginationNavigation = Platform.OS === 'web' && window.location.href.includes('?');

      // For mobile: Always reset
      // For web: Reset if not pagination navigation or if we need to reset
      if (Platform.OS !== 'web' || !isPaginationNavigation || needsReset) {
        // Reset to default values
        setCurrentPage(1);
        setSearchText('');
        setActiveFilter('All');
        setSortBy('tarihSaat');
        setSortOrder('asc');

        // Clear any stored pagination state in web
        if (Platform.OS === 'web') {
          localStorage.removeItem('trialsCurrentPage');
          localStorage.removeItem('trialsSearchText');
          localStorage.removeItem('trialsActiveFilter');
          localStorage.removeItem('trialsSortBy');
          localStorage.removeItem('trialsSortOrder');

          // Clear the needs reset flag
          setNeedsReset(false);
        }

        // Clear any stored state in mobile
        if (Platform.OS !== 'web') {
          const clearMobileState = async () => {
            try {
              await AsyncStorage.removeItem('trialsCurrentPage');
              await AsyncStorage.removeItem('trialsSearchText');
              await AsyncStorage.removeItem('trialsActiveFilter');
              await AsyncStorage.removeItem('trialsSortBy');
              await AsyncStorage.removeItem('trialsSortOrder');
              await AsyncStorage.removeItem('durusmalarNeedsReset');
              await AsyncStorage.removeItem('lastVisitedPath');
            } catch (error) {
              console.error('Error clearing mobile state:', error);
            }
          };
          clearMobileState();
        }
      } else {
        // We're navigating with pagination on web, restore state
        if (Platform.OS === 'web') {
          const storedPage = localStorage.getItem('trialsCurrentPage');
          const storedSearchText = localStorage.getItem('trialsSearchText');
          const storedActiveFilter = localStorage.getItem('trialsActiveFilter');
          const storedSortBy = localStorage.getItem('trialsSortBy');
          const storedSortOrder = localStorage.getItem('trialsSortOrder');

          if (storedPage) setCurrentPage(parseInt(storedPage, 10));
          if (storedSearchText) setSearchText(storedSearchText);
          if (storedActiveFilter) setActiveFilter(storedActiveFilter as 'All' | 'Upcoming' | 'Past');
          if (storedSortBy) setSortBy(storedSortBy);
          if (storedSortOrder) setSortOrder(storedSortOrder as 'asc' | 'desc');
        }
      }

      // Mark initial mount as complete only if this was the initial mount
      if (initialMount) {
        setInitialMount(false);
      }
    }
  }, [initialMount, needsReset]); // Include needsReset in dependencies

  // Sayfa yüklendiğinde duruşmaları getir
  useEffect(() => {
    fetchTrials();
  }, []);

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    fetchTrials();
  };

  // Not: Tarih formatı fonksiyonu kaldırıldı

  // Saat formatını düzenle
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);

      // Saat ve dakikayı al
      const saat = date.getHours().toString().padStart(2, '0');
      const dakika = date.getMinutes().toString().padStart(2, '0');

      return `${saat}:${dakika}`;
    } catch (error) {
      return '';
    }
  };



  // Duruşma kartı - Dosyalar sayfasındaki gibi kompakt kart yapısı
  const renderTrialCard = ({ item }: { item: Trial }) => {
    const trialTime = formatTime(item.tarihSaat);
    const trialDate = new Date(item.tarihSaat);
    const trialTypeColor = getTrialTypeColor(item.islemTuruAciklama || '');

    // Tarih formatı: 15 Oca 2023, 14:30
    const formattedDate = `${trialDate.getDate()} ${trialDate.toLocaleString('tr-TR', { month: 'short' })} ${trialDate.getFullYear()}, ${trialTime}`;

    return (
      <BlurView
        intensity={isDark ? 40 : 60}
        tint={isDark ? 'dark' : 'light'}
        style={[
          styles.trialCard,
          isDark && styles.trialCardDark,
          { borderLeftWidth: 4, borderLeftColor: trialTypeColor }
        ]}
      >
        {/* Kart içeriği - Tek satırda bilgiler */}
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* Sol taraf - Dosya bilgileri */}
          <View style={{ flex: 1 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
              <View
                style={{
                  backgroundColor: trialTypeColor,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                  borderRadius: 4,
                  marginRight: 8
                }}
              >
                <ThemedText style={{ color: 'white', fontSize: 12, fontWeight: '600' }}>
                  {item.islemTuruAciklama}
                </ThemedText>
              </View>
              <ThemedText style={{ fontSize: 13, opacity: 0.7 }}>
                {formattedDate}
              </ThemedText>
            </View>

            <ThemedText type="defaultSemiBold" style={{ fontSize: 15, marginBottom: 2 }}>
              Dosya: {item.dosyaNo}
            </ThemedText>

            <ThemedText style={{ fontSize: 14, opacity: 0.8 }}>
              {item.yerelBirimAd}
            </ThemedText>

            {item.islemSonucuAciklama && (
              <ThemedText style={{ fontSize: 13, opacity: 0.7, marginTop: 2 }}>
                Sonuç: {item.islemSonucuAciklama}
              </ThemedText>
            )}
          </View>

          {/* Sağ taraf - Detay butonu */}
          <TouchableOpacity
            style={{
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              paddingHorizontal: 12,
              paddingVertical: 8,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}
            onPress={() => {
              // Web için localStorage'a önceki sayfayı kaydet
              if (Platform.OS === 'web') {
                localStorage.setItem('previousPath', window.location.pathname);

                // Duruşma verilerini localStorage'a kaydet
                localStorage.setItem('selectedTrialData', JSON.stringify(item));
              } else {
                // Mobil için AsyncStorage'a kaydet
                AsyncStorage.setItem('selectedTrialData', JSON.stringify(item));
              }

              // Duruşma detay sayfasına yönlendir
              router.push(`/trials/${item.kayitId}?from=trials`);
            }}
          >
            <ThemedText style={{ fontSize: 14, fontWeight: '500', color: Colors.light.tint, marginRight: 4 }}>
              Detaylar
            </ThemedText>
            <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
          </TouchableOpacity>
        </View>
      </BlurView>
    );
  };

  // Duruşma türüne göre renk belirle
  const getTrialTypeColor = (type: string): string => {
    if (!type) return '#6B7280'; // Gray (default)

    const typeLower = type.toLowerCase();

    if (typeLower.includes('duruşma')) {
      return '#3B82F6'; // Blue
    } else if (typeLower.includes('keşif')) {
      return '#10B981'; // Green
    } else if (typeLower.includes('tensip')) {
      return '#8B5CF6'; // Purple
    } else if (typeLower.includes('ön inceleme')) {
      return '#F59E0B'; // Amber
    } else if (typeLower.includes('tahkikat')) {
      return '#EC4899'; // Pink
    } else if (typeLower.includes('sözlü')) {
      return '#14B8A6'; // Teal
    } else if (typeLower.includes('karar')) {
      return '#EF4444'; // Red
    } else {
      return '#6B7280'; // Gray
    }
  };

  // Sıralama fonksiyonu
  const sortTrials = (trials: Trial[]) => {
    if (!Array.isArray(trials) || trials.length === 0) {
      return [];
    }

    return [...trials].sort((a, b) => {
      if (!a || !b) return 0;

      let valueA: string | number | Date;
      let valueB: string | number | Date;

      // Sıralama alanına göre değerleri al
      switch (sortBy) {
        case 'tarihSaat':
          valueA = new Date(a.tarihSaat || '');
          valueB = new Date(b.tarihSaat || '');
          break;
        case 'dosyaNo':
          valueA = String(a.dosyaNo || '');
          valueB = String(b.dosyaNo || '');
          break;
        case 'yerelBirimAd':
          valueA = String(a.yerelBirimAd || '').toLowerCase();
          valueB = String(b.yerelBirimAd || '').toLowerCase();
          break;
        case 'islemTuruAciklama':
          valueA = String(a.islemTuruAciklama || '').toLowerCase();
          valueB = String(b.islemTuruAciklama || '').toLowerCase();
          break;
        default:
          valueA = new Date(a.tarihSaat || '');
          valueB = new Date(b.tarihSaat || '');
      }

      // Sıralama yönüne göre karşılaştır
      if (sortBy === 'tarihSaat') {
        // Tarih değerleri için
        return sortOrder === 'asc'
          ? (valueA as Date).getTime() - (valueB as Date).getTime()
          : (valueB as Date).getTime() - (valueA as Date).getTime();
      } else {
        // Metin değerleri için
        if (sortOrder === 'asc') {
          return String(valueA).localeCompare(String(valueB));
        } else {
          return String(valueB).localeCompare(String(valueA));
        }
      }
    });
  };

  // Sıralama işleyicisi
  const handleSort = (newSortBy: string) => {
    // Aynı alana tıklandığında sıralama yönünü değiştir
    if (newSortBy === sortBy) {
      const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
      setSortOrder(newSortOrder);

      if (Platform.OS === 'web') {
        localStorage.setItem('trialsSortOrder', newSortOrder);
        localStorage.setItem('trialsCurrentPage', '1');
        // Add a query parameter to indicate this is a sort navigation
        window.location.href = window.location.pathname + '?sort=' + sortBy + '&order=' + newSortOrder;
      } else {
        // For mobile, just update the state
        setCurrentPage(1);
      }
    } else {
      // Farklı bir alana tıklandığında, o alanı seç ve artan sıralama yap
      setSortBy(newSortBy);
      setSortOrder('asc');

      if (Platform.OS === 'web') {
        localStorage.setItem('trialsSortBy', newSortBy);
        localStorage.setItem('trialsSortOrder', 'asc');
        localStorage.setItem('trialsCurrentPage', '1');
        // Add a query parameter to indicate this is a sort navigation
        window.location.href = window.location.pathname + '?sort=' + newSortBy + '&order=asc';
      } else {
        // For mobile, just update the state
        setCurrentPage(1);
      }
    }

    setIsSortModalVisible(false);
  };

  // Arama ve filtreleme fonksiyonu - useMemo ile optimize edildi
  const filteredTrials = useMemo(() => {
    let filtered = trials;

    // Durum filtreleme
    if (activeFilter !== 'All') {
      const now = new Date();

      if (activeFilter === 'Upcoming') {
        // Gelecekteki duruşmaları filtrele
        filtered = filtered.filter(item => {
          const trialDate = new Date(item.tarihSaat);
          return trialDate >= now;
        });
      } else if (activeFilter === 'Past') {
        // Geçmişteki duruşmaları filtrele
        filtered = filtered.filter(item => {
          const trialDate = new Date(item.tarihSaat);
          return trialDate < now;
        });
      }
    }

    // Metin araması - case-insensitive
    if (searchText.trim() !== '') {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(item =>
        // Dosya No
        (item.dosyaNo?.toLowerCase() || '').includes(searchLower) ||
        // Mahkeme Adı
        (item.yerelBirimAd?.toLowerCase() || '').includes(searchLower) ||
        // İşlem Türü
        (item.islemTuruAciklama?.toLowerCase() || '').includes(searchLower) ||
        // İşlem Sonucu
        (item.islemSonucuAciklama?.toLowerCase() || '').includes(searchLower)
      );
    }

    // Sıralama
    return sortTrials(filtered);
  }, [trials, activeFilter, searchText, sortBy, sortOrder]);

  // Sayfa değiştiğinde sayfayı en üste kaydır
  const scrollToTop = useCallback(() => {
    if (Platform.OS === 'web') {
      // Force immediate scroll to top without animation
      window.scrollTo(0, 0);

      // For safety, also try to scroll the container element if it exists
      const container = document.getElementById('trials-container');
      if (container) {
        container.scrollTop = 0;
      }

      // Also try to scroll the body and html elements
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
    }
  }, []);

  // Sayfa değiştiğinde sayfayı en üste kaydır
  useEffect(() => {
    scrollToTop();
  }, [currentPage, scrollToTop]);

  // Calculate total pages for pagination
  const totalPages = Math.ceil(filteredTrials.length / itemsPerPage);

  // Define sections for FlatList
  const sections = [
    { id: 'content', type: 'content' },
    { id: 'pagination', type: 'pagination' },
    { id: 'footer', type: 'footer' }
  ];

  // Filtrelenmiş duruşmalar değiştiğinde mobil için displayedTrials'i güncelle
  useEffect(() => {
    if (Platform.OS !== 'web') {
      // Mobil için ilk sayfayı yükle
      setDisplayedTrials(filteredTrials.slice(0, itemsPerPage));
    }
  }, [filteredTrials, itemsPerPage]); // Sıralama değiştiğinde de güncellenmesi için tüm filteredTrials'i kullan

  // Sayfalama için geçerli sayfadaki duruşmalar
  const currentTrials = Platform.OS === 'web'
    ? filteredTrials.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : displayedTrials;

  // Mobil için daha fazla yükle
  const loadMoreItems = () => {
    if (isLoadingMore || displayedTrials.length >= filteredTrials.length) return;

    setIsLoadingMore(true);

    // Simule edilmiş yükleme gecikmesi (gerçek uygulamada API çağrısı olabilir)
    setTimeout(() => {
      // Sıralanmış filteredTrials'ten sonraki sayfayı al
      const nextItems = filteredTrials.slice(
        displayedTrials.length,
        displayedTrials.length + itemsPerPage
      );

      setDisplayedTrials([...displayedTrials, ...nextItems]);
      setIsLoadingMore(false);

      // Mobil cihazlarda da sayfayı en üste kaydır
      if (Platform.OS !== 'web' && displayedTrials.length === 0) {
        // FlatList referansı varsa en üste kaydır
        if (flatListRef.current) {
          flatListRef.current.scrollToOffset({ offset: 0, animated: true });
        }
      }
    }, 300); // Daha hızlı güncelleme için süreyi azalttık
  };

  return (
    <BackgroundWrapper style={styles.container}>
        {/* Main FlatList as the scrollable component */}
        <FlatList
          ref={flatListRef}
          data={sections}
          keyExtractor={item => item.id}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => {
            // Content section
            if (item.type === 'content') {
              return (
                <View>
                  {/* Ultra Compact Filter Bar */}
                  {Platform.OS === 'web' && (
                    <View style={styles.ultraCompactFilterBar}>
                      {/* Search Input */}
                      <View style={[
                        styles.searchContainer,
                        isDark && styles.searchInputContainerDark,
                        Platform.OS === 'web' && { className: 'search-container' }
                      ]}>
                        <Ionicons name="search" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                        <TextInput
                          style={[
                            styles.searchInput,
                            isDark && styles.searchInputDark,
                            Platform.OS === 'web' && { className: 'search-input' }
                          ]}
                          placeholder="Duruşma ara..."
                          placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
                          value={searchText}
                          onChangeText={setSearchText}
                        />
                        {searchText.length > 0 && (
                          <TouchableOpacity
                            style={styles.clearSearchButton}
                            onPress={() => setSearchText('')}
                          >
                            <Ionicons name="close-circle" size={18} color={isDark ? '#9ca3af' : '#64748b'} />
                          </TouchableOpacity>
                        )}
                      </View>

                      {/* Filter Controls */}
                      <View style={styles.filterControls}>
                        {/* Filter Button */}
                        <TouchableOpacity
                          style={styles.miniFilterButton}
                          onPress={() => setIsFilterModalVisible(true)}
                        >
                          <Ionicons
                            name="filter"
                            size={20}
                            color={activeFilter !== 'All' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                          />
                        </TouchableOpacity>

                        {/* Sort Button */}
                        <TouchableOpacity
                          style={styles.miniFilterButton}
                          onPress={() => setIsSortModalVisible(true)}
                        >
                          <Ionicons
                            name="swap-vertical"
                            size={20}
                            color={isDark ? '#9ca3af' : '#64748b'}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}

                  <View style={styles.contentContainer}>
                    {isLoading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
                        <ThemedText style={styles.loadingText}>Duruşmalar yükleniyor...</ThemedText>
                      </View>
                    ) : filteredTrials.length === 0 ? (
                      <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.emptyContainer}>
                        <Ionicons name="calendar-outline" size={64} color={Colors[colorScheme ?? 'light'].tint} />
                        <ThemedText style={styles.emptyText}>Duruşma bulunamadı</ThemedText>
                        <ThemedText style={styles.emptySubText}>
                          {searchText ?
                            `"${searchText}" aramasına uygun duruşma bulunamadı.` :
                            activeFilter !== 'All' ?
                              `${activeFilter === 'Upcoming' ? 'Yaklaşan' : 'Geçmiş'} duruşma bulunamadı.` :
                              'Yaklaşan duruşmanız bulunmamaktadır.'
                          }
                        </ThemedText>
                        <TouchableOpacity style={styles.refreshButton} onPress={fetchTrials}>
                          <ThemedText style={styles.refreshButtonText}>Yenile</ThemedText>
                          <Ionicons name="refresh" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                        </TouchableOpacity>
                      </BlurView>
                    ) : (
                      <View style={styles.cardsContainer}>
                        <FlatList
                          data={Platform.OS === 'web' ? currentTrials : displayedTrials}
                          renderItem={renderTrialCard}
                          keyExtractor={item => item.kayitId.toString()}
                          contentContainerStyle={[
                            Platform.OS !== 'web' && { paddingBottom: 80 }
                          ]}
                          showsVerticalScrollIndicator={false}
                          scrollEnabled={false} // Disable scrolling to prevent nested scrolling
                          onEndReached={Platform.OS !== 'web' ? loadMoreItems : undefined}
                          onEndReachedThreshold={0.3}
                          ListFooterComponent={Platform.OS !== 'web' && isLoadingMore ? (
                            <View style={styles.loadMoreContainer}>
                              <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                              <ThemedText style={styles.loadMoreText}>Daha fazla yükleniyor...</ThemedText>
                            </View>
                          ) : null}
                        />
                      </View>
                    )}
                  </View>
                </View>
              );
            }

            // Pagination section - Web only
            if (item.type === 'pagination' && Platform.OS === 'web' && totalPages > 1 && !isLoading && filteredTrials.length > 0) {
              return (
                <View style={styles.contentContainer}>
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={filteredTrials.length}
                    onPageChange={(page) => {
                      if (Platform.OS === 'web') {
                        // Save all filter states for pagination
                        localStorage.setItem('trialsCurrentPage', String(page));
                        localStorage.setItem('trialsSearchText', searchText);
                        localStorage.setItem('trialsActiveFilter', activeFilter);
                        localStorage.setItem('trialsSortBy', sortBy);
                        localStorage.setItem('trialsSortOrder', sortOrder);

                        // Add a query parameter to indicate this is a pagination navigation
                        // This prevents the page from resetting when using pagination
                        window.location.href = window.location.pathname + '?page=' + page;
                      } else {
                        // For non-web platforms, just update the state
                        setCurrentPage(page);
                      }
                    }}
                    itemsPerPage={itemsPerPage}
                  />
                </View>
              );
            }

            // Footer section - Web only
            if (item.type === 'footer' && Platform.OS === 'web' && !isLoading) {
              return (
                <View style={styles.fullWidthContainer}>
                  <Footer />
                </View>
              );
            }

            return null;
          }}
        />

        {/* Status Filter Modal */}
        {isFilterModalVisible && (
          <View style={styles.modalOverlay}>
            <TouchableOpacity
              style={styles.modalBackdrop}
              activeOpacity={1}
              onPress={() => setIsFilterModalVisible(false)}
            />
            <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>Duruşma Durumu</ThemedText>
                <ThemedText style={styles.modalSubtitle}>
                  Seçili: {activeFilter === 'All' ? 'Tümü' :
                           activeFilter === 'Upcoming' ? 'Yaklaşan' : 'Geçmiş'}
                </ThemedText>
                <TouchableOpacity onPress={() => setIsFilterModalVisible(false)} style={styles.modalCloseButton}>
                  <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              </View>

              <ScrollView style={{padding: 16, maxHeight: Platform.OS === 'web' ? '80%' : '70%'}}>
                <TouchableOpacity
                  style={[styles.modalOption, activeFilter === 'All' && styles.activeModalOption]}
                  onPress={() => {
                    // Only reload if changing the filter
                    if (activeFilter !== 'All') {
                      setActiveFilter('All');
                      setIsFilterModalVisible(false);

                      if (Platform.OS === 'web') {
                        // Save the new filter state
                        localStorage.setItem('trialsActiveFilter', 'All');
                        // Reset to page 1
                        localStorage.setItem('trialsCurrentPage', '1');
                        // Add a query parameter to indicate this is a filter navigation
                        window.location.href = window.location.pathname + '?filter=All';
                      }
                    } else {
                      setIsFilterModalVisible(false);
                    }
                  }}
                >
                  <Ionicons
                    name="list"
                    size={20}
                    color={activeFilter === 'All' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      activeFilter === 'All' && styles.activeModalOptionText
                    ]}>
                      Tümü
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Tüm duruşmaları göster
                    </ThemedText>
                  </View>
                  {activeFilter === 'All' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalOption, activeFilter === 'Upcoming' && styles.activeModalOption]}
                  onPress={() => {
                    // Only reload if changing the filter
                    if (activeFilter !== 'Upcoming') {
                      setActiveFilter('Upcoming');
                      setIsFilterModalVisible(false);

                      if (Platform.OS === 'web') {
                        // Save the new filter state
                        localStorage.setItem('trialsActiveFilter', 'Upcoming');
                        // Reset to page 1
                        localStorage.setItem('trialsCurrentPage', '1');
                        // Add a query parameter to indicate this is a filter navigation
                        window.location.href = window.location.pathname + '?filter=Upcoming';
                      }
                    } else {
                      setIsFilterModalVisible(false);
                    }
                  }}
                >
                  <Ionicons
                    name="time"
                    size={20}
                    color={activeFilter === 'Upcoming' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      activeFilter === 'Upcoming' && styles.activeModalOptionText
                    ]}>
                      Yaklaşan
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Sadece yaklaşan duruşmaları göster
                    </ThemedText>
                  </View>
                  {activeFilter === 'Upcoming' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalOption, activeFilter === 'Past' && styles.activeModalOption]}
                  onPress={() => {
                    // Only reload if changing the filter
                    if (activeFilter !== 'Past') {
                      setActiveFilter('Past');
                      setIsFilterModalVisible(false);

                      if (Platform.OS === 'web') {
                        // Save the new filter state
                        localStorage.setItem('trialsActiveFilter', 'Past');
                        // Reset to page 1
                        localStorage.setItem('trialsCurrentPage', '1');
                        // Add a query parameter to indicate this is a filter navigation
                        window.location.href = window.location.pathname + '?filter=Past';
                      }
                    } else {
                      setIsFilterModalVisible(false);
                    }
                  }}
                >
                  <Ionicons
                    name="calendar"
                    size={20}
                    color={activeFilter === 'Past' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      activeFilter === 'Past' && styles.activeModalOptionText
                    ]}>
                      Geçmiş
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Sadece geçmiş duruşmaları göster
                    </ThemedText>
                  </View>
                  {activeFilter === 'Past' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>
              </ScrollView>
            </View>
          </View>
        )}

        {/* Sort Modal */}
        {isSortModalVisible && (
          <View style={styles.modalOverlay}>
            <TouchableOpacity
              style={styles.modalBackdrop}
              activeOpacity={1}
              onPress={() => setIsSortModalVisible(false)}
            />
            <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>Sıralama</ThemedText>
                <ThemedText style={styles.modalSubtitle}>
                  Seçili: {sortBy === 'tarihSaat' ? 'Tarih' :
                           sortBy === 'dosyaNo' ? 'Dosya No' :
                           sortBy === 'yerelBirimAd' ? 'Mahkeme' : 'İşlem Türü'}
                  {' '}{sortOrder === 'asc' ? '(Artan)' : '(Azalan)'}
                </ThemedText>
                <TouchableOpacity onPress={() => setIsSortModalVisible(false)} style={styles.modalCloseButton}>
                  <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              </View>

              <View style={{padding: 12, paddingTop: 8}}>
                <ThemedText style={{
                  fontSize: 14,
                  fontWeight: '600',
                  marginBottom: 8,
                }}>Sıralama Kriteri</ThemedText>

                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'tarihSaat' && styles.activeModalOption]}
                  onPress={() => handleSort('tarihSaat')}
                >
                  <Ionicons
                    name="calendar-outline"
                    size={20}
                    color={sortBy === 'tarihSaat' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'tarihSaat' && styles.activeModalOptionText
                    ]}>
                      Tarih {sortBy === 'tarihSaat' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Duruşmaları tarihe göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'tarihSaat' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'dosyaNo' && styles.activeModalOption]}
                  onPress={() => handleSort('dosyaNo')}
                >
                  <Ionicons
                    name="document-text-outline"
                    size={20}
                    color={sortBy === 'dosyaNo' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'dosyaNo' && styles.activeModalOptionText
                    ]}>
                      Dosya No {sortBy === 'dosyaNo' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Duruşmaları dosya numarasına göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'dosyaNo' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'yerelBirimAd' && styles.activeModalOption]}
                  onPress={() => handleSort('yerelBirimAd')}
                >
                  <Ionicons
                    name="business-outline"
                    size={20}
                    color={sortBy === 'yerelBirimAd' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'yerelBirimAd' && styles.activeModalOptionText
                    ]}>
                      Mahkeme {sortBy === 'yerelBirimAd' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Duruşmaları mahkeme adına göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'yerelBirimAd' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'islemTuruAciklama' && styles.activeModalOption]}
                  onPress={() => handleSort('islemTuruAciklama')}
                >
                  <Ionicons
                    name="information-circle-outline"
                    size={20}
                    color={sortBy === 'islemTuruAciklama' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'islemTuruAciklama' && styles.activeModalOptionText
                    ]}>
                      İşlem Türü {sortBy === 'islemTuruAciklama' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Duruşmaları işlem türüne göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'islemTuruAciklama' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <View style={styles.modalDivider} />

                <ThemedText style={{
                  fontSize: 14,
                  fontWeight: '600',
                  marginBottom: 8,
                  marginTop: 4,
                }}>Sıralama Yönü</ThemedText>

                <View style={styles.sortDirectionContainer}>
                  <TouchableOpacity
                    style={[styles.sortDirectionButton, sortOrder === 'asc' && styles.activeSortDirectionButton]}
                    onPress={() => {
                      // Use the same logic as handleSort for consistency
                      const newSortOrder = 'asc';
                      setSortOrder(newSortOrder);

                      if (Platform.OS === 'web') {
                        localStorage.setItem('trialsSortOrder', newSortOrder);
                        localStorage.setItem('trialsCurrentPage', '1');
                        // Add a query parameter to indicate this is a sort navigation
                        window.location.href = window.location.pathname + '?sort=' + sortBy + '&order=' + newSortOrder;
                      } else {
                        // For mobile, just update the state
                        setCurrentPage(1);
                      }
                      setIsSortModalVisible(false);
                    }}
                  >
                    <Ionicons
                      name="arrow-up"
                      size={20}
                      color={sortOrder === 'asc' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                    />
                    <ThemedText style={[
                      styles.sortDirectionText,
                      sortOrder === 'asc' && styles.activeSortDirectionText
                    ]}>
                      Artan
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.sortDirectionButton, sortOrder === 'desc' && styles.activeSortDirectionButton]}
                    onPress={() => {
                      // Use the same logic as handleSort for consistency
                      const newSortOrder = 'desc';
                      setSortOrder(newSortOrder);

                      if (Platform.OS === 'web') {
                        localStorage.setItem('trialsSortOrder', newSortOrder);
                        localStorage.setItem('trialsCurrentPage', '1');
                        // Add a query parameter to indicate this is a sort navigation
                        window.location.href = window.location.pathname + '?sort=' + sortBy + '&order=' + newSortOrder;
                      } else {
                        // For mobile, just update the state
                        setCurrentPage(1);
                      }
                      setIsSortModalVisible(false);
                    }}
                  >
                    <Ionicons
                      name="arrow-down"
                      size={20}
                      color={sortOrder === 'desc' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                    />
                    <ThemedText style={[
                      styles.sortDirectionText,
                      sortOrder === 'desc' && styles.activeSortDirectionText
                    ]}>
                      Azalan
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        )}


    </BackgroundWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 10, // Reduced further since we removed the header
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  // Ultra Compact Filter Bar
  ultraCompactFilterBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 6,
    marginBottom: 2,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
    ...(Platform.OS !== 'web' && {
      paddingHorizontal: 12, // Smaller padding for mobile
      paddingBottom: 4, // Smaller padding for mobile
    }),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 14,
    height: 40,
    flex: 1,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
    // Web-specific styles added via custom CSS in useEffect
  },
  searchInputContainerDark: {
    backgroundColor: 'rgba(30, 41, 59, 0.9)',
    borderColor: 'rgba(75, 85, 99, 0.5)',
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.2)', // CSS shadow for web
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 15,
    color: '#1F2937',
    marginLeft: 10,
    paddingVertical: 0,
    fontWeight: '500',
    letterSpacing: 0.2, // Slightly improved letter spacing
    // outline is not supported in React Native Web
  },
  searchInputDark: {
    color: '#F3F4F6',
  },
  clearSearchButton: {
    padding: 6,
    marginLeft: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  filterControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  miniFilterButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
  },
  contentContainer: {
    flex: 1,
    padding: 16,
    paddingTop: 16,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
  },
  cardsContainer: {
    width: '100%',
    marginBottom: 16,
  },
  fullWidthContainer: {
    width: '100%',
    maxWidth: '100%',
    marginLeft: 0,
    marginRight: 0,
  },
  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: Platform.OS === 'web' ? '90%' : '85%',
    maxWidth: 500,
    maxHeight: Platform.OS === 'web' ? '80%' : '70%',
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  modalContainerDark: {
    backgroundColor: '#1f2937',
  },
  modalHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    flexDirection: 'column',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  modalSubtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
  modalCloseButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 4,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  activeModalOption: {
    backgroundColor: `${Colors.light.tint}10`,
  },
  modalOptionIcon: {
    marginRight: 12,
  },
  modalOptionTextContainer: {
    flex: 1,
  },
  modalOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  activeModalOptionText: {
    color: Colors.light.tint,
  },
  modalOptionDescription: {
    fontSize: 13,
    opacity: 0.7,
    marginTop: 2,
  },
  modalDivider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginVertical: 12,
  },
  sortDirectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  sortDirectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    flex: 1,
    marginHorizontal: 4,
  },
  activeSortDirectionButton: {
    backgroundColor: Colors.light.tint,
  },
  sortDirectionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  activeSortDirectionText: {
    color: 'white',
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  loadMoreText: {
    marginLeft: 8,
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  emptyText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.7,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 24,
  },
  refreshButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
    color: Colors.light.tint,
  },
  listContainer: {
    paddingBottom: 24,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  trialCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: 'white',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
    // Dosyalar sayfasındaki gibi daha kompakt kart yapısı
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  trialCardDark: {
    backgroundColor: '#1E293B',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  trialHeader: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  dateContainer: {
    width: 50,
    height: 50,
    backgroundColor: Colors.light.tint,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  dateText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  monthText: {
    color: 'white',
    fontSize: 12,
  },
  trialInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  trialTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  trialSubtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
    fontStyle: 'italic',
  },
  trialDetails: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    marginLeft: 8,
  },
  trialFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  partiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  davacıTag: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
  },
  davalıTag: {
    backgroundColor: 'rgba(239, 68, 68, 0.2)',
  },
  cocukTag: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
  },
  vekilTag: {
    backgroundColor: 'rgba(139, 92, 246, 0.2)',
  },
  defaultTag: {
    backgroundColor: 'rgba(107, 114, 128, 0.2)',
  },
  tagText: {
    fontSize: 12,
    fontWeight: '600',
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
    color: Colors.light.tint,
  },
});
