import React, { useState, useContext, useRef } from 'react';
import { StyleSheet, TextInput, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView, View, Text, Dimensions, Animated, SafeAreaView, Alert, ImageBackground, Modal } from 'react-native';
import { Link, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import authService from '@/services/authService';
import { AuthContext } from '@/app/_layout';
import Footer from '@/components/layout/Footer';

export default function SignupScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { login: contextLogin } = useContext(AuthContext);

  // Form alanları
  const [name, setName] = useState('');
  const [surname, setSurname] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [identityNumber, setIdentityNumber] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [mobilePhone, setMobilePhone] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  // Field validation states
  const [nameError, setNameError] = useState('');
  const [surnameError, setSurnameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [identityNumberError, setIdentityNumberError] = useState('');
  const [birthDateError, setBirthDateError] = useState('');
  const [mobilePhoneError, setMobilePhoneError] = useState('');
  const [termsError, setTermsError] = useState('');

  // Password strength
  const [passwordStrength, setPasswordStrength] = useState(0); // 0-4 scale

  // Field touched states (for validation on blur)
  const [nameTouched, setNameTouched] = useState(false);
  const [surnameTouched, setSurnameTouched] = useState(false);
  const [emailTouched, setEmailTouched] = useState(false);
  const [passwordTouched, setPasswordTouched] = useState(false);
  const [confirmPasswordTouched, setConfirmPasswordTouched] = useState(false);
  const [identityNumberTouched, setIdentityNumberTouched] = useState(false);
  const [birthDateTouched, setBirthDateTouched] = useState(false);
  const [mobilePhoneTouched, setMobilePhoneTouched] = useState(false);

  // State for input focus
  const [nameFocused, setNameFocused] = useState(false);
  const [surnameFocused, setSurnameFocused] = useState(false);
  const [emailFocused, setEmailFocused] = useState(false);
  const [identityNumberFocused, setIdentityNumberFocused] = useState(false);
  const [birthDateFocused, setBirthDateFocused] = useState(false);
  const [mobilePhoneFocused, setMobilePhoneFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [confirmPasswordFocused, setConfirmPasswordFocused] = useState(false);

  // State for password visibility
  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [confirmSecureTextEntry, setConfirmSecureTextEntry] = useState(true);

  // State for terms and privacy policy modals
  const [termsModalVisible, setTermsModalVisible] = useState(false);
  const [privacyModalVisible, setPrivacyModalVisible] = useState(false);

  const { width, height } = Dimensions.get('window');
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];

  // Input referansları
  const surnameRef = useRef(null);
  const emailRef = useRef(null);
  const identityNumberRef = useRef(null);
  const birthDateRef = useRef(null);
  const mobilePhoneRef = useRef(null);
  const passwordRef = useRef(null);
  const confirmPasswordRef = useRef(null);

  // Animate the signup screen when it mounts
  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Validation functions
  const validateName = (value: string) => {
    if (!value.trim()) {
      setNameError('Ad alanı zorunludur');
      return false;
    } else if (value.trim().length < 2) {
      setNameError('Ad en az 2 karakter olmalıdır');
      return false;
    } else if (!/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/.test(value)) {
      setNameError('Ad sadece harflerden oluşmalıdır');
      return false;
    } else {
      setNameError('');
      return true;
    }
  };

  const validateSurname = (value: string) => {
    if (!value.trim()) {
      setSurnameError('Soyad alanı zorunludur');
      return false;
    } else if (value.trim().length < 2) {
      setSurnameError('Soyad en az 2 karakter olmalıdır');
      return false;
    } else if (!/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/.test(value)) {
      setSurnameError('Soyad sadece harflerden oluşmalıdır');
      return false;
    } else {
      setSurnameError('');
      return true;
    }
  };

  const validateEmail = (value: string) => {
    if (!value.trim()) {
      setEmailError('E-posta alanı zorunludur');
      return false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      setEmailError('Geçerli bir e-posta adresi giriniz');
      return false;
    } else {
      setEmailError('');
      return true;
    }
  };

  const validateIdentityNumber = (value: string) => {
    if (!value.trim()) {
      setIdentityNumberError('TC Kimlik numarası zorunludur');
      return false;
    } else if (value.length !== 11) {
      setIdentityNumberError('TC Kimlik numarası 11 haneli olmalıdır');
      return false;
    } else if (!/^\d+$/.test(value)) {
      setIdentityNumberError('TC Kimlik numarası sadece rakamlardan oluşmalıdır');
      return false;
    } else {
      // Basit kontrol: İlk hane 0 olamaz
      if (value.charAt(0) === '0') {
        setIdentityNumberError('TC Kimlik numarası 0 ile başlayamaz');
        return false;
      }

      // Diğer algoritma kontrolleri devre dışı bırakıldı

      setIdentityNumberError('');
      return true;
    }
  };

  const validateBirthDate = (value: string) => {
    if (!value.trim()) {
      setBirthDateError('Doğum tarihi zorunludur');
      return false;
    } else if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
      setBirthDateError('Doğum tarihi YYYY-AA-GG formatında olmalıdır');
      return false;
    } else {
      // Tarih geçerliliği kontrolü
      const date = new Date(value);
      const today = new Date();

      if (isNaN(date.getTime())) {
        setBirthDateError('Geçersiz tarih');
        return false;
      }

      if (date > today) {
        setBirthDateError('Doğum tarihi bugünden ileri bir tarih olamaz');
        return false;
      }

      // Yaş kontrolü (18 yaşından büyük olmalı)
      const age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();

      if (age < 18 || (age === 18 && monthDiff < 0)) {
        setBirthDateError('Kullanıcı 18 yaşından büyük olmalıdır');
        return false;
      }

      // Yıl, ay ve gün kontrolü
      const [year, month, day] = value.split('-').map(Number);

      if (year < 1900 || year > today.getFullYear()) {
        setBirthDateError('Geçersiz yıl');
        return false;
      }

      if (month < 1 || month > 12) {
        setBirthDateError('Geçersiz ay');
        return false;
      }

      const daysInMonth = new Date(year, month, 0).getDate();
      if (day < 1 || day > daysInMonth) {
        setBirthDateError('Geçersiz gün');
        return false;
      }

      setBirthDateError('');
      return true;
    }
  };

  const validateMobilePhone = (value: string) => {
    if (!value.trim()) {
      setMobilePhoneError('Telefon numarası zorunludur');
      return false;
    } else if (value.length !== 10) {
      setMobilePhoneError('Telefon numarası 10 haneli olmalıdır');
      return false;
    } else if (!/^\d+$/.test(value)) {
      setMobilePhoneError('Telefon numarası sadece rakamlardan oluşmalıdır');
      return false;
    } else if (!value.startsWith('5')) {
      setMobilePhoneError('Telefon numarası 5 ile başlamalıdır');
      return false;
    } else {
      setMobilePhoneError('');
      return true;
    }
  };

  // Password strength is now handled directly in handlePasswordChange

  const validatePassword = (value: string) => {
    if (!value.trim()) {
      setPasswordError('Şifre alanı zorunludur');
      return false;
    } else if (value.length < 8) {
      setPasswordError('Şifre en az 8 karakter olmalıdır');
      return false;
    } else if (!/[A-Z]/.test(value)) {
      setPasswordError('Şifre en az bir büyük harf içermelidir');
      return false;
    } else if (!/[0-9]/.test(value)) {
      setPasswordError('Şifre en az bir rakam içermelidir');
      return false;
    } else if (!/[^A-Za-z0-9]/.test(value)) {
      setPasswordError('Şifre en az bir özel karakter içermelidir');
      return false;
    } else {
      setPasswordError('');
      return true;
    }
  };

  const validateConfirmPassword = (value: string) => {
    if (!value.trim()) {
      setConfirmPasswordError('Şifre tekrarı zorunludur');
      return false;
    } else if (value !== password) {
      setConfirmPasswordError('Şifreler eşleşmiyor');
      return false;
    } else {
      setConfirmPasswordError('');
      return true;
    }
  };

  const validateTerms = () => {
    if (!agreeToTerms) {
      setTermsError('Kullanım Koşulları ve Gizlilik Politikası\'nı kabul etmelisiniz');
      return false;
    } else {
      setTermsError('');
      return true;
    }
  };

  // Handle input changes without validation during typing
  const handleNameChange = (value: string) => {
    setName(value);
    // No validation during typing
  };

  const handleSurnameChange = (value: string) => {
    setSurname(value);
    // No validation during typing
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    // No validation during typing
  };

  const handleIdentityNumberChange = (value: string) => {
    // Only allow digits
    const digitsOnly = value.replace(/\D/g, '');
    setIdentityNumber(digitsOnly);
    // No validation during typing
  };

  const handleBirthDateChange = (value: string) => {
    // Format as YYYY-MM-DD while typing
    let formattedValue = value.replace(/\D/g, '');

    if (formattedValue.length > 0) {
      // Add year separator
      if (formattedValue.length > 4) {
        formattedValue = `${formattedValue.substring(0, 4)}-${formattedValue.substring(4)}`;
      }

      // Add month separator
      if (formattedValue.length > 7) {
        formattedValue = `${formattedValue.substring(0, 7)}-${formattedValue.substring(7, 9)}`;
      }

      // Limit to YYYY-MM-DD format
      if (formattedValue.length > 10) {
        formattedValue = formattedValue.substring(0, 10);
      }
    }

    setBirthDate(formattedValue);
    // No validation during typing
  };

  const handleMobilePhoneChange = (value: string) => {
    // Only allow digits
    const digitsOnly = value.replace(/\D/g, '');
    setMobilePhone(digitsOnly);
    // No validation during typing
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    // Set a simple strength value without any validation
    if (value.length === 0) {
      setPasswordStrength(0);
    } else if (value.length < 8) {
      setPasswordStrength(1);
    } else {
      setPasswordStrength(3);
    }
  };

  const handleConfirmPasswordChange = (value: string) => {
    setConfirmPassword(value);
    // No validation during typing
  };

  const handleTermsChange = (value: boolean) => {
    setAgreeToTerms(value);
    // No validation during change
  };

  // Open terms modal
  const openTermsModal = () => {
    setTermsModalVisible(true);
  };

  // Open privacy policy modal
  const openPrivacyModal = () => {
    setPrivacyModalVisible(true);
  };

  const handleSignup = async () => {
    // Mark all fields as touched to trigger validation
    setNameTouched(true);
    setSurnameTouched(true);
    setEmailTouched(true);
    setPasswordTouched(true);
    setConfirmPasswordTouched(true);
    setIdentityNumberTouched(true);
    setBirthDateTouched(true);
    setMobilePhoneTouched(true);

    // Validate all fields
    const isNameValid = validateName(name);
    const isSurnameValid = validateSurname(surname);
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);
    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);
    const isIdentityNumberValid = validateIdentityNumber(identityNumber);
    const isBirthDateValid = validateBirthDate(birthDate);
    const isMobilePhoneValid = validateMobilePhone(mobilePhone);
    const isTermsValid = validateTerms();

    // Check if all validations passed
    if (!isNameValid || !isSurnameValid || !isEmailValid || !isPasswordValid ||
        !isConfirmPasswordValid || !isIdentityNumberValid || !isBirthDateValid ||
        !isMobilePhoneValid || !isTermsValid) {

      // Set general error message
      setError('Lütfen form alanlarındaki hataları düzeltin');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Kullanıcı kayıt verilerini hazırla
      const userData = {
        name,
        surname,
        email,
        password,
        identityNumber: identityNumber, // Send as string instead of parseInt
        birthDate,
        mobilePhone
      };

      // Store the user data in AsyncStorage for potential resend
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        await AsyncStorage.setItem('signupUserData', JSON.stringify(userData));
      } catch (storageError) {
        // Failed to store user data in AsyncStorage
      }

      const response = await authService.signup(userData);

      // Kayıt başarılı ise OTP gönder ve doğrulama sayfasına yönlendir
      if (response) {
        try {
          // Explicitly send OTP to the user's email
          await authService.sendOtp(email);
        } catch (otpError) {
          // Continue even if OTP sending fails - we'll handle it in the OTP screen
        }

        // Store email in AsyncStorage for backup
        try {
          const AsyncStorage = require('@react-native-async-storage/async-storage').default;
          await AsyncStorage.setItem('verificationEmail', email);
        } catch (storageError) {
          // Failed to store email in AsyncStorage
        }

        // Try multiple navigation methods
        try {
          // First try with router.navigate
          router.navigate('/auth/verify-otp', { email });
        } catch (navError) {
          try {
            // Then try with router.push
            router.push('/auth/verify-otp');
          } catch (pushError) {
            try {
              // Then try with router.replace
              router.replace('/auth/verify-otp');
            } catch (replaceError) {

              // Last resort: show alert with instructions
              Alert.alert(
                'Kayıt Başarılı',
                'Hesabınız başarıyla oluşturuldu. Lütfen e-posta doğrulama sayfasına gidin ve e-posta adresinize gönderilen kodu girin.',
                [{ text: 'Tamam' }]
              );
            }
          }
        }
      }
    } catch (err) {
      setIsLoading(false);

      // Hata mesajını göster
      if (err.response && err.response.status === 409) {
        setError('Bu e-posta adresi zaten kullanımda.');
      } else if (err.response && err.response.data && err.response.data.message) {
        setError(err.response.data.message);
      } else if (err.response && err.response.data && err.response.data.responseMessage) {
        setError(err.response.data.responseMessage);
      } else if (err.message) {
        setError(`Kayıt hatası: ${err.message}`);
      } else {
        setError('Kayıt başarısız oldu. Lütfen tekrar deneyin.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const toggleSecureEntry = () => {
    setSecureTextEntry(!secureTextEntry);
  };

  const toggleConfirmSecureEntry = () => {
    setConfirmSecureTextEntry(!confirmSecureTextEntry);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <View style={{flex: 1, width: '100%'}}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <ImageBackground
            source={require('../../assets/images/law-background.jpg')}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          >
            <View style={styles.mainContainer}>
            {Platform.OS === 'web' ? (
              <>
                {/* Arka plan gradient - Web görünümü için */}
                <LinearGradient
                  colors={['#4A78B0', '#8FB8DE']}
                  style={styles.backgroundGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                />

                {/* Dekoratif şekiller - Web görünümü için */}
                <Animated.View style={[styles.decorativeCircle, styles.decorativeCircle1, {
                  transform: [{ scale: fadeAnim }]
                }]} />
                <Animated.View style={[styles.decorativeCircle, styles.decorativeCircle2, {
                  transform: [{ scale: fadeAnim }]
                }]} />
                <Animated.View style={[styles.decorativeCircle, styles.decorativeCircle3, {
                  transform: [{ scale: fadeAnim }]
                }]} />

                {/* Sol Taraf - Logo ve Motto - Web görünümü için */}
                <View style={styles.leftSection}>
                  <LinearGradient
                    colors={['rgba(255,255,255,0.15)', 'rgba(255,255,255,0.05)']}
                    style={styles.leftGradientOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  />
                  <View style={styles.leftContent}>
                    <View style={styles.logoContainer}>
                      <Animated.View
                        style={[styles.logoIconContainer, {
                          transform: [{ scale: fadeAnim }]
                        }]}
                      >
                        <MaterialCommunityIcons name="scale-balance" size={60} color="#FFFFFF" />
                      </Animated.View>
                      <Animated.Text style={[styles.appTitle, { opacity: fadeAnim }]}>AVAS</Animated.Text>
                      <Animated.Text style={[styles.motto, { opacity: fadeAnim }]}>Adaletin Dijital Asistanı</Animated.Text>
                      <Animated.Text style={[styles.descriptionText, { opacity: fadeAnim }]}>
                        Modern avukatlık süreçlerinizi dijitalleştirin,
                        dava ve müvekkil yönetimini kolaylaştırın.
                      </Animated.Text>
                    </View>
                  </View>
                </View>
              </>
            ) : (
              // Mobil için sadece minimal bir header göster
              <View style={styles.mobileHeader}>
                <View style={styles.mobileLogoContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={30} color="#4A78B0" />
                  <Text style={styles.mobileAppTitle}>AVAS</Text>
                </View>
                <Text style={styles.mobileSubtitle}>Avukatlık Asistanı</Text>
              </View>
            )}

            {/* Sağ Taraf - Kayıt Formu */}
            <View style={[styles.rightSection, Platform.OS !== 'web' && styles.mobileRightSection]}>
              <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                alwaysBounceVertical={true}
                overScrollMode="always"
                keyboardShouldPersistTaps="handled"
              >
                <Animated.View
                  style={[
                    styles.formWrapper,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                <View style={styles.formHeader}>
                  <Text style={styles.formTitle}>Kayıt Olun</Text>
                  <View style={styles.formTitleUnderline} />
                </View>

                <View style={styles.formContainer}>
                    {/* Error Message */}
                    {error ? (
                      <Animated.View style={styles.errorContainer}>
                        <Ionicons name="alert-circle" size={20} color="#EF4444" />
                        <Text style={styles.errorText}>{error}</Text>
                      </Animated.View>
                    ) : null}

                    {/* Ad Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="person-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Ad</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        nameFocused && styles.inputWrapperFocused,
                        nameError && nameTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Adınızı girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={name}
                          onChangeText={handleNameChange}
                          returnKeyType="next"
                          onSubmitEditing={() => surnameRef.current?.focus()}
                          onFocus={() => setNameFocused(true)}
                          onBlur={() => {
                            setNameFocused(false);
                            // No validation on blur
                          }}
                        />
                        {/* Validation icon only shown after form submission */}
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Soyad Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="person-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Soyad</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        surnameFocused && styles.inputWrapperFocused,
                        surnameError && surnameTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={surnameRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Soyadınızı girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={surname}
                          onChangeText={handleSurnameChange}
                          returnKeyType="next"
                          onSubmitEditing={() => emailRef.current?.focus()}
                          onFocus={() => setSurnameFocused(true)}
                          onBlur={() => {
                            setSurnameFocused(false);
                            // No validation on blur
                          }}
                        />
                        {/* Validation icon only shown after form submission */}
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Email Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="mail-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>E-posta</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        emailFocused && styles.inputWrapperFocused,
                        emailError && emailTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={emailRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="E-posta adresinizi girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={email}
                          onChangeText={handleEmailChange}
                          autoCapitalize="none"
                          keyboardType="email-address"
                          returnKeyType="next"
                          onSubmitEditing={() => identityNumberRef.current?.focus()}
                          onFocus={() => setEmailFocused(true)}
                          onBlur={() => {
                            setEmailFocused(false);
                            // No validation on blur
                          }}
                        />
                        {/* Validation icon only shown after form submission */}
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* TC Kimlik Numarası Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="card-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>TC Kimlik Numarası</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        identityNumberFocused && styles.inputWrapperFocused,
                        identityNumberError && identityNumberTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={identityNumberRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="TC Kimlik numaranızı girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={identityNumber}
                          onChangeText={handleIdentityNumberChange}
                          keyboardType="numeric"
                          maxLength={11}
                          returnKeyType="next"
                          onSubmitEditing={() => birthDateRef.current?.focus()}
                          onFocus={() => setIdentityNumberFocused(true)}
                          onBlur={() => {
                            setIdentityNumberFocused(false);
                            // No validation on blur
                          }}
                        />
                        {/* Validation icon only shown after form submission */}
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Doğum Tarihi Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="calendar-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Doğum Tarihi (YYYY-AA-GG)</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        birthDateFocused && styles.inputWrapperFocused,
                        birthDateError && birthDateTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={birthDateRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="1990-01-30"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={birthDate}
                          onChangeText={handleBirthDateChange}
                          returnKeyType="next"
                          onSubmitEditing={() => mobilePhoneRef.current?.focus()}
                          onFocus={() => setBirthDateFocused(true)}
                          onBlur={() => {
                            setBirthDateFocused(false);
                            // No validation on blur
                          }}
                        />
                        {/* Validation icon only shown after form submission */}
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Telefon Numarası Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="call-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Telefon Numarası (5XXXXXXXXX)</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        mobilePhoneFocused && styles.inputWrapperFocused,
                        mobilePhoneError && mobilePhoneTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={mobilePhoneRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="5XXXXXXXXX"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={mobilePhone}
                          onChangeText={handleMobilePhoneChange}
                          keyboardType="phone-pad"
                          maxLength={10}
                          returnKeyType="next"
                          onSubmitEditing={() => passwordRef.current?.focus()}
                          onFocus={() => setMobilePhoneFocused(true)}
                          onBlur={() => {
                            setMobilePhoneFocused(false);
                            // No validation on blur
                          }}
                        />
                        {/* Validation icon only shown after form submission */}
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Password Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="lock-closed-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Şifre</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        passwordFocused && styles.inputWrapperFocused,
                        passwordError && passwordTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={passwordRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Şifre oluşturun"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={password}
                          onChangeText={handlePasswordChange}
                          secureTextEntry={secureTextEntry}
                          returnKeyType="next"
                          onSubmitEditing={() => confirmPasswordRef.current?.focus()}
                          onFocus={() => setPasswordFocused(true)}
                          onBlur={() => {
                            setPasswordFocused(false);
                            // No validation on blur
                          }}
                        />
                        <TouchableOpacity
                          onPress={toggleSecureEntry}
                          style={styles.eyeIcon}
                          activeOpacity={0.7}
                        >
                          <Ionicons
                            name={secureTextEntry ? "eye-outline" : "eye-off-outline"}
                            size={20}
                            color={isDark ? '#94a3b8' : '#64748b'}
                          />
                        </TouchableOpacity>
                      </View>
                      {password.length > 0 && passwordFocused && (
                        <View style={styles.passwordStrengthContainer}>
                          <View style={styles.passwordStrengthBars}>
                            <View style={[
                              styles.passwordStrengthBar,
                              passwordStrength >= 1 ? styles.passwordStrengthBarFilled : null,
                              passwordStrength === 1 ? styles.passwordStrengthBarWeak : null,
                              passwordStrength >= 3 ? styles.passwordStrengthBarStrong : null,
                            ]} />
                            <View style={[
                              styles.passwordStrengthBar,
                              passwordStrength >= 2 ? styles.passwordStrengthBarFilled : null,
                              passwordStrength >= 3 ? styles.passwordStrengthBarStrong : null,
                            ]} />
                            <View style={[
                              styles.passwordStrengthBar,
                              passwordStrength >= 3 ? styles.passwordStrengthBarFilled : null,
                              passwordStrength >= 3 ? styles.passwordStrengthBarStrong : null,
                            ]} />
                            <View style={[
                              styles.passwordStrengthBar,
                              passwordStrength >= 4 ? styles.passwordStrengthBarFilled : null,
                              passwordStrength >= 4 ? styles.passwordStrengthBarVeryStrong : null,
                            ]} />
                          </View>
                          <Text style={[
                            styles.passwordStrengthText,
                            passwordStrength === 0 ? styles.passwordStrengthTextEmpty : null,
                            passwordStrength === 1 ? styles.passwordStrengthTextWeak : null,
                            passwordStrength === 2 ? styles.passwordStrengthTextMedium : null,
                            passwordStrength === 3 ? styles.passwordStrengthTextStrong : null,
                            passwordStrength === 4 ? styles.passwordStrengthTextVeryStrong : null,
                          ]}>
                            {passwordStrength === 0 && 'Şifre girin'}
                            {passwordStrength === 1 && 'Zayıf'}
                            {passwordStrength === 2 && 'Orta'}
                            {passwordStrength === 3 && 'Güçlü'}
                            {passwordStrength === 4 && 'Çok güçlü'}
                          </Text>
                        </View>
                      )}
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Confirm Password Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="lock-closed-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Şifre Tekrarı</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        confirmPasswordFocused && styles.inputWrapperFocused,
                        confirmPasswordError && confirmPasswordTouched && styles.inputWrapperError
                      ]}>
                        <TextInput
                          ref={confirmPasswordRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Şifrenizi tekrar girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={confirmPassword}
                          onChangeText={handleConfirmPasswordChange}
                          secureTextEntry={confirmSecureTextEntry}
                          returnKeyType="done"
                          onFocus={() => setConfirmPasswordFocused(true)}
                          onBlur={() => {
                            setConfirmPasswordFocused(false);
                            // No validation on blur
                          }}
                        />
                        <TouchableOpacity
                          onPress={toggleConfirmSecureEntry}
                          style={styles.eyeIcon}
                          activeOpacity={0.7}
                        >
                          <Ionicons
                            name={confirmSecureTextEntry ? "eye-outline" : "eye-off-outline"}
                            size={20}
                            color={isDark ? '#94a3b8' : '#64748b'}
                          />
                        </TouchableOpacity>
                      </View>
                      {/* Error messages only shown after form submission */}
                    </View>

                    {/* Terms and Conditions */}
                    <TouchableOpacity
                      style={styles.termsContainer}
                      onPress={() => handleTermsChange(!agreeToTerms)}
                    >
                      <View style={[
                        styles.checkbox,
                        agreeToTerms && styles.checkboxChecked,
                        termsError && styles.checkboxError
                      ]}>
                        {agreeToTerms && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </View>
                      <ThemedText style={styles.termsText}>
                        <TouchableOpacity onPress={openTermsModal}>
                          <ThemedText style={styles.termsLink}>Kullanım Koşulları</ThemedText>
                        </TouchableOpacity> ve <TouchableOpacity onPress={openPrivacyModal}>
                          <ThemedText style={styles.termsLink}>Gizlilik Politikası</ThemedText>
                        </TouchableOpacity>'nı kabul ediyorum
                      </ThemedText>
                    </TouchableOpacity>
                    {/* Error messages only shown after form submission */}

                    {/* Signup Button */}
                    <TouchableOpacity
                      style={[styles.button, isLoading && styles.buttonDisabled]}
                      onPress={handleSignup}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <View style={styles.loadingContainer}>
                          <Text style={styles.buttonText}>Hesap oluşturuluyor</Text>
                          <View style={styles.loadingDots}>
                            <View style={styles.loadingDot} />
                            <View style={[styles.loadingDot, styles.loadingDotMiddle]} />
                            <View style={styles.loadingDot} />
                          </View>
                        </View>
                      ) : (
                        <>
                          <Text style={styles.buttonText}>Kayıt Ol</Text>
                          <Ionicons name="arrow-forward" size={20} color="white" style={styles.buttonIcon} />
                        </>
                      )}
                    </TouchableOpacity>

                    {/* Login Link */}
                    <View style={styles.footer}>
                      <ThemedText style={styles.footerText}>Zaten bir hesabınız var mı? </ThemedText>
                      <Link href="/auth/login" asChild>
                        <TouchableOpacity>
                          <ThemedText style={styles.link}>Giriş Yap</ThemedText>
                        </TouchableOpacity>
                      </Link>
                    </View>
                  </View>
                </Animated.View>
              </ScrollView>
            </View>
          </View>
            </ImageBackground>
          </KeyboardAvoidingView>
          {width > 768 && (
            <View style={styles.footerContainer}>
              <Footer />
            </View>
          )}
        </View>

        {/* Terms of Use Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={termsModalVisible}
          onRequestClose={() => setTermsModalVisible(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setTermsModalVisible(false)}
          >
            <TouchableOpacity
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
              style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>Kullanım Koşulları</ThemedText>
                <TouchableOpacity onPress={() => setTermsModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#64748B" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.modalBody}>
                <ThemedText style={styles.modalText}>
                  <ThemedText style={styles.modalTextBold}>1. Kabul Edilen Şartlar</ThemedText>{'\n\n'}
                  AVAS uygulamasını kullanarak, bu kullanım koşullarını kabul etmiş sayılırsınız. Bu koşulları kabul etmiyorsanız, lütfen uygulamayı kullanmayınız.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>2. Hizmet Tanımı</ThemedText>{'\n\n'}
                  AVAS, avukatlara yönelik bir dava ve müvekkil yönetim uygulamasıdır. Uygulama, dava takibi, müvekkil bilgilerinin yönetimi, takvim ve görev yönetimi gibi özellikleri içerir.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>3. Kullanıcı Hesabı</ThemedText>{'\n\n'}
                  Uygulamayı kullanabilmek için bir hesap oluşturmanız gerekmektedir. Hesap bilgilerinizin güvenliğinden siz sorumlusunuz. Hesabınızla ilgili herhangi bir yetkisiz erişim durumunda derhal bizimle iletişime geçmelisiniz.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>4. Kullanım Kuralları</ThemedText>{'\n\n'}
                  Uygulamayı kullanırken, tüm geçerli yasalara ve düzenlemelere uymayı kabul edersiniz. Uygulamayı kötüye kullanmamayı, başkalarının haklarını ihlal etmemeyi ve hizmetlerimizi engellememeyi taahhüt edersiniz.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>5. Fikri Mülkiyet</ThemedText>{'\n\n'}
                  Uygulama ve içeriği, fikri mülkiyet hakları ile korunmaktadır. Uygulamanın içeriğini kopyalamak, değiştirmek, dağıtmak veya ticari amaçlarla kullanmak yasaktır.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>6. Sorumluluk Sınırlaması</ThemedText>{'\n\n'}
                  Uygulama "olduğu gibi" sunulmaktadır. Uygulamanın kullanımından doğabilecek herhangi bir zarar veya kayıptan sorumlu değiliz.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>7. Değişiklikler</ThemedText>{'\n\n'}
                  Bu kullanım koşullarını herhangi bir zamanda değiştirme hakkını saklı tutarız. Değişiklikler, uygulamada yayınlandıktan sonra geçerli olacaktır.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>8. Hesap İptali</ThemedText>{'\n\n'}
                  Kullanım koşullarını ihlal etmeniz durumunda, hesabınızı askıya alma veya sonlandırma hakkını saklı tutarız.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>9. İletişim</ThemedText>{'\n\n'}
                  Herhangi bir sorunuz veya geri bildiriminiz varsa, lütfen bizimle iletişime geçin.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>10. Geçerli Yasa</ThemedText>{'\n\n'}
                  Bu kullanım koşulları, Türkiye Cumhuriyeti yasalarına tabidir.
                </ThemedText>
              </ScrollView>
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={styles.modalButton}
                  onPress={() => {
                    setTermsModalVisible(false);
                    setAgreeToTerms(true);
                    validateTerms();
                  }}
                >
                  <Text style={styles.modalButtonText}>Kabul Ediyorum</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonSecondary]}
                  onPress={() => setTermsModalVisible(false)}
                >
                  <Text style={styles.modalButtonTextSecondary}>Kapat</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>

        {/* Privacy Policy Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={privacyModalVisible}
          onRequestClose={() => setPrivacyModalVisible(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setPrivacyModalVisible(false)}
          >
            <TouchableOpacity
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
              style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>Gizlilik Politikası</ThemedText>
                <TouchableOpacity onPress={() => setPrivacyModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#64748B" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.modalBody}>
                <ThemedText style={styles.modalText}>
                  <ThemedText style={styles.modalTextBold}>1. Toplanan Bilgiler</ThemedText>{'\n\n'}
                  AVAS uygulamasını kullanırken, aşağıdaki bilgileri toplayabiliriz:{'\n'}
                  • Kişisel bilgiler (ad, soyad, e-posta, telefon numarası, TC kimlik numarası, doğum tarihi){'\n'}
                  • Dava ve müvekkil bilgileri{'\n'}
                  • Kullanım verileri ve istatistikler{'\n'}
                  • Cihaz bilgileri{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>2. Bilgilerin Kullanımı</ThemedText>{'\n\n'}
                  Topladığımız bilgileri aşağıdaki amaçlarla kullanırız:{'\n'}
                  • Hizmetlerimizi sağlamak ve iyileştirmek{'\n'}
                  • Hesabınızı yönetmek{'\n'}
                  • Teknik destek sağlamak{'\n'}
                  • Güvenliği sağlamak{'\n'}
                  • Yasal yükümlülükleri yerine getirmek{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>3. Bilgi Paylaşımı</ThemedText>{'\n\n'}
                  Bilgilerinizi aşağıdaki durumlar dışında üçüncü taraflarla paylaşmayız:{'\n'}
                  • Açık izniniz olduğunda{'\n'}
                  • Yasal bir yükümlülük olduğunda{'\n'}
                  • Hizmet sağlayıcılarımızla (sadece hizmet sağlamak amacıyla){'\n'}
                  • Şirket birleşmesi veya satın alınması durumunda{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>4. Veri Güvenliği</ThemedText>{'\n\n'}
                  Bilgilerinizi korumak için uygun teknik ve organizasyonel önlemler alıyoruz. Ancak, internet üzerinden hiçbir veri iletiminin %100 güvenli olmadığını unutmayın.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>5. Veri Saklama</ThemedText>{'\n\n'}
                  Bilgilerinizi, hizmetlerimizi sağlamak için gerekli olduğu sürece veya yasal yükümlülüklerimizi yerine getirmek için saklarız.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>6. Çerezler ve Benzer Teknolojiler</ThemedText>{'\n\n'}
                  Hizmetlerimizi iyileştirmek ve kullanıcı deneyimini geliştirmek için çerezler ve benzer teknolojiler kullanabiliriz.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>7. Kullanıcı Hakları</ThemedText>{'\n\n'}
                  KVKK kapsamında aşağıdaki haklara sahipsiniz:{'\n'}
                  • Bilgilerinize erişim{'\n'}
                  • Bilgilerinizin düzeltilmesi{'\n'}
                  • Bilgilerinizin silinmesi{'\n'}
                  • Veri işlemeye itiraz etme{'\n'}
                  • Veri taşınabilirliği{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>8. Çocukların Gizliliği</ThemedText>{'\n\n'}
                  Hizmetlerimiz 18 yaş altındaki kişilere yönelik değildir. 18 yaş altındaki kişilerden bilerek kişisel bilgi toplamayız.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>9. Değişiklikler</ThemedText>{'\n\n'}
                  Bu gizlilik politikasını herhangi bir zamanda değiştirme hakkını saklı tutarız. Değişiklikler, uygulamada yayınlandıktan sonra geçerli olacaktır.{'\n\n'}

                  <ThemedText style={styles.modalTextBold}>10. İletişim</ThemedText>{'\n\n'}
                  Gizlilik politikamızla ilgili herhangi bir sorunuz varsa, lütfen bizimle iletişime geçin.
                </ThemedText>
              </ScrollView>
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={styles.modalButton}
                  onPress={() => {
                    setPrivacyModalVisible(false);
                    setAgreeToTerms(true);
                    validateTerms();
                  }}
                >
                  <Text style={styles.modalButtonText}>Kabul Ediyorum</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonSecondary]}
                  onPress={() => setPrivacyModalVisible(false)}
                >
                  <Text style={styles.modalButtonTextSecondary}>Kapat</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  footerContainer: {
    width: '100%',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingBottom: Platform.OS === 'web' ? 0 : 50,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  mainContainer: {
    flexDirection: Platform.OS === 'web' ? 'row' : 'column',
    position: 'relative',
    overflow: 'hidden',
    width: '90%',
    maxWidth: 1000,
    height: Platform.OS === 'web' ? '90%' : 'auto',
    maxHeight: Platform.OS === 'web' ? 700 : undefined,
    borderRadius: 20,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
    marginVertical: Platform.OS === 'web' ? 0 : 20,
    backgroundColor: Platform.OS === 'web' ? 'transparent' : '#FFFFFF',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  decorativeCircle: {
    position: 'absolute',
    borderRadius: 500,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    zIndex: 1,
  },
  decorativeCircle1: {
    width: 300,
    height: 300,
    top: -100,
    left: -50,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  decorativeCircle2: {
    width: 200,
    height: 200,
    bottom: -50,
    right: '40%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle3: {
    width: 150,
    height: 150,
    top: '40%',
    right: -30,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  leftSection: {
    flex: Platform.OS === 'web' ? 1 : undefined,
    height: Platform.OS === 'web' ? undefined : 180,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    zIndex: 2,
    overflow: 'hidden',
    borderTopLeftRadius: 20,
    borderTopRightRadius: Platform.OS === 'web' ? 0 : 20,
    borderBottomLeftRadius: Platform.OS === 'web' ? 20 : 0,
  },
  leftGradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  leftContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
    position: 'relative',
    zIndex: 2,
  },
  rightSection: {
    flex: Platform.OS === 'web' ? 1 : undefined,
    backgroundColor: '#FFFFFF',
    padding: 0,
    zIndex: 2,
    borderTopRightRadius: Platform.OS === 'web' ? 20 : 0,
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: Platform.OS === 'web' ? 0 : 20,
    paddingBottom: Platform.OS === 'web' ? 0 : 20,
  },
  mobileRightSection: {
    borderRadius: 20,
    marginTop: 10,
    zIndex: 5,
  },
  mobileHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    backgroundColor: '#F8FAFC',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  mobileLogoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  mobileAppTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginLeft: 8,
  },
  mobileSubtitle: {
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 30 : 20,
    paddingVertical: Platform.OS === 'web' ? 50 : 30,
    minHeight: Platform.OS === 'web' ? '100%' : 'auto',
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
  },
  logoContainer: {
    alignItems: 'center',
    width: '100%',
    paddingVertical: Platform.OS === 'web' ? 0 : 20,
  },
  logoIconContainer: {
    width: Platform.OS === 'web' ? 120 : 80,
    height: Platform.OS === 'web' ? 120 : 80,
    borderRadius: Platform.OS === 'web' ? 60 : 40,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Platform.OS === 'web' ? 24 : 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 5px 10px rgba(0, 0, 0, 0.2)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 5 },
          shadowOpacity: 0.2,
          shadowRadius: 10,
        }
    ),
    elevation: 5,
  },
  appTitle: {
    fontSize: Platform.OS === 'web' ? 48 : 36,
    fontWeight: 'bold',
    marginBottom: Platform.OS === 'web' ? 8 : 4,
    letterSpacing: 1,
    color: '#FFFFFF',
    textAlign: 'center',
    ...(Platform.OS === 'web'
      ? { textShadow: '0px 2px 3px rgba(0, 0, 0, 0.2)' }
      : {
          textShadowColor: 'rgba(0, 0, 0, 0.2)',
          textShadowOffset: { width: 0, height: 2 },
          textShadowRadius: 3,
        }
    ),
  },
  appSubtitle: {
    fontSize: 22,
    fontWeight: '500',
    letterSpacing: 0.5,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.9,
  },
  motto: {
    fontSize: Platform.OS === 'web' ? 18 : 16,
    fontStyle: 'italic',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: Platform.OS === 'web' ? 40 : 20,
    letterSpacing: 0.5,
    opacity: 0.8,
  },
  divider: {
    width: 60,
    height: 2,
    backgroundColor: '#FFFFFF',
    marginVertical: 20,
    opacity: 0.7,
  },
  descriptionText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'web' ? 16 : 14,
    textAlign: 'center',
    lineHeight: Platform.OS === 'web' ? 24 : 20,
    maxWidth: 300,
    paddingHorizontal: 10,
    opacity: 0.8,
    display: Platform.OS === 'web' ? 'flex' : 'none', // Mobil görünümde gizle
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginBottom: 8,
  },
  formTitleUnderline: {
    width: 40,
    height: 4,
    backgroundColor: '#4A78B0',
    borderRadius: 2,
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 20,
  },
  errorText: {
    color: '#EF4444',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    marginRight: 8,
    color: '#1E3A8A',
  },
  label: {
    fontWeight: '600',
    fontSize: 16,
    color: '#1E3A8A',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#CBD5E1',
    backgroundColor: '#FFFFFF',
    zIndex: 10,
  },
  inputWrapperFocused: {
    borderColor: '#1E3A8A',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 0px 5px rgba(30, 58, 138, 0.2)' }
      : {
          shadowColor: '#1E3A8A',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.2,
          shadowRadius: 5,
        }
    ),
    elevation: 2,
  },
  inputWrapperError: {
    borderColor: '#EF4444',
    borderWidth: 1,
  },
  fieldErrorText: {
    color: '#EF4444',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  validIcon: {
    position: 'absolute',
    right: 12,
    padding: 8,
    zIndex: 30,
  },
  passwordStrengthContainer: {
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  passwordStrengthBars: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 10,
  },
  passwordStrengthBar: {
    height: 4,
    flex: 1,
    backgroundColor: '#E2E8F0',
    marginHorizontal: 2,
    borderRadius: 2,
  },
  passwordStrengthBarFilled: {
    backgroundColor: '#10B981',
  },
  passwordStrengthBarWeak: {
    backgroundColor: '#EF4444',
  },
  passwordStrengthBarMedium: {
    backgroundColor: '#F59E0B',
  },
  passwordStrengthBarStrong: {
    backgroundColor: '#10B981',
  },
  passwordStrengthBarVeryStrong: {
    backgroundColor: '#047857',
  },
  passwordStrengthText: {
    fontSize: 12,
    fontWeight: '600',
    width: 70,
    textAlign: 'right',
  },
  passwordStrengthTextEmpty: {
    color: '#94A3B8',
  },
  passwordStrengthTextWeak: {
    color: '#EF4444',
  },
  passwordStrengthTextMedium: {
    color: '#F59E0B',
  },
  passwordStrengthTextStrong: {
    color: '#10B981',
  },
  passwordStrengthTextVeryStrong: {
    color: '#047857',
  },
  checkboxError: {
    borderColor: '#EF4444',
    borderWidth: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    ...Platform.select({
      web: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
      }
    }),
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    width: '100%',
    maxWidth: 600,
    maxHeight: Platform.OS === 'web' ? '80vh' : '90%',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1E3A8A',
  },
  modalBody: {
    padding: 16,
    maxHeight: Platform.OS === 'web' ? '50vh' : 400,
    flex: 1,
  },
  modalText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#1E293B',
  },
  modalTextBold: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#1E3A8A',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    gap: 10,
  },
  modalButton: {
    backgroundColor: '#4A78B0',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  modalButtonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#4A78B0',
  },
  modalButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalButtonTextSecondary: {
    color: '#4A78B0',
    fontWeight: 'bold',
    fontSize: 16,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'transparent',
    color: '#1E293B',
    zIndex: 20,
  },
  inputLight: {
    color: '#1E293B',
  },
  inputDark: {
    color: '#1E293B',
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    padding: 8,
    zIndex: 30,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 6,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#1E3A8A',
    borderColor: '#1E3A8A',
  },
  termsText: {
    flex: 1,
    fontSize: 14,
    color: '#64748B',
  },
  termsLink: {
    color: '#4A78B0',
    fontWeight: '600',
  },
  button: {
    backgroundColor: '#4A78B0',
    height: 50,
    borderRadius: 25, // Daha yuvarlak buton
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 4px 8px rgba(74, 120, 176, 0.3)' }
      : {
          shadowColor: '#4A78B0',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }
    ),
    elevation: 5,
    paddingHorizontal: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  buttonIcon: {
    marginLeft: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    marginLeft: 8,
    alignItems: 'center',
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
  loadingDotMiddle: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: Platform.OS === 'web' ? 0 : 30,
    paddingBottom: Platform.OS === 'web' ? 0 : 20,
  },
  footerText: {
    fontSize: 16,
    color: '#64748B',
  },
  link: {
    color: '#4A78B0',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 4,
  },

});


