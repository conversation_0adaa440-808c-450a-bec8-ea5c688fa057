import React from 'react';
import { ImageBackground, StyleSheet, View, ViewStyle } from 'react-native';
import { ThemedView } from './ThemedView';

interface BackgroundWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

/**
 * A wrapper component that provides a consistent background image for all pages
 */
export const BackgroundWrapper: React.FC<BackgroundWrapperProps> = ({ children, style }) => {
  return (
    <ThemedView style={[styles.container, style]}>
      <ImageBackground
        source={require('@/assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
        resizeMode="cover"
      >
        {children}
      </ImageBackground>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
});

export default BackgroundWrapper;
