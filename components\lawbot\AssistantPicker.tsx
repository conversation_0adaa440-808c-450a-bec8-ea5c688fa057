import React, { useState } from 'react';
import { View, StyleSheet, Platform, TouchableOpacity, Modal, FlatList, Pressable } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Assistant } from '@/services/lawbotService';

interface AssistantPickerProps {
  assistants: Assistant[];
  selectedAssistant: string;
  onAssistantChange: (assistantId: string) => void;
}

export default function AssistantPicker({
  assistants,
  selectedAssistant,
  onAssistantChange
}: AssistantPickerProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [modalVisible, setModalVisible] = useState(false);

  // On Android, use a regular View instead of BlurView for better performance
  const Container = Platform.OS === 'android' ? View : BlurView;

  // Find the selected assistant name
  const selectedAssistantName = assistants.find(a => a.id === selectedAssistant)?.name || 'Select Assistant';

  // iOS style picker (modal)
  const renderIOSPicker = () => (
    <>
      <TouchableOpacity
        style={[styles.iosPicker, { backgroundColor: isDark ? 'rgba(60, 60, 60, 0.8)' : 'rgba(255, 255, 255, 0.8)' }]}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        <ThemedText style={styles.selectedText}>{selectedAssistantName}</ThemedText>
        <Ionicons
          name="chevron-down"
          size={20}
          color={Colors[colorScheme ?? 'light'].tint}
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        >
          <View
            style={[styles.modalContent, { backgroundColor: isDark ? '#1c1c1e' : '#ffffff' }]}
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <ThemedText style={styles.doneButton}>Done</ThemedText>
              </TouchableOpacity>
            </View>
            <FlatList
              data={assistants}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.optionItem, item.id === selectedAssistant && styles.selectedOption]}
                  onPress={() => {
                    onAssistantChange(item.id);
                    setModalVisible(false);
                  }}
                >
                  <ThemedText style={styles.optionText}>{item.name}</ThemedText>
                  {item.id === selectedAssistant && (
                    <Ionicons name="checkmark" size={22} color={Colors[colorScheme ?? 'light'].tint} />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </Pressable>
      </Modal>
    </>
  );

  // Android style picker (enhanced)
  const renderAndroidPicker = () => (
    <View style={[
      styles.androidPickerContainer,
      { backgroundColor: isDark ? 'rgba(50, 50, 50, 0.9)' : 'rgba(250, 250, 250, 0.9)' }
    ]}>
      <Picker
        selectedValue={selectedAssistant}
        onValueChange={(itemValue) => onAssistantChange(itemValue)}
        style={[
          styles.androidPicker,
          { color: isDark ? '#fff' : '#000' }
        ]}
        dropdownIconColor={Colors[colorScheme ?? 'light'].tint}
      >
        {assistants.map((assistant) => (
          <Picker.Item
            key={assistant.id}
            label={assistant.name}
            value={assistant.id}
          />
        ))}
      </Picker>
    </View>
  );

  return (
    <Container
      intensity={Platform.OS !== 'android' ? (isDark ? 40 : 60) : undefined}
      tint={Platform.OS !== 'android' ? (isDark ? 'dark' : 'light') : undefined}
      style={[styles.container, Platform.OS === 'android' && { backgroundColor: isDark ? '#333' : '#f0f0f0' }]}
    >
      <ThemedText style={styles.label}>Select Assistant:</ThemedText>
      {Platform.OS === 'ios' ? renderIOSPicker() : renderAndroidPicker()}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(150, 150, 150, 0.2)',
    backgroundColor: Platform.OS === 'android' ? 'rgba(245, 245, 245, 0.9)' : undefined,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  // iOS styles
  iosPicker: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(150, 150, 150, 0.3)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        }
    ),
    elevation: 2,
  },
  selectedText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 30,
    maxHeight: '70%',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px -2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(150, 150, 150, 0.3)',
  },
  doneButton: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(150, 150, 150, 0.2)',
  },
  selectedOption: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  optionText: {
    fontSize: 16,
  },
  // Android styles
  androidPickerContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(150, 150, 150, 0.3)',
    elevation: 2,
  },
  androidPicker: {
    height: 50,
    width: '100%',
  },
});
