import React, { useState } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, TextInput, View } from 'react-native';
import { router } from 'expo-router';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Sample case data for dropdown
const CASES_DATA = [
  { id: '1', title: '<PERSON> vs. <PERSON>' },
  { id: '2', title: 'Williams Divorce' },
  { id: '3', title: 'Davis Estate' },
  { id: '4', title: 'Thompson LLC' },
  { id: '5', title: 'Martinez Criminal Case' },
];

export default function AddEventScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [eventTitle, setEventTitle] = useState('');
  const [eventType, setEventType] = useState('Hearing');
  const [caseId, setCaseId] = useState('');
  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [location, setLocation] = useState('');
  const [notes, setNotes] = useState('');
  
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  
  const handleDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(false);
    setDate(currentDate);
  };
  
  const handleTimeChange = (event, selectedTime) => {
    const currentTime = selectedTime || time;
    setShowTimePicker(false);
    setTime(currentTime);
  };
  
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };
  
  const formatTime = (time) => {
    return time.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  const handleSave = () => {
    // In a real app, you would save the event data to your backend
    // For now, we'll just navigate back to the dashboard
    router.replace('/(tabs)');
  };
  
  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol size={24} name="chevron.left" color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Add New Event</ThemedText>
      </ThemedView>
      
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.scrollContent}>
        <ThemedView style={styles.formSection}>
          <ThemedText style={styles.sectionTitle}>Event Information</ThemedText>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Event Title *</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={eventTitle}
              onChangeText={setEventTitle}
              placeholder="Enter event title"
              placeholderTextColor="#999"
            />
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Event Type *</ThemedText>
            <View style={[styles.pickerContainer, isDark && styles.pickerContainerDark]}>
              <Picker
                selectedValue={eventType}
                onValueChange={(itemValue) => setEventType(itemValue)}
                style={[styles.picker, isDark && styles.pickerDark]}
                dropdownIconColor={isDark ? '#fff' : '#000'}
              >
                <Picker.Item label="Hearing" value="Hearing" />
                <Picker.Item label="Deposition" value="Deposition" />
                <Picker.Item label="Client Meeting" value="Client Meeting" />
                <Picker.Item label="Deadline" value="Deadline" />
                <Picker.Item label="Other" value="Other" />
              </Picker>
            </View>
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Related Case</ThemedText>
            <View style={[styles.pickerContainer, isDark && styles.pickerContainerDark]}>
              <Picker
                selectedValue={caseId}
                onValueChange={(itemValue) => setCaseId(itemValue)}
                style={[styles.picker, isDark && styles.pickerDark]}
                dropdownIconColor={isDark ? '#fff' : '#000'}
              >
                <Picker.Item label="Select a case" value="" />
                {CASES_DATA.map((caseItem) => (
                  <Picker.Item key={caseItem.id} label={caseItem.title} value={caseItem.id} />
                ))}
              </Picker>
            </View>
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Date *</ThemedText>
            <TouchableOpacity 
              style={[styles.dateTimeButton, isDark && styles.dateTimeButtonDark]}
              onPress={() => setShowDatePicker(true)}
            >
              <IconSymbol size={20} name="calendar" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.dateTimeText}>{formatDate(date)}</ThemedText>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={date}
                mode="date"
                display="default"
                onChange={handleDateChange}
              />
            )}
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Time *</ThemedText>
            <TouchableOpacity 
              style={[styles.dateTimeButton, isDark && styles.dateTimeButtonDark]}
              onPress={() => setShowTimePicker(true)}
            >
              <IconSymbol size={20} name="clock.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.dateTimeText}>{formatTime(time)}</ThemedText>
            </TouchableOpacity>
            {showTimePicker && (
              <DateTimePicker
                value={time}
                mode="time"
                display="default"
                onChange={handleTimeChange}
              />
            )}
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Location</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={location}
              onChangeText={setLocation}
              placeholder="Enter location"
              placeholderTextColor="#999"
            />
          </ThemedView>
        </ThemedView>
        
        <ThemedView style={styles.formSection}>
          <ThemedText style={styles.sectionTitle}>Notes</ThemedText>
          <TextInput
            style={[styles.notesInput, isDark && styles.notesInputDark]}
            multiline
            value={notes}
            onChangeText={setNotes}
            placeholder="Add event notes here..."
            placeholderTextColor="#999"
            textAlignVertical="top"
          />
        </ThemedView>
        
        <ThemedView style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <ThemedText style={styles.saveButtonText}>Save Event</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
    color: '#333',
  },
  inputDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
    color: '#fff',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  pickerContainerDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
  },
  picker: {
    height: 50,
    color: '#333',
  },
  pickerDark: {
    color: '#fff',
  },
  dateTimeButton: {
    height: 50,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateTimeButtonDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
  },
  dateTimeText: {
    marginLeft: 8,
  },
  notesInput: {
    height: 150,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    backgroundColor: '#fff',
    color: '#333',
  },
  notesInputDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderColor: Colors.light.tint,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    height: 50,
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
