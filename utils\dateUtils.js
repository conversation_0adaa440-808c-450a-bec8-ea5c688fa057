/**
 * Date utility functions for the application
 */

/**
 * Formats a date input (Unix timestamp or ISO string) to Turkish date and time format
 * 
 * @param {string|number} dateInput - The date input (Unix timestamp in seconds or milliseconds, or ISO string)
 * @param {boolean} includeTime - Whether to include time in the formatted string
 * @returns {string} - The formatted date string
 */
export const formatDate = (dateInput, includeTime = true) => {
  try {
    let date;
    
    // Check if the input is a number (Unix timestamp)
    if (typeof dateInput === 'number' || !isNaN(Number(dateInput))) {
      const epochTime = Number(dateInput);
      
      // If the epoch time is in seconds (10 digits or less), convert to milliseconds
      // Otherwise assume it's already in milliseconds (13 digits)
      if (epochTime < 10000000000) {
        date = new Date(epochTime * 1000); // Convert seconds to milliseconds
      } else {
        date = new Date(epochTime);
      }
    } else {
      // Handle as a date string
      date = new Date(dateInput);
    }
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid date:', dateInput);
      return String(dateInput);
    }
    
    // Format options
    const dateOptions = {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    };
    
    const timeOptions = {
      hour: '2-digit',
      minute: '2-digit'
    };
    
    // Format date with or without time
    if (includeTime) {
      return date.toLocaleDateString('tr-TR', {
        ...dateOptions,
        ...timeOptions
      });
    } else {
      return date.toLocaleDateString('tr-TR', dateOptions);
    }
  } catch (error) {
    console.error('Error formatting date:', error, 'Input:', dateInput);
    return String(dateInput);
  }
};

/**
 * Formats a date input for relative display (Today, Tomorrow, or date)
 * 
 * @param {string|number} dateInput - The date input (Unix timestamp or ISO string)
 * @returns {string} - The formatted relative date string
 */
export const formatRelativeDate = (dateInput) => {
  try {
    let date;
    
    // Check if the input is a number (Unix timestamp)
    if (typeof dateInput === 'number' || !isNaN(Number(dateInput))) {
      const epochTime = Number(dateInput);
      
      // If the epoch time is in seconds (10 digits), convert to milliseconds
      // Otherwise assume it's already in milliseconds (13 digits)
      if (epochTime < 10000000000) {
        date = new Date(epochTime * 1000); // Convert seconds to milliseconds
      } else {
        date = new Date(epochTime);
      }
    } else {
      // Handle as a date string
      date = new Date(dateInput);
    }
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid date:', dateInput);
      return String(dateInput);
    }
    
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Bugün
    if (date.toDateString() === today.toDateString()) {
      return `Bugün, ${date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    // Yarın
    if (date.toDateString() === tomorrow.toDateString()) {
      return `Yarın, ${date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    // Diğer günler
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting relative date:', error, 'Input:', dateInput);
    return String(dateInput);
  }
};
