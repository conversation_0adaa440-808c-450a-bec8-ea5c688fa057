import React, { useEffect } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import Constants from 'expo-constants';
import { openUri } from '@/utils/openUri';

interface WebViewHandlerProps {
  uri: string;
}

/**
 * Component that handles opening a URI based on platform:
 * - On web: Opens the URI in a new tab
 * - On mobile: Renders a WebView
 */
export default function WebViewHandler({ uri }: WebViewHandlerProps) {
  // For browser platforms, open in a new tab
  useEffect(() => {
    if (Platform.OS === 'web') {
      openUri(uri);
    }
  }, [uri]);
  
  // For non-browser platforms, render a WebView
  if (Platform.OS !== 'web') {
    return (
      <View style={styles.container}>
        <WebView
          style={styles.webView}
          source={{ uri }}
        />
      </View>
    );
  }
  
  // Return an empty view for web platforms
  return <View />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: Constants.statusBarHeight,
  },
  webView: {
    flex: 1,
  },
});
