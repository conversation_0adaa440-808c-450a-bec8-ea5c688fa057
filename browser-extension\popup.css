/* <PERSON><PERSON> */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  width: 450px;
  height: 550px;
  background-color: #f0f4f8;
  overflow: hidden;
  padding: 10px;
}

.main-container {
  position: relative;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #4A78B0, #8FB8DE);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Dekoratif Daireler */
.decorative-circle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  z-index: 1;
}

.decorative-circle1 {
  width: 150px;
  height: 150px;
  top: -50px;
  left: -25px;
  background-color: rgba(255, 255, 255, 0.15);
}

.decorative-circle2 {
  width: 100px;
  height: 100px;
  bottom: -25px;
  right: 40%;
  background-color: rgba(255, 255, 255, 0.1);
}

.decorative-circle3 {
  width: 75px;
  height: 75px;
  top: 40%;
  right: -15px;
  background-color: rgba(255, 255, 255, 0.08);
}

/* Sol Taraf - Logo ve Motto */
.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
  background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
  z-index: 2;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.logo-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: rgba(255, 255, 255, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.logo-icon-container i {
  font-size: 36px;
  color: white;
}

.app-title {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
  letter-spacing: 1px;
  color: white;
  text-align: center;
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.motto {
  font-size: 16px;
  font-style: italic;
  color: white;
  text-align: center;
  margin-bottom: 20px;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.divider {
  width: 50px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.5);
  margin-bottom: 20px;
  transform: scaleX(0);
}

.description-text {
  font-size: 14px;
  line-height: 1.5;
  color: white;
  text-align: center;
  opacity: 0.9;
  max-width: 250px;
}

/* Sağ Taraf - Formlar */
.right-section {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px;
  background-color: white;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  z-index: 2;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.05);
}

.form {
  width: 100%;
  max-width: 350px;
  max-height: 100%;
  overflow-y: auto;
}

/* Form scrollbar stilleri */
.form::-webkit-scrollbar {
  width: 5px;
}

.form::-webkit-scrollbar-track {
  background: transparent;
}

.form::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.form::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.form-header {
  margin-bottom: 25px;
  text-align: center;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 10px;
}

.form-title-underline {
  width: 50px;
  height: 3px;
  background-color: #4A78B0;
  margin: 0 auto;
}

/* Input Grupları */
.input-group {
  margin-bottom: 20px;
}

.label-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.input-icon {
  font-size: 16px;
  color: #64748b;
  margin-right: 8px;
}

.input-group label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  border: 1px solid #e2e8f0;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #f8fafc;
}

.input-wrapper:focus-within {
  border-color: #1E3A8A;
  box-shadow: 0 0 8px rgba(30, 58, 138, 0.2);
}

.input-group input {
  width: 100%;
  height: 50px;
  padding: 0 16px;
  font-size: 16px;
  background-color: transparent;
  border: none;
  color: #1E293B;
}

.input-group input:focus {
  outline: none;
}

.eye-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #64748b;
  padding: 8px;
}

.error-message {
  color: #d93025;
  font-size: 12px;
  margin-bottom: 15px;
  min-height: 18px;
}

/* Butonlar */
.login-button, .logout-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  background-color: #4A78B0;
  color: white;
  font-size: 18px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  margin-bottom: 20px;
  box-shadow: 0 6px 12px rgba(74, 120, 176, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover, .logout-button:hover {
  background-color: #3A67A0;
  box-shadow: 0 8px 16px rgba(74, 120, 176, 0.4);
  transform: translateY(-2px);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.switch-form {
  font-size: 14px;
  color: #64748b;
  text-align: center;
  margin-top: 15px;
}

.switch-form a {
  color: #4A78B0;
  text-decoration: none;
  font-weight: 600;
}

.switch-form a:hover {
  text-decoration: underline;
}

/* KVKK Consent */
.kvkk-consent {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  font-size: 13px;
}

.kvkk-consent input {
  margin-right: 10px;
  margin-top: 2px;
}

.kvkk-consent label {
  color: #64748b;
  line-height: 1.5;
}

.kvkk-consent a {
  color: #4A78B0;
  text-decoration: none;
  font-weight: 600;
}

.kvkk-consent a:hover {
  text-decoration: underline;
}

/* User Info */
.user-info-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #e2e8f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.user-avatar i {
  font-size: 30px;
  color: #64748b;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 5px;
}

.user-email {
  font-size: 14px;
  color: #64748b;
}

.status {
  margin: 15px 0;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.status p {
  font-size: 14px;
  color: #64748b;
  display: flex;
  align-items: center;
}

#connectionStatus {
  font-weight: 600;
  color: #10b981;
  margin-left: 5px;
}

#connectionStatus.disconnected {
  color: #ef4444;
}

.sync-container {
  margin-bottom: 20px;
}

.sync-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  background-color: #10b981;
  color: white;
  font-size: 16px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  margin-bottom: 10px;
  box-shadow: 0 6px 12px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sync-button i {
  margin-right: 8px;
  font-size: 16px;
}

.sync-button:hover {
  background-color: #059669;
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

.sync-button.syncing {
  background-color: #6b7280;
  cursor: not-allowed;
}

.sync-button.syncing i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.sync-info {
  font-size: 12px;
  color: #64748b;
  line-height: 1.5;
  margin-top: 5px;
  text-align: center;
}

.kvkk-info {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.kvkk-info p {
  font-size: 12px;
  color: #64748b;
  line-height: 1.5;
}

.kvkk-info a {
  color: #4A78B0;
  text-decoration: none;
  font-weight: 600;
}

.kvkk-info a:hover {
  text-decoration: underline;
}

.logout-button {
  background-color: #ef4444;
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.logout-button:hover {
  background-color: #dc2626;
  box-shadow: 0 6px 12px rgba(239, 68, 68, 0.4);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 25px;
  padding: 25px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Özel scrollbar stilleri */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.close {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close:hover {
  color: #1e293b;
}

.kvkk-text {
  margin: 20px 0;
  font-size: 14px;
  line-height: 1.6;
  color: #1e293b;
}

.kvkk-text p {
  margin-bottom: 15px;
}

.kvkk-text ul {
  margin-left: 20px;
  margin-bottom: 15px;
}

#acceptKvkk {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  background-color: #4A78B0;
  color: white;
  font-size: 16px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  margin-top: 20px;
  box-shadow: 0 6px 12px rgba(74, 120, 176, 0.3);
  transition: all 0.3s ease;
}

#acceptKvkk:hover {
  background-color: #3A67A0;
  box-shadow: 0 8px 16px rgba(74, 120, 176, 0.4);
  transform: translateY(-2px);
}

/* Utility */
.hidden {
  display: none;
}
