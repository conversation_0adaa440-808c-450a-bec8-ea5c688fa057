import apiClient from './api';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Bildirim servisi
const notificationService = {
  // Bildirim izinlerini kontrol et ve gerekirse iste
  checkNotificationPermissions: async () => {
    if (Platform.OS === 'web') {
      return false; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      return false;
    }

    return true;
  },

  // Bildirim ayarlarını kaydet
  saveNotificationSettings: async (settings) => {
    try {
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Save notification settings error:', error);
      return false;
    }
  },

  // Bildirim ayarlarını getir
  getNotificationSettings: async () => {
    try {
      const settings = await AsyncStorage.getItem('notificationSettings');
      return settings ? JSON.parse(settings) : {
        enabled: true,
        taskReminders: true,
        hearingReminders: true,
        deadlineReminders: true,
        reminderTime: 30, // Dakika cinsinden varsayılan hatırlatma süresi
      };
    } catch (error) {
      console.error('Get notification settings error:', error);
      return null;
    }
  },

  // Yerel bildirim gönder
  sendLocalNotification: async (title, body, data = {}) => {
    if (Platform.OS === 'web') {
      return; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    const hasPermission = await notificationService.checkNotificationPermissions();
    if (!hasPermission) {
      return;
    }

    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: null, // Hemen göster
    });
  },

  // Zamanlanmış bildirim gönder
  scheduleNotification: async (title, body, date, data = {}) => {
    if (Platform.OS === 'web') {
      return null; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    const hasPermission = await notificationService.checkNotificationPermissions();
    if (!hasPermission) {
      return null;
    }

    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: {
        date,
      },
    });

    return notificationId;
  },

  // Bildirimi iptal et
  cancelNotification: async (notificationId) => {
    if (Platform.OS === 'web') {
      return; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    await Notifications.cancelScheduledNotificationAsync(notificationId);
  },

  // Tüm bildirimleri iptal et
  cancelAllNotifications: async () => {
    if (Platform.OS === 'web') {
      return; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    await Notifications.cancelAllScheduledNotificationsAsync();
  },

  // Görev için bildirim planla
  scheduleTaskReminder: async (task) => {
    if (Platform.OS === 'web') {
      return null; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    const settings = await notificationService.getNotificationSettings();
    if (!settings || !settings.enabled || !settings.taskReminders) {
      return null;
    }

    const reminderTime = settings.reminderTime || 30; // Dakika cinsinden
    const taskDate = new Date(task.dueDate);
    const reminderDate = new Date(taskDate.getTime() - (reminderTime * 60 * 1000));

    // Eğer hatırlatma zamanı geçmişse, bildirim gönderme
    if (reminderDate <= new Date()) {
      return null;
    }

    const title = `${task.type}: ${task.title}`;
    const body = `${task.description || 'Görev zamanı yaklaşıyor'} - ${taskDate.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`;

    const notificationId = await notificationService.scheduleNotification(
      title,
      body,
      reminderDate,
      {
        type: 'task',
        taskId: task.id,
        taskType: task.type,
      }
    );

    return notificationId;
  },

  // Duruşma için bildirim planla
  scheduleHearingReminder: async (hearing) => {
    if (Platform.OS === 'web') {
      return null; // Web'de bildirimler için farklı bir yaklaşım gerekebilir
    }

    const settings = await notificationService.getNotificationSettings();
    if (!settings || !settings.enabled || !settings.hearingReminders) {
      return null;
    }

    const reminderTime = settings.reminderTime || 30; // Dakika cinsinden
    const hearingDate = new Date(`${hearing.date}T${hearing.time}`);
    const reminderDate = new Date(hearingDate.getTime() - (reminderTime * 60 * 1000));

    // Eğer hatırlatma zamanı geçmişse, bildirim gönderme
    if (reminderDate <= new Date()) {
      return null;
    }

    const title = `Duruşma Hatırlatıcısı: ${hearing.type}`;
    const body = `${hearing.location} - ${hearingDate.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`;

    const notificationId = await notificationService.scheduleNotification(
      title,
      body,
      reminderDate,
      {
        type: 'hearing',
        hearingId: hearing.id,
        caseId: hearing.caseId,
      }
    );

    return notificationId;
  },

  // Bildirim listesini API'den getir
  getNotifications: async () => {
    try {
      const response = await apiClient.get('/api/notifications');
      return response.data;
    } catch (error) {
      console.error('Get notifications error:', error);
      throw error;
    }
  },

  // Kullanıcının bildirimlerini getir
  getUserNotifications: async () => {
    try {
      console.log('API İsteği Gönderiliyor: GET /api/user/notifications');
      const response = await apiClient.get('/api/user/notifications');

      // API yanıtını kontrol et ve konsola yazdır
      console.log('API Yanıtı:', JSON.stringify(response, null, 2));
      console.log('API Yanıt Verileri:', JSON.stringify(response.data, null, 2));

      if (response.data) {
        console.log('Bildirimler başarıyla alındı:', response.data.length);
        return response.data;
      } else {
        console.warn('API yanıtı boş veya geçersiz format');
        return [];
      }
    } catch (error) {
      console.error('Get user notifications error:', error);
      console.error('Hata Detayları:', JSON.stringify(error, null, 2));
      if (error.response) {
        console.error('Hata Yanıtı:', JSON.stringify(error.response, null, 2));
      }
      // Hata durumunda boş dizi döndür
      return [];
    }
  },

  // Bildirimi okundu olarak işaretle
  markNotificationAsRead: async (notificationId) => {
    try {
      const response = await apiClient.put(`/api/notifications/${notificationId}/read`);
      return response.data;
    } catch (error) {
      console.error(`Mark notification ${notificationId} as read error:`, error);
      throw error;
    }
  },

  // Tüm bildirimleri okundu olarak işaretle
  markAllNotificationsAsRead: async () => {
    try {
      const response = await apiClient.put('/api/notifications/read-all');
      return response.data;
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      throw error;
    }
  },

  // Bildirimi sil
  deleteNotification: async (notificationId) => {
    try {
      const response = await apiClient.delete(`/api/notifications/${notificationId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete notification ${notificationId} error:`, error);
      throw error;
    }
  },

  // Örnek bildirimler (geliştirme için)
  getSampleNotifications: () => {
    return [
      {
        mesaj: "Gebze 3. Aile Mahkemesi Biriminde Bulunan 2024/671 Hukuk Dava Dosyası İstinaf Karar Kaydı Yapılmıştır. 07 Ocak 2025 14:20:12",
        baslik: "Hukuk Dava Dosyası İstinaf Karar Kaydı",
        mesajId: 95713678,
        gonderilmeTarihi: "Jan 7, 2025 2:20:12 PM",
        okunduMu: false,
        bildirimId: 148090279
      },
      {
        mesaj: "Ankara 5. İş Mahkemesi Biriminde Bulunan 2023/456 Hukuk Dava Dosyası Duruşma Günü Belirlenmiştir. 15 Ocak 2025 10:30:00",
        baslik: "Hukuk Dava Dosyası Duruşma Günü",
        mesajId: 95713679,
        gonderilmeTarihi: "Jan 5, 2025 9:15:45 AM",
        okunduMu: true,
        bildirimId: 148090280
      },
      {
        mesaj: "Sistem bakım çalışmaları nedeniyle uygulama 10 Ocak 2025 tarihinde 02:00-04:00 saatleri arasında geçici olarak kullanıma kapalı olacaktır.",
        baslik: "Sistem Bakım Duyurusu",
        mesajId: 95713680,
        gonderilmeTarihi: "Jan 3, 2025 11:45:30 AM",
        okunduMu: false,
        bildirimId: 148090281
      }
    ];
  },
};

export default notificationService;
