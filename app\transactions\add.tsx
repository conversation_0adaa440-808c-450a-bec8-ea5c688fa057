import { StyleSheet, ScrollView, TouchableOpacity, View, TextInput, Switch, Alert, Platform, ActivityIndicator } from 'react-native';
import React, { useState, useEffect } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import DateTimePicker from '@react-native-community/datetimepicker';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import transactionService from '@/services/transactionService';
import clientService from '@/services/clientService';
import caseService from '@/services/caseService';

export default function AddTransactionScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Form state
  const [type, setType] = useState('INCOME'); // INCOME, EXPENSE
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [category, setCategory] = useState('');
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [selectedCase, setSelectedCase] = useState(null);
  const [showClientPicker, setShowClientPicker] = useState(false);
  const [showCasePicker, setShowCasePicker] = useState(false);

  // API state
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState([]);
  const [cases, setCases] = useState([]);
  const [loadingClients, setLoadingClients] = useState(false);
  const [loadingCases, setLoadingCases] = useState(false);

  // Form validation
  const [errors, setErrors] = useState({
    description: '',
    amount: '',
    category: '',
  });

  // Müvekkilleri getir - NOT: clientService.getAllClients() artık API'ye istek atmıyor
  useEffect(() => {
    const loadClients = async () => {
      try {
        setLoadingClients(true);
        // NOT: Artık API'ye istek atmak yerine CasePartiesContext'ten veri kullanıyoruz
        // Bu nedenle clientService.getAllClients() çağrısını kaldırdık
        console.log('Müvekkil verileri CasePartiesContext\'ten alınıyor');
        // Şimdilik boş bir dizi kullan
        setClients([]);
      } catch (err) {
        console.error('Error loading clients:', err);
      } finally {
        setLoadingClients(false);
      }
    };

    loadClients();
  }, []);

  // Davaları getir
  useEffect(() => {
    const fetchCases = async () => {
      try {
        setLoadingCases(true);
        const data = await caseService.getUserCases();
        setCases(data);
      } catch (err) {
        console.error('Error fetching cases:', err);
      } finally {
        setLoadingCases(false);
      }
    };

    fetchCases();
  }, []);

  // Form doğrulama
  const validateForm = () => {
    let isValid = true;
    const newErrors = { description: '', amount: '', category: '' };

    if (!description.trim()) {
      newErrors.description = 'Açıklama gereklidir';
      isValid = false;
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = 'Geçerli bir tutar giriniz';
      isValid = false;
    }

    if (!category.trim()) {
      newErrors.category = 'Kategori gereklidir';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Tarih seçici değişikliği
  const onDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'ios');
    setDate(currentDate);
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Kaydet
  const handleSave = async () => {
    if (validateForm()) {
      try {
        setLoading(true);

        // API'ye gönderilecek işlem verisi
        const transactionData = {
          type,
          description,
          amount: parseFloat(amount),
          category,
          date: date.toISOString().split('T')[0],
          clientId: selectedClient,
          caseId: selectedCase
        };

        // API'ye gönder
        const response = await transactionService.createTransaction(transactionData);

        console.log('İşlem oluşturuldu:', response);

        Alert.alert(
          'Başarılı',
          'İşlem başarıyla eklendi',
          [
            {
              text: 'Tamam',
              onPress: () => router.back()
            }
          ]
        );
      } catch (err) {
        console.error('Error creating transaction:', err);
        Alert.alert('Hata', 'İşlem eklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Yeni İşlem</ThemedText>
        <TouchableOpacity onPress={handleSave} disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
          ) : (
            <Ionicons name="checkmark" size={24} color={Colors[colorScheme ?? 'light'].tint} />
          )}
        </TouchableOpacity>
      </ThemedView>

      <ScrollView style={styles.scrollView}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>İşlem Tipi</ThemedText>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'INCOME' && styles.typeButtonActive,
                  type === 'INCOME' && { backgroundColor: 'rgba(34, 197, 94, 0.2)' }
                ]}
                onPress={() => setType('INCOME')}
              >
                <Ionicons
                  name="arrow-down"
                  size={20}
                  color={type === 'INCOME' ? '#22C55E' : (isDark ? '#9ca3af' : '#64748b')}
                />
                <ThemedText style={[
                  styles.typeText,
                  type === 'INCOME' && { color: '#22C55E' }
                ]}>
                  Gelir
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'EXPENSE' && styles.typeButtonActive,
                  type === 'EXPENSE' && { backgroundColor: 'rgba(239, 68, 68, 0.2)' }
                ]}
                onPress={() => setType('EXPENSE')}
              >
                <Ionicons
                  name="arrow-up"
                  size={20}
                  color={type === 'EXPENSE' ? '#EF4444' : (isDark ? '#9ca3af' : '#64748b')}
                />
                <ThemedText style={[
                  styles.typeText,
                  type === 'EXPENSE' && { color: '#EF4444' }
                ]}>
                  Gider
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Açıklama <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.description ? styles.inputError : null
              ]}
              placeholder="İşlem açıklaması"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={description}
              onChangeText={setDescription}
            />
            {errors.description ? <ThemedText style={styles.errorText}>{errors.description}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Tutar (₺) <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.amount ? styles.inputError : null
              ]}
              placeholder="Örn: 1000"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
            />
            {errors.amount ? <ThemedText style={styles.errorText}>{errors.amount}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Kategori <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.category ? styles.inputError : null
              ]}
              placeholder="Örn: Danışmanlık Ücreti, Kira, Ofis Gideri"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={category}
              onChangeText={setCategory}
            />
            {errors.category ? <ThemedText style={styles.errorText}>{errors.category}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Tarih</ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.dateInput]}
              onPress={() => setShowDatePicker(true)}
            >
              <ThemedText>{formatDate(date)}</ThemedText>
              <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={date}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={onDateChange}
              />
            )}
          </View>
        </BlurView>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>İlişkili Kayıtlar (Opsiyonel)</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Müvekkil</ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.pickerInput]}
              onPress={() => setShowClientPicker(!showClientPicker)}
            >
              <ThemedText>
                {loadingClients ? 'Müvekkiller yükleniyor...' :
                  selectedClient ?
                    clients.find(c => c.id === selectedClient)?.name :
                    'Müvekkil seçiniz (opsiyonel)'}
              </ThemedText>
              <Ionicons
                name={showClientPicker ? 'chevron-up' : 'chevron-down'}
                size={20}
                color={isDark ? '#9ca3af' : '#64748b'}
              />
            </TouchableOpacity>

            {showClientPicker && (
              <View style={styles.pickerContainer}>
                {loadingClients ? (
                  <View style={styles.pickerLoading}>
                    <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                    <ThemedText style={styles.pickerLoadingText}>Müvekkiller yükleniyor...</ThemedText>
                  </View>
                ) : clients.length === 0 ? (
                  <View style={styles.pickerEmpty}>
                    <ThemedText style={styles.pickerEmptyText}>Müvekkil bulunamadı</ThemedText>
                  </View>
                ) : (
                  <>
                    <TouchableOpacity
                      style={styles.pickerItem}
                      onPress={() => {
                        setSelectedClient(null);
                        setShowClientPicker(false);
                      }}
                    >
                      <ThemedText>Seçimi Temizle</ThemedText>
                    </TouchableOpacity>
                    {clients.map(client => (
                      <TouchableOpacity
                        key={client.id}
                        style={styles.pickerItem}
                        onPress={() => {
                          setSelectedClient(client.id);
                          setShowClientPicker(false);
                        }}
                      >
                        <ThemedText>{client.name}</ThemedText>
                        {selectedClient === client.id && (
                          <Ionicons name="checkmark" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                        )}
                      </TouchableOpacity>
                    ))}
                  </>
                )}
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Dava</ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.pickerInput]}
              onPress={() => setShowCasePicker(!showCasePicker)}
            >
              <ThemedText>
                {loadingCases ? 'Davalar yükleniyor...' :
                  selectedCase ?
                    cases.find(c => c.id === selectedCase)?.title :
                    'Dava seçiniz (opsiyonel)'}
              </ThemedText>
              <Ionicons
                name={showCasePicker ? 'chevron-up' : 'chevron-down'}
                size={20}
                color={isDark ? '#9ca3af' : '#64748b'}
              />
            </TouchableOpacity>

            {showCasePicker && (
              <View style={styles.pickerContainer}>
                {loadingCases ? (
                  <View style={styles.pickerLoading}>
                    <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                    <ThemedText style={styles.pickerLoadingText}>Davalar yükleniyor...</ThemedText>
                  </View>
                ) : cases.length === 0 ? (
                  <View style={styles.pickerEmpty}>
                    <ThemedText style={styles.pickerEmptyText}>Dava bulunamadı</ThemedText>
                  </View>
                ) : (
                  <>
                    <TouchableOpacity
                      style={styles.pickerItem}
                      onPress={() => {
                        setSelectedCase(null);
                        setShowCasePicker(false);
                      }}
                    >
                      <ThemedText>Seçimi Temizle</ThemedText>
                    </TouchableOpacity>
                    {cases.map(caseItem => (
                      <TouchableOpacity
                        key={caseItem.id}
                        style={styles.pickerItem}
                        onPress={() => {
                          setSelectedCase(caseItem.id);
                          setShowCasePicker(false);
                        }}
                      >
                        <ThemedText>{caseItem.title}</ThemedText>
                        {selectedCase === caseItem.id && (
                          <Ionicons name="checkmark" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                        )}
                      </TouchableOpacity>
                    ))}
                  </>
                )}
              </View>
            )}
          </View>
        </BlurView>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Ionicons name="save-outline" size={20} color="white" />
              <ThemedText style={styles.saveButtonText}>Kaydet</ThemedText>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sectionTitle: {
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  requiredStar: {
    color: '#F44336',
  },
  input: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#000',
  },
  inputDark: {
    color: '#fff',
  },
  inputError: {
    borderColor: '#F44336',
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  typeButtonActive: {
    borderColor: 'transparent',
  },
  typeText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  pickerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerContainer: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    maxHeight: 200,
  },
  pickerItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  pickerLoading: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  pickerLoadingText: {
    marginLeft: 8,
  },
  pickerEmpty: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pickerEmptyText: {
    color: '#9ca3af',
  },
  saveButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
});
