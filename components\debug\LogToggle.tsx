/**
 * Debug component to toggle logging on/off
 * This is useful for debugging in mobile environments
 */

import React, { useState } from 'react';
import { View, Text, Switch, StyleSheet, Platform } from 'react-native';
import { enableAllLogs, disableVerboseLogs } from '@/utils/initLogger';
import logger from '@/utils/logger';

interface LogToggleProps {
  // Optional props can be added here
}

export default function LogToggle({}: LogToggleProps) {
  const [logsEnabled, setLogsEnabled] = useState(false);
  
  // Only show this component in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  // Toggle logging on/off
  const toggleLogs = (value: boolean) => {
    if (value) {
      enableAllLogs();
      logger.debug('Logs enabled via LogToggle component');
    } else {
      disableVerboseLogs();
      logger.info('Verbose logs disabled via LogToggle component');
    }
    setLogsEnabled(value);
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.label}>Verbose Logs</Text>
      <Switch
        value={logsEnabled}
        onValueChange={toggleLogs}
        trackColor={{ false: '#767577', true: '#81b0ff' }}
        thumbColor={logsEnabled ? '#f5dd4b' : '#f4f3f4'}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 4,
    marginVertical: 4,
    marginHorizontal: Platform.OS === 'web' ? 16 : 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
  },
});
