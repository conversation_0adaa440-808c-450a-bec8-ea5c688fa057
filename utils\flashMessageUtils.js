import { showMessage, hideMessage } from 'react-native-flash-message';
import { Colors } from '@/constants/Colors';
import { Dimensions, View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';

// Get screen dimensions
const { width } = Dimensions.get('window');

// Default style overrides for centered messages
const defaultStyleOverrides = {
  // Make the message container wider on web/desktop
  width: width > 768 ? 350 : width * 0.85,
  // Center the message horizontally
  alignSelf: 'center',
  // Add some border radius for a modern look
  borderRadius: 12,
  // Add some padding
  paddingVertical: 20,
  paddingHorizontal: 20,
  // Add shadow
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.3,
  shadowRadius: 5,
  elevation: 8,
  // White background
  backgroundColor: '#FFFFFF',
  // Add border
  borderWidth: 1,
  borderColor: '#E0E0E0',
};

// Default text style overrides
const defaultTextStyleOverrides = {
  fontSize: 18,
  fontWeight: '600',
  textAlign: 'center',
  color: '#333333',
  marginBottom: 8,
};

// Default description style overrides
const defaultDescriptionStyleOverrides = {
  fontSize: 14,
  textAlign: 'center',
  color: '#666666',
  marginBottom: 16,
};

/**
 * Custom render function for success message with icon and buttons
 * @param {Object} props - Props for the custom render
 * @returns {React.ReactNode} - Custom render component
 */
const renderCustomSuccess = ({ message, description, buttons = [], onClose }) => {
  return (
    <View style={{ alignItems: 'center', width: '100%' }}>
      {/* Success Icon */}
      <View
        style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: '#4CAF50',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: -50,
          marginBottom: 16,
          borderWidth: 4,
          borderColor: '#FFFFFF',
        }}
      >
        <Ionicons name="checkmark" size={36} color="#FFFFFF" />
      </View>

      {/* Message */}
      <Text style={defaultTextStyleOverrides}>{message}</Text>

      {/* Description */}
      {description ? (
        <Text style={defaultDescriptionStyleOverrides}>{description}</Text>
      ) : null}

      {/* Buttons */}
      {buttons.length > 0 ? (
        buttons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={{
              backgroundColor: '#4CAF50',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 6,
              marginBottom: 8,
              width: '100%',
            }}
            onPress={() => {
              if (button.onPress) {
                button.onPress();
              }
              if (button.closeOnPress !== false) {
                onClose();
              }
            }}
          >
            <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
              {button.text}
            </Text>
          </TouchableOpacity>
        ))
      ) : (
        <TouchableOpacity
          style={{
            backgroundColor: '#4CAF50',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 6,
            marginTop: 8,
            width: '100%',
          }}
          onPress={onClose}
        >
          <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
            Tamam
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * Show a success message
 * @param {string} message - The message to display
 * @param {string} description - Optional description
 * @param {Array} buttons - Optional array of button objects { text, onPress, closeOnPress }
 * @param {Object} options - Additional options for the flash message
 */
export const showSuccessMessage = (message, description = '', buttons = [], options = {}) => {
  showMessage({
    message,
    description,
    type: 'success',
    duration: 0, // Don't auto-hide when we have buttons
    backgroundColor: '#FFFFFF', // White background
    color: '#333333', // Dark text
    style: { ...defaultStyleOverrides, ...(options.style || {}) },
    titleStyle: { ...defaultTextStyleOverrides, ...(options.titleStyle || {}) },
    textStyle: { ...defaultDescriptionStyleOverrides, ...(options.textStyle || {}) },
    renderCustomContent: (props) => renderCustomSuccess({
      message,
      description,
      buttons,
      onClose: hideMessage
    }),
    ...options,
  });
};

/**
 * Custom render function for error message with icon and buttons
 * @param {Object} props - Props for the custom render
 * @returns {React.ReactNode} - Custom render component
 */
const renderCustomError = ({ message, description, buttons = [], onClose }) => {
  return (
    <View style={{ alignItems: 'center', width: '100%' }}>
      {/* Error Icon */}
      <View
        style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: '#F44336',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: -50,
          marginBottom: 16,
          borderWidth: 4,
          borderColor: '#FFFFFF',
        }}
      >
        <Ionicons name="close" size={36} color="#FFFFFF" />
      </View>

      {/* Message */}
      <Text style={defaultTextStyleOverrides}>{message}</Text>

      {/* Description */}
      {description ? (
        <Text style={defaultDescriptionStyleOverrides}>{description}</Text>
      ) : null}

      {/* Buttons */}
      {buttons.length > 0 ? (
        buttons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={{
              backgroundColor: '#F44336',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 6,
              marginBottom: 8,
              width: '100%',
            }}
            onPress={() => {
              if (button.onPress) {
                button.onPress();
              }
              if (button.closeOnPress !== false) {
                onClose();
              }
            }}
          >
            <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
              {button.text}
            </Text>
          </TouchableOpacity>
        ))
      ) : (
        <TouchableOpacity
          style={{
            backgroundColor: '#F44336',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 6,
            marginTop: 8,
            width: '100%',
          }}
          onPress={onClose}
        >
          <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
            Tamam
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * Show an error message
 * @param {string} message - The message to display
 * @param {string} description - Optional description
 * @param {Array} buttons - Optional array of button objects { text, onPress, closeOnPress }
 * @param {Object} options - Additional options for the flash message
 */
export const showErrorMessage = (message, description = '', buttons = [], options = {}) => {
  showMessage({
    message,
    description,
    type: 'danger',
    duration: 0, // Don't auto-hide when we have buttons
    backgroundColor: '#FFFFFF', // White background
    color: '#333333', // Dark text
    style: { ...defaultStyleOverrides, ...(options.style || {}) },
    titleStyle: { ...defaultTextStyleOverrides, ...(options.titleStyle || {}) },
    textStyle: { ...defaultDescriptionStyleOverrides, ...(options.textStyle || {}) },
    renderCustomContent: (props) => renderCustomError({
      message,
      description,
      buttons,
      onClose: hideMessage
    }),
    ...options,
  });
};

/**
 * Custom render function for info message with icon and buttons
 * @param {Object} props - Props for the custom render
 * @returns {React.ReactNode} - Custom render component
 */
const renderCustomInfo = ({ message, description, buttons = [], onClose }) => {
  return (
    <View style={{ alignItems: 'center', width: '100%' }}>
      {/* Info Icon */}
      <View
        style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: '#2196F3',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: -50,
          marginBottom: 16,
          borderWidth: 4,
          borderColor: '#FFFFFF',
        }}
      >
        <Ionicons name="information" size={36} color="#FFFFFF" />
      </View>

      {/* Message */}
      <Text style={defaultTextStyleOverrides}>{message}</Text>

      {/* Description */}
      {description ? (
        <Text style={defaultDescriptionStyleOverrides}>{description}</Text>
      ) : null}

      {/* Buttons */}
      {buttons.length > 0 ? (
        buttons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={{
              backgroundColor: '#2196F3',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 6,
              marginBottom: 8,
              width: '100%',
            }}
            onPress={() => {
              if (button.onPress) {
                button.onPress();
              }
              if (button.closeOnPress !== false) {
                onClose();
              }
            }}
          >
            <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
              {button.text}
            </Text>
          </TouchableOpacity>
        ))
      ) : (
        <TouchableOpacity
          style={{
            backgroundColor: '#2196F3',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 6,
            marginTop: 8,
            width: '100%',
          }}
          onPress={onClose}
        >
          <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
            Tamam
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * Show an info message
 * @param {string} message - The message to display
 * @param {string} description - Optional description
 * @param {Array} buttons - Optional array of button objects { text, onPress, closeOnPress }
 * @param {Object} options - Additional options for the flash message
 */
export const showInfoMessage = (message, description = '', buttons = [], options = {}) => {
  showMessage({
    message,
    description,
    type: 'info',
    duration: 0, // Don't auto-hide when we have buttons
    backgroundColor: '#FFFFFF', // White background
    color: '#333333', // Dark text
    style: { ...defaultStyleOverrides, ...(options.style || {}) },
    titleStyle: { ...defaultTextStyleOverrides, ...(options.titleStyle || {}) },
    textStyle: { ...defaultDescriptionStyleOverrides, ...(options.textStyle || {}) },
    renderCustomContent: (props) => renderCustomInfo({
      message,
      description,
      buttons,
      onClose: hideMessage
    }),
    ...options,
  });
};

/**
 * Custom render function for warning message with icon and buttons
 * @param {Object} props - Props for the custom render
 * @returns {React.ReactNode} - Custom render component
 */
const renderCustomWarning = ({ message, description, buttons = [], onClose }) => {
  return (
    <View style={{ alignItems: 'center', width: '100%' }}>
      {/* Warning Icon */}
      <View
        style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: '#FF9800',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: -50,
          marginBottom: 16,
          borderWidth: 4,
          borderColor: '#FFFFFF',
        }}
      >
        <Ionicons name="warning" size={36} color="#FFFFFF" />
      </View>

      {/* Message */}
      <Text style={defaultTextStyleOverrides}>{message}</Text>

      {/* Description */}
      {description ? (
        <Text style={defaultDescriptionStyleOverrides}>{description}</Text>
      ) : null}

      {/* Buttons */}
      {buttons.length > 0 ? (
        buttons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={{
              backgroundColor: '#FF9800',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 6,
              marginBottom: 8,
              width: '100%',
            }}
            onPress={() => {
              if (button.onPress) {
                button.onPress();
              }
              if (button.closeOnPress !== false) {
                onClose();
              }
            }}
          >
            <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
              {button.text}
            </Text>
          </TouchableOpacity>
        ))
      ) : (
        <TouchableOpacity
          style={{
            backgroundColor: '#FF9800',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 6,
            marginTop: 8,
            width: '100%',
          }}
          onPress={onClose}
        >
          <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
            Tamam
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * Show a warning message
 * @param {string} message - The message to display
 * @param {string} description - Optional description
 * @param {Array} buttons - Optional array of button objects { text, onPress, closeOnPress }
 * @param {Object} options - Additional options for the flash message
 */
export const showWarningMessage = (message, description = '', buttons = [], options = {}) => {
  showMessage({
    message,
    description,
    type: 'warning',
    duration: 0, // Don't auto-hide when we have buttons
    backgroundColor: '#FFFFFF', // White background
    color: '#333333', // Dark text
    style: { ...defaultStyleOverrides, ...(options.style || {}) },
    titleStyle: { ...defaultTextStyleOverrides, ...(options.titleStyle || {}) },
    textStyle: { ...defaultDescriptionStyleOverrides, ...(options.textStyle || {}) },
    renderCustomContent: (props) => renderCustomWarning({
      message,
      description,
      buttons,
      onClose: hideMessage
    }),
    ...options,
  });
};

/**
 * Custom render function for custom message with icon and buttons
 * @param {Object} props - Props for the custom render
 * @returns {React.ReactNode} - Custom render component
 */
const renderCustomMessage = ({ message, description, buttons = [], onClose, iconName = 'checkmark', iconColor = '#4CAF50', buttonColor = '#4CAF50' }) => {
  return (
    <View style={{ alignItems: 'center', width: '100%' }}>
      {/* Custom Icon */}
      <View
        style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: iconColor,
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: -50,
          marginBottom: 16,
          borderWidth: 4,
          borderColor: '#FFFFFF',
        }}
      >
        <Ionicons name={iconName} size={36} color="#FFFFFF" />
      </View>

      {/* Message */}
      <Text style={defaultTextStyleOverrides}>{message}</Text>

      {/* Description */}
      {description ? (
        <Text style={defaultDescriptionStyleOverrides}>{description}</Text>
      ) : null}

      {/* Buttons */}
      {buttons.length > 0 ? (
        buttons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={{
              backgroundColor: button.color || buttonColor,
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 6,
              marginBottom: 8,
              width: '100%',
            }}
            onPress={() => {
              if (button.onPress) {
                button.onPress();
              }
              if (button.closeOnPress !== false) {
                onClose();
              }
            }}
          >
            <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
              {button.text}
            </Text>
          </TouchableOpacity>
        ))
      ) : (
        <TouchableOpacity
          style={{
            backgroundColor: buttonColor,
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 6,
            marginTop: 8,
            width: '100%',
          }}
          onPress={onClose}
        >
          <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
            Tamam
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * Show a custom message with specified colors
 * @param {string} message - The message to display
 * @param {string} description - Optional description
 * @param {Array} buttons - Optional array of button objects { text, onPress, closeOnPress, color }
 * @param {string} iconName - Name of the Ionicons icon to display
 * @param {string} iconColor - Color of the icon background
 * @param {string} buttonColor - Color of the buttons
 * @param {Object} options - Additional options for the flash message
 */
export const showCustomMessage = (
  message,
  description = '',
  buttons = [],
  iconName = 'checkmark',
  iconColor = Colors.light.tint,
  buttonColor = Colors.light.tint,
  options = {}
) => {
  showMessage({
    message,
    description,
    backgroundColor: '#FFFFFF', // White background
    color: '#333333', // Dark text
    duration: 0, // Don't auto-hide when we have buttons
    style: { ...defaultStyleOverrides, ...(options.style || {}) },
    titleStyle: { ...defaultTextStyleOverrides, ...(options.titleStyle || {}) },
    textStyle: { ...defaultDescriptionStyleOverrides, ...(options.textStyle || {}) },
    renderCustomContent: (props) => renderCustomMessage({
      message,
      description,
      buttons,
      onClose: hideMessage,
      iconName,
      iconColor,
      buttonColor
    }),
    ...options,
  });
};

/**
 * Show a confirmation dialog with Yes/No buttons
 * @param {string} message - The message to display
 * @param {string} description - Optional description
 * @param {Function} onConfirm - Function to call when user confirms
 * @param {Function} onCancel - Optional function to call when user cancels
 * @param {Object} options - Additional options for the flash message
 */
export const showConfirmDialog = (
  message,
  description = '',
  onConfirm,
  onCancel = () => {},
  options = {}
) => {
  const iconName = options.iconName || 'trash';
  const iconColor = options.iconColor || '#F44336';
  const confirmButtonText = options.confirmButtonText || 'Evet, Sil';
  const cancelButtonText = options.cancelButtonText || 'İptal';
  const confirmButtonColor = options.confirmButtonColor || '#F44336';
  const cancelButtonColor = options.cancelButtonColor || '#9E9E9E';

  showMessage({
    message: '',
    description: '',
    backgroundColor: '#FFFFFF',
    color: '#333333',
    duration: 0,
    style: {
      ...defaultStyleOverrides,
      paddingTop: 0,
      paddingBottom: 0,
      paddingHorizontal: 0,
      ...(options.style || {})
    },
    renderCustomContent: (props) => (
      <View style={{ width: '100%' }}>
        {/* Header with Icon */}
        <View style={{
          alignItems: 'center',
          backgroundColor: iconColor,
          paddingTop: 20,
          paddingBottom: 20,
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}>
          <Ionicons name={iconName} size={36} color="#FFFFFF" />
        </View>

        {/* Content */}
        <View style={{ padding: 20, alignItems: 'center' }}>
          {/* Title */}
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#333333',
            marginBottom: 12,
            textAlign: 'center'
          }}>
            {message}
          </Text>

          {/* Description */}
          {description ? (
            <Text style={{
              fontSize: 14,
              color: '#666666',
              textAlign: 'center',
              marginBottom: 20
            }}>
              {description}
            </Text>
          ) : null}

          {/* Buttons */}
          <View style={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between' }}>
            {/* Cancel Button */}
            <TouchableOpacity
              style={{
                backgroundColor: cancelButtonColor,
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 6,
                flex: 1,
                marginRight: 8,
              }}
              onPress={() => {
                onCancel();
                hideMessage();
              }}
            >
              <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
                {cancelButtonText}
              </Text>
            </TouchableOpacity>

            {/* Confirm Button */}
            <TouchableOpacity
              style={{
                backgroundColor: confirmButtonColor,
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 6,
                flex: 1,
                marginLeft: 8,
              }}
              onPress={() => {
                onConfirm();
                hideMessage();
              }}
            >
              <Text style={{ color: '#FFFFFF', textAlign: 'center', fontWeight: '500' }}>
                {confirmButtonText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    ),
    ...options,
  });
};

// Export hideMessage directly for convenience
export { hideMessage };

// Default export with all functions
export default {
  showSuccessMessage,
  showErrorMessage,
  showInfoMessage,
  showWarningMessage,
  showCustomMessage,
  showConfirmDialog,
  hideMessage,
};
