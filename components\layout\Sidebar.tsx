import React, { useContext, useRef, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Platform, Dimensions, Animated } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { router, usePathname } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthContext } from '@/app/_layout';

interface SidebarProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function Sidebar({ isVisible, onClose }: SidebarProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { logout } = useContext(AuthContext);
  const currentPath = usePathname();

  // Animasyon için Animated değeri
  const translateX = useRef(new Animated.Value(-280)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  // isVisible değiştiğinde animasyonu başlat
  useEffect(() => {
    if (isVisible) {
      // Sidebar'ı göster - daha yumuşak animasyon
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300, // Daha uzun süre
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      // Sidebar'ı gizle
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: -280,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [isVisible]);

  const handleLogout = () => {
    logout();
    router.replace('/auth/login');
  };

  const navigateTo = (path: string) => {
    router.push(path);
    onClose();
  };

  const menuItems = [
    { icon: 'home-outline', activeIcon: 'home', label: 'Ana Sayfa', path: '/(tabs)' },
    { icon: 'folder-outline', activeIcon: 'folder', label: 'Dosyalar', path: '/(tabs)/cases' },
    { icon: 'people-outline', activeIcon: 'people', label: 'Kişiler', path: '/(tabs)/clients' },
    { icon: 'calendar-outline', activeIcon: 'calendar', label: 'Görevler', path: '/(tabs)/tasks' },
    { icon: 'business-outline', activeIcon: 'business', label: 'Duruşmalar', path: '/(tabs)/trials' },
    { icon: 'chatbubble-outline', activeIcon: 'chatbubble', label: 'Lawbot', path: '/(tabs)/lawbot' },
    { icon: 'person-outline', activeIcon: 'person', label: 'Profil', path: '/(tabs)/profile' },
  ];

  const isActive = (path: string) => {
    return currentPath.includes(path.replace('/(tabs)', ''));
  };

  return (
    <>
      {/* Arka plan overlay - sadece mobil cihazlarda göster */}
      {Platform.OS !== 'web' && isVisible && (
        <Animated.View
          style={[styles.overlay, { opacity }]}
          onTouchEnd={onClose}
        />
      )}

      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateX }],
            opacity: opacity
          }
        ]}
      >
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.sidebarBlur}>
          <View style={styles.menuContainer}>
            {menuItems.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.menuItem, isActive(item.path) && styles.activeMenuItem]}
                onPress={() => navigateTo(item.path)}
              >
                <Ionicons
                  name={isActive(item.path) ? item.activeIcon : item.icon}
                  size={24}
                  color={isActive(item.path) ? Colors[colorScheme ?? 'light'].tint : Colors[colorScheme ?? 'light'].icon}
                />
                <ThemedText style={[styles.menuText, isActive(item.path) && styles.activeMenuText]}>
                  {item.label}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Ionicons name="log-out-outline" size={24} color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.logoutText}>Çıkış Yap</ThemedText>
            </TouchableOpacity>
          </View>
        </BlurView>
      </Animated.View>
    </>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 99,
  },
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: 280,
    zIndex: 100,
    ...(Platform.OS === 'web'
      ? { boxShadow: '2px 0px 10px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 2, height: 0 },
          shadowOpacity: 0.1,
          shadowRadius: 10,
        }
    ),
    elevation: 10,
  },
  sidebarBlur: {
    flex: 1,
    borderRightWidth: 1,
    borderRightColor: 'rgba(0, 0, 0, 0.1)',
  },

  menuContainer: {
    flex: 1,
    paddingTop: 24,
    paddingBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  activeMenuItem: {
    backgroundColor: 'rgba(10, 126, 164, 0.1)',
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
    fontWeight: '500',
  },
  activeMenuText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  logoutText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
});
