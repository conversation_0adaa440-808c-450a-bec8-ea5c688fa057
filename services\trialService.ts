import apiClient from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface TrialParty {
  isim: string;
  soyad: string;
  sifat: string;
}

export interface Trial {
  kayitId: number;
  dosyaId: string;
  dosyaNo: string;
  dosyaTurKod: number;
  dosyaTurKodAciklama: string;
  birimId: string;
  birimOrgKodu: string;
  birimTuru1: string;
  birimTuru2: string;
  birimTuru3: string;
  yerelBirimAd: string;
  tarihSaat: string;
  islemTuru: number;
  islemSonucu: number;
  hakimHeyet: number;
  islemTuruAciklama: string;
  islemSonucuAciklama: string;
  dosyaTaraflari: TrialParty[];
  izinliHakimList: any[];
  talepDurumu: string;
  katilButonAktifMi: boolean;
  token: string;
  isEDurusmaBirimTalepValid: boolean;
  isEDurusmaSaatTalepValid: boolean;
  isEDurusmaGuncellenecek: boolean;
}

const trialService = {
  // Duruşmaları getir
  getUserTrials: async (): Promise<Trial[]> => {
    try {
      const response = await apiClient.get('/api/user/trials');

      // API yanıtını kontrol et
      if (!response.data) {
        throw new Error('API yanıtı boş');
      }

      console.log('API yanıtı:', response.data);

      // Yanıt bir dizi değilse, dizi içine al
      const trials = Array.isArray(response.data) ? response.data : [response.data];

      // Tarih alanını Date nesnesine çevir
      return trials.map(trial => ({
        ...trial,
        tarihSaat: trial.tarihSaat || new Date().toISOString() // Tarih yoksa şimdiki zamanı kullan
      }));
    } catch (error) {
      console.error('Duruşmalar alınırken hata oluştu:', error);
      throw error;
    }
  },

  // Duruşma detayını getir
  getTrialById: async (trialId: string | number): Promise<Trial> => {
    try {
      console.log(`Fetching trial with ID: ${trialId}`);

      // Token al
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Oturum açık değil');
      }

      // Doğrudan URL ile çağrı yap
      const url = `http://193.35.154.97:4244/api/user/trials/by-id?id=${encodeURIComponent(String(trialId))}`;
      console.log('API URL for trial by ID:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Trial data response:', data);

      if (!data) {
        throw new Error('Duruşma detayı bulunamadı');
      }

      return {
        ...data,
        tarihSaat: data.tarihSaat || new Date().toISOString()
      };
    } catch (error) {
      console.error(`Duruşma detayı alınırken hata oluştu (ID: ${trialId}):`, error);
      throw error;
    }
  },

  // Duruşma detayını dosya numarasına göre getir
  getTrialByCaseNumber: async (caseNumber: string): Promise<Trial[]> => {
    try {
      const response = await apiClient.get(`/api/user/trials/by-case?caseNumber=${encodeURIComponent(caseNumber)}`);

      if (!response.data) {
        throw new Error('Duruşma detayı bulunamadı');
      }

      // Yanıt bir dizi değilse, dizi içine al
      const trials = Array.isArray(response.data) ? response.data : [response.data];

      // Tarih alanını Date nesnesine çevir
      return trials.map(trial => ({
        ...trial,
        tarihSaat: trial.tarihSaat || new Date().toISOString() // Tarih yoksa şimdiki zamanı kullan
      }));
    } catch (error) {
      console.error(`Duruşma detayı alınırken hata oluştu (Dosya No: ${caseNumber}):`, error);
      throw error;
    }
  }
};

export default trialService;
