import { Redirect } from 'expo-router';
import React, { useContext, useEffect } from 'react';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { AuthContext } from './_layout';

export default function Index() {
  const { isAuthenticated } = useContext(AuthContext);

  // Redirect to the appropriate screen based on authentication status
  if (isAuthenticated) {
    return <Redirect href="/(tabs)" />;
  }

  // Mobil cihazlarda splash ekranına yönlendir
  if (Platform.OS !== 'web') {
    return <Redirect href="/splash" />;
  }

  // Web'de doğrudan login sayfas<PERSON>na yönlendir
  return <Redirect href="/auth/login" />;
}
