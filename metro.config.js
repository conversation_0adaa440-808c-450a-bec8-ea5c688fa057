// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');
const fs = require('fs');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add support for CSS files
config.resolver.sourceExts.push('css');

// Handle CSS files as assets instead of trying to transform them
// This is a workaround for lightningcss issues
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

// Add CSS to asset extensions
if (Array.isArray(config.resolver.assetExts)) {
  // Make sure CSS is in assetExts and not in sourceExts
  config.resolver.assetExts.push('css');
  config.resolver.sourceExts = config.resolver.sourceExts.filter(ext => ext !== 'css');
}

// Fix for the lightningcss native module issue
// Copy the native module to the expected location if it exists
const nativeModulePath = path.join(__dirname, 'node_modules', '@expo', 'metro-config', 'node_modules', 'lightningcss', 'node', 'lightningcss.win32-x64-msvc.node');
const nativeModuleSource = path.join(__dirname, 'node_modules', '@expo', 'metro-config', 'node_modules', 'lightningcss-win32-x64-msvc', 'lightningcss.win32-x64-msvc.node');

if (!fs.existsSync(nativeModulePath) && fs.existsSync(nativeModuleSource)) {
  try {
    // Create the directory if it doesn't exist
    const dir = path.dirname(nativeModulePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Copy the file
    fs.copyFileSync(nativeModuleSource, nativeModulePath);
    console.log('Successfully copied lightningcss native module');
  } catch (error) {
    console.warn('Failed to copy lightningcss native module:', error.message);
  }
}

module.exports = config;
