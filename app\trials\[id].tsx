import React, { useState, useEffect } from 'react';
import { View, ScrollView, TouchableOpacity, ActivityIndicator, StyleSheet, ImageBackground, FlatList, Platform, Text, Dimensions } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService from '@/services/notificationService';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import trialService, { Trial, TrialParty } from '@/services/trialService';
import NotificationCenter from '@/components/notifications/NotificationCenter';

export default function TrialDetailScreen() {
  const { id, from } = useLocalSearchParams();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';
  const isWideScreen = isWeb && width > 768;

  // Store the source of navigation for back button
  const [navigatedFrom, setNavigatedFrom] = useState<string | null>(null);

  // Notification state
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      setIsLoadingNotifications(true);
      console.log('Header: Bildirimler getiriliyor...');
      const data = await notificationService.getUserNotifications();

      console.log('Header: Bildirim verileri alındı:', data);

      if (Array.isArray(data)) {
        setNotifications(data);
        // Okunmamış bildirim sayısını hesapla
        const unreadCount = data.filter(notification => !notification.okunduMu).length;
        console.log('Header: Okunmamış bildirim sayısı:', unreadCount);
        setNotificationCount(unreadCount);
      } else {
        console.warn('Header: Bildirim verisi dizi değil veya boş');
        setNotifications([]);
        setNotificationCount(0);
      }
    } catch (error) {
      console.error('Header: Bildirimler alınırken hata oluştu:', error);
      setNotifications([]);
      setNotificationCount(0);
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  // Load notifications on mount
  useEffect(() => {
    fetchNotifications();

    // Refresh notifications every minute
    const interval = setInterval(() => {
      fetchNotifications();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // State
  const [trial, setTrial] = useState<Trial | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Set the navigated from state based on URL parameter
  useEffect(() => {
    if (from) {
      // Set navigatedFrom based on the 'from' query parameter
      setNavigatedFrom(from as string);
      console.log('Setting navigatedFrom from URL parameter:', from);
    } else if (isWeb) {
      // Check localStorage for previous path
      const previousPath = localStorage.getItem('previousPath');
      if (previousPath) {
        if (previousPath.includes('/trials')) {
          setNavigatedFrom('trials');
          console.log('Setting navigatedFrom from localStorage (trials)');
        }
      }
    }
  }, [from, isWeb]);

  // Fetch trial data
  useEffect(() => {
    const fetchTrialData = async () => {
      try {
        setLoading(true);
        setError('');

        // Önce localStorage veya AsyncStorage'dan duruşma verilerini almayı dene
        let storedTrialData = null;

        if (Platform.OS === 'web') {
          const storedData = localStorage.getItem('selectedTrialData');
          if (storedData) {
            try {
              storedTrialData = JSON.parse(storedData);
              console.log('Retrieved trial data from localStorage:', storedTrialData);
            } catch (parseError) {
              console.error('Error parsing stored trial data:', parseError);
            }
          }
        } else {
          const storedData = await AsyncStorage.getItem('selectedTrialData');
          if (storedData) {
            try {
              storedTrialData = JSON.parse(storedData);
              console.log('Retrieved trial data from AsyncStorage:', storedTrialData);
            } catch (parseError) {
              console.error('Error parsing stored trial data:', parseError);
            }
          }
        }

        // Eğer saklanan veri varsa ve ID'si eşleşiyorsa, onu kullan
        if (storedTrialData && storedTrialData.kayitId.toString() === id) {
          console.log('Using stored trial data for ID:', id);
          setTrial(storedTrialData);
          setLoading(false);
          return;
        }

        // Saklanan veri yoksa veya ID eşleşmiyorsa, API'den al
        console.log('No stored data found or ID mismatch, fetching from API for ID:', id);

        // Check for token
        const token = await AsyncStorage.getItem('auth_token');
        if (!token) {
          setLoading(false);
          setError('Oturum açık değil. Lütfen tekrar giriş yapın.');
          return;
        }

        // Fetch trial details by ID
        try {
          const trialDetails = await trialService.getTrialById(id as string);
          setTrial(trialDetails);
          console.log('Trial details from API:', trialDetails);
        } catch (trialError) {
          console.error('Error fetching trial details:', trialError);
          throw trialError; // Rethrow to be caught by the outer catch block
        }

      } catch (error: any) {
        console.error('Error fetching trial data:', error);
        // More detailed error message for debugging
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);

          if (error.response.status === 404) {
            setError('Duruşma bilgisi bulunamadı. Duruşma ID: ' + id);
          } else {
            setError(`API Hatası (${error.response.status}): ${JSON.stringify(error.response.data)}`);
          }
        } else if (error.request) {
          // The request was made but no response was received
          console.error('Error request:', error.request);
          setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error message:', error.message);

          // Daha kullanıcı dostu hata mesajları
          if (error.message?.includes('Network Error')) {
            setError('Ağ hatası: Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.');
          } else if (error.message?.includes('timeout')) {
            setError('Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.');
          } else if (error.message?.includes('API error')) {
            setError(`Duruşma bilgisi alınamadı: ${error.message}`);
          } else {
            setError(`Duruşma bilgileri yüklenirken bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`);
          }
        }
      } finally {
        setLoading(false);
      }
    };

    fetchTrialData();

    // Temizlik fonksiyonu - component unmount olduğunda storage'ı temizle
    return () => {
      if (Platform.OS === 'web') {
        localStorage.removeItem('selectedTrialData');
      } else {
        AsyncStorage.removeItem('selectedTrialData');
      }
    };
  }, [id]);

  // Tarih formatını düzenle
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);

      // Türkçe ay isimleri
      const aylar = [
        'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
        'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
      ];

      const gun = date.getDate();
      const ay = aylar[date.getMonth()];
      const yil = date.getFullYear();
      const saat = date.getHours().toString().padStart(2, '0');
      const dakika = date.getMinutes().toString().padStart(2, '0');

      return `${gun} ${ay} ${yil} ${saat}:${dakika}`;
    } catch (error) {
      return dateString;
    }
  };

  // Taraf sıfatına göre tag stili
  const getPartyTagStyle = (sifat: string) => {
    switch (sifat.toUpperCase()) {
      case 'DAVACI':
        return styles.davacıTag;
      case 'DAVALI':
        return styles.davalıTag;
      case 'ÇOCUK':
        return styles.cocukTag;
      case 'VEKİL':
      case 'AVUKAT':
        return styles.vekilTag;
      default:
        return styles.defaultTag;
    }
  };

  // Duruşma detay bölümlerini oluştur
  const getSections = () => {
    if (!trial) return [];

    return [
      {
        id: 'dosya',
        title: 'Dosya Bilgileri',
        content: [
          { label: 'Dosya No:', value: trial.dosyaNo },
          { label: 'Dosya Türü:', value: trial.dosyaTurKodAciklama },
          { label: 'Dosya ID:', value: trial.dosyaId }
        ]
      },
      {
        id: 'mahkeme',
        title: 'Mahkeme Bilgileri',
        content: [
          { label: 'Mahkeme:', value: trial.yerelBirimAd },
          { label: 'Birim ID:', value: trial.birimId },
          { label: 'Birim Org Kodu:', value: trial.birimOrgKodu }
        ]
      },
      {
        id: 'durusma',
        title: 'Duruşma Bilgileri',
        content: [
          { label: 'Tarih ve Saat:', value: formatDate(trial.tarihSaat) },
          { label: 'İşlem Türü:', value: trial.islemTuruAciklama },
          { label: 'İşlem Sonucu:', value: trial.islemSonucuAciklama },
          { label: 'Talep Durumu:', value: trial.talepDurumu },
          { label: 'E-Duruşma:', value: trial.isEDurusmaSaatTalepValid ? 'Mümkün' : 'Mümkün Değil' }
        ]
      },
      {
        id: 'diger',
        title: 'Diğer Bilgiler',
        content: [
          { label: 'Kayıt ID:', value: trial.kayitId },
          { label: 'Hakim/Heyet:', value: trial.hakimHeyet },
          { label: 'Katılım Butonu:', value: trial.katilButonAktifMi ? 'Aktif' : 'Pasif' }
        ]
      }
    ];
  };

  // Taraf bilgilerini hazırla
  const getPartySummaries = () => {
    if (!trial || !trial.dosyaTaraflari) return [];

    const davaci = trial.dosyaTaraflari?.filter(p => p.sifat === 'DAVACI') || [];
    const davali = trial.dosyaTaraflari?.filter(p => p.sifat === 'DAVALI') || [];
    const diger = trial.dosyaTaraflari?.filter(p => p.sifat !== 'DAVACI' && p.sifat !== 'DAVALI') || [];

    return [
      { id: 'davaci', title: 'Davacılar:', parties: davaci },
      { id: 'davali', title: 'Davalılar:', parties: davali },
      { id: 'diger', title: 'Diğer Taraflar:', parties: diger }
    ];
  };

  // Bilgi satırı render fonksiyonu
  const renderInfoRow = ({ item }) => (
    <View style={styles.infoRow}>
      <ThemedText style={styles.infoLabel}>{item.label}</ThemedText>
      <ThemedText style={styles.infoValue}>{item.value}</ThemedText>
    </View>
  );

  // Bölüm render fonksiyonu
  const renderSection = ({ item }) => (
    <View style={styles.section}>
      <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>{item.title}</ThemedText>
      <FlatList
        data={item.content}
        renderItem={renderInfoRow}
        keyExtractor={(item, index) => `${item.label}-${index}`}
        scrollEnabled={false}
      />
    </View>
  );

  // Taraf özeti render fonksiyonu
  const renderPartySummary = ({ item }) => (
    <View style={styles.partySection}>
      <ThemedText style={styles.partySectionTitle}>{item.title}</ThemedText>
      {item.parties && item.parties.length > 0 ? (
        <FlatList
          data={item.parties}
          renderItem={({ item }) => {
            // Taraf sıfatına göre stil belirle
            let tagStyle = styles.defaultTag;
            if (item.sifat === 'DAVACI') tagStyle = styles.davacıTag;
            else if (item.sifat === 'DAVALI') tagStyle = styles.davalıTag;
            else if (item.sifat === 'ÇOCUK') tagStyle = styles.cocukTag;
            else if (item.sifat === 'VEKİL' || item.sifat === 'AVUKAT') tagStyle = styles.vekilTag;

            return (
              <View style={styles.partyItemContainer}>
                <View style={[styles.partyTag, tagStyle]}>
                  <ThemedText style={styles.partyTagText}>{item.sifat}</ThemedText>
                </View>
                <ThemedText style={styles.partyItem}>
                  {item.isim} {item.soyad}
                </ThemedText>
              </View>
            );
          }}
          keyExtractor={(item, index) => `${item.id || item.isim}-${index}`}
          scrollEnabled={false}
        />
      ) : (
        <ThemedText style={styles.emptyText}>Yok</ThemedText>
      )}
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <ImageBackground
        source={require('../../assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <View style={styles.headerContainer}>
          <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => {
                    // Navigate back based on where we came from
                    if (navigatedFrom === 'trials') {
                      router.push('/(tabs)/trials');
                    } else if (Platform.OS === 'web' && localStorage.getItem('previousPath')) {
                      // Use the stored path if available
                      const previousPath = localStorage.getItem('previousPath');
                      if (previousPath) {
                        if (previousPath.includes('/trials')) {
                          router.push('/(tabs)/trials');
                        } else {
                          router.push(previousPath as any);
                        }
                      } else {
                        router.push('/trials');
                      }
                    } else {
                      // Default fallback
                      router.push('/trials');
                    }
                  }}
                >
                  <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <View style={styles.titleContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                  <Text style={styles.titleText}>AVAS</Text>
                  <Text style={styles.subtitleText}> | Duruşma Detayı</Text>
                </View>
              </View>

              {/* Right section with notification icon */}
              <View style={styles.rightSection}>
                <TouchableOpacity
                  style={styles.notificationButton}
                  onPress={toggleNotifications}
                >
                  <View style={styles.notificationIconContainer}>
                    <Ionicons name="notifications" size={24} color="#FFFFFF" />
                    {notificationCount > 0 && (
                      <View style={styles.notificationBadge}>
                        <Text style={styles.notificationBadgeText}>
                          {notificationCount > 9 ? '9+' : notificationCount}
                        </Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Notification Center */}
        {showNotifications && (
          <NotificationCenter
            notifications={notifications}
            loading={isLoadingNotifications}
            onClose={() => setShowNotifications(false)}
            onRefresh={fetchNotifications}
          />
        )}

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.loadingText}>Duruşma bilgileri yükleniyor...</ThemedText>
          </View>
        ) : error ? (
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.errorCard}>
            <Ionicons name="alert-circle" size={48} color="#EF4444" />
            <ThemedText style={styles.errorTextFull}>{error}</ThemedText>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                setLoading(true);
                setError('');

                const fetchData = async () => {
                  try {
                    // Önce localStorage veya AsyncStorage'dan duruşma verilerini almayı dene
                    let storedTrialData = null;

                    if (Platform.OS === 'web') {
                      const storedData = localStorage.getItem('selectedTrialData');
                      if (storedData) {
                        try {
                          storedTrialData = JSON.parse(storedData);
                          console.log('Retrieved trial data from localStorage:', storedTrialData);
                        } catch (parseError) {
                          console.error('Error parsing stored trial data:', parseError);
                        }
                      }
                    } else {
                      const storedData = await AsyncStorage.getItem('selectedTrialData');
                      if (storedData) {
                        try {
                          storedTrialData = JSON.parse(storedData);
                          console.log('Retrieved trial data from AsyncStorage:', storedTrialData);
                        } catch (parseError) {
                          console.error('Error parsing stored trial data:', parseError);
                        }
                      }
                    }

                    // Eğer saklanan veri varsa ve ID'si eşleşiyorsa, onu kullan
                    if (storedTrialData && storedTrialData.kayitId.toString() === id) {
                      console.log('Using stored trial data for ID:', id);
                      setTrial(storedTrialData);
                      return;
                    }

                    // Saklanan veri yoksa veya ID eşleşmiyorsa, API'den al
                    console.log('No stored data found or ID mismatch, fetching from API for ID:', id);

                    // Check for token
                    const token = await AsyncStorage.getItem('auth_token');
                    if (!token) {
                      setError('Oturum açık değil. Lütfen tekrar giriş yapın.');
                      return;
                    }

                    // Retry fetching trial details
                    const trialDetails = await trialService.getTrialById(id as string);
                    setTrial(trialDetails);
                  } catch (err: any) {
                    console.error('Error retrying fetch trial:', err);

                    // Daha kullanıcı dostu hata mesajları
                    if (err.message?.includes('Network Error')) {
                      setError('Ağ hatası: Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.');
                    } else if (err.message?.includes('timeout')) {
                      setError('Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.');
                    } else if (err.message?.includes('API error')) {
                      setError(`Duruşma bilgisi alınamadı: ${err.message}`);
                    } else {
                      setError(`Duruşma bilgileri yüklenirken bir hata oluştu: ${err.message || 'Bilinmeyen hata'}`);
                    }
                  } finally {
                    setLoading(false);
                  }
                };

                fetchData();
              }}
            >
              <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
            </TouchableOpacity>
          </BlurView>
        ) : trial ? (
          <ScrollView contentContainerStyle={styles.scrollContent}>
            {/* Duruşma Detayı Header */}
            <View style={[
              styles.modernHeader,
              isDark && {
                backgroundColor: 'rgba(17, 24, 39, 0.8)',
                borderBottomColor: 'rgba(255, 255, 255, 0.1)'
              }
            ]}>
              <View style={styles.headerContent}>
                <View style={[
                  styles.headerIconContainer,
                  isDark && { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
                ]}>
                  <Ionicons
                    name="calendar"
                    size={24}
                    color={isDark ? Colors.dark.tint : Colors.light.tint}
                  />
                </View>
                <ThemedText style={styles.headerTitle}>
                  Duruşma {trial.dosyaNo}
                </ThemedText>
                {trial.dosyaTurKodAciklama && (
                  <View style={[
                    styles.headerStatsContainer,
                    isDark && { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
                  ]}>
                    <ThemedText style={styles.headerStats}>
                      {trial.dosyaTurKodAciklama}
                    </ThemedText>
                  </View>
                )}
              </View>
            </View>

            {/* Content */}
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
              {/* Duruşma Bilgileri */}
              <View style={styles.basicInfoCard}>
                <View style={styles.basicInfoHeader}>
                  <ThemedText style={styles.basicInfoTitle}>
                    Duruşma Bilgileri
                  </ThemedText>
                </View>

                <View style={styles.basicInfoGrid}>
                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Dosya No:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{trial.dosyaNo}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Tarih ve Saat:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{formatDate(trial.tarihSaat)}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>İşlem Türü:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{trial.islemTuruAciklama}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>İşlem Sonucu:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{trial.islemSonucuAciklama || 'Belirtilmemiş'}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>E-Duruşma:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{trial.isEDurusmaSaatTalepValid ? 'Mümkün' : 'Mümkün Değil'}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Hakim/Heyet:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{trial.hakimHeyet || 'Belirtilmemiş'}</ThemedText>
                  </View>
                </View>
              </View>

              {/* Mahkeme Bilgileri */}
              <View style={styles.basicInfoCard}>
                <View style={styles.basicInfoHeader}>
                  <ThemedText style={styles.basicInfoTitle}>
                    Mahkeme Bilgileri
                  </ThemedText>
                </View>

                <View style={styles.basicInfoGrid}>
                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Mahkeme:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{trial.yerelBirimAd}</ThemedText>
                  </View>

                  {trial.dosyaTurKodAciklama && (
                    <View style={styles.basicInfoItem}>
                      <ThemedText style={styles.basicInfoLabel}>Dosya Türü:</ThemedText>
                      <ThemedText style={styles.basicInfoValue}>{trial.dosyaTurKodAciklama}</ThemedText>
                    </View>
                  )}
                </View>
              </View>

              {/* Taraf Bilgileri */}
              <View style={styles.section}>
                <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>Taraf Bilgileri</ThemedText>
                {trial.dosyaTaraflari && trial.dosyaTaraflari.length > 0 ? (
                  <View style={styles.partiesContainer}>
                    <FlatList
                      data={getPartySummaries()}
                      renderItem={renderPartySummary}
                      keyExtractor={item => item.id}
                      scrollEnabled={false}
                    />
                  </View>
                ) : (
                  <ThemedText style={styles.emptyText}>Taraf bilgisi bulunamadı</ThemedText>
                )}
              </View>
            </BlurView>
          </ScrollView>
        ) : (
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.errorCard}>
            <Ionicons name="alert-circle" size={48} color="#EF4444" />
            <ThemedText style={styles.errorTextFull}>Duruşma bilgisi bulunamadı</ThemedText>
          </BlurView>
        )}
      </ImageBackground>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  headerContainer: {
    width: '100%',
    zIndex: 10,
  },
  headerBackground: {
    width: '100%',
    paddingTop: 10,
    paddingBottom: 10,
  },
  headerBackgroundLight: {
    backgroundColor: Colors.light.tint,
  },
  headerBackgroundDark: {
    backgroundColor: '#1E293B',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuButton: {
    padding: 8,
    borderRadius: 8,
  },
  notificationButton: {
    padding: 8,
    borderRadius: 8,
  },
  notificationIconContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  logo: {
    marginRight: 8,
  },
  titleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitleText: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  errorTextFull: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  scrollContent: {
    paddingBottom: 24,
    ...(Platform.OS === 'web' && {
      width: '80%',
      maxWidth: 1200,
      marginLeft: 'auto',
      marginRight: 'auto',
    }),
  },
  modernHeader: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 16,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  headerStatsContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  headerStats: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Tab styles
  tabContainer: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 8,
  },
  tabScrollContent: {
    paddingVertical: 4,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  activeTabButton: {
    backgroundColor: `${Colors.light.tint}10`,
    borderColor: Colors.light.tint,
  },
  tabIcon: {
    marginRight: 6,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  contentCard: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  basicInfoCard: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  basicInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  basicInfoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginRight: 12,
  },
  basicInfoTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  basicInfoTypeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  basicInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  basicInfoItem: {
    width: '50%',
    marginBottom: 12,
    paddingRight: 8,
  },
  basicInfoLabel: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  basicInfoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    paddingBottom: 8,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    width: 120,
    fontWeight: '600',
    opacity: 0.8,
  },
  infoValue: {
    flex: 1,
  },
  partiesContainer: {
    marginTop: 8,
  },
  partySection: {
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  partySectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
  },
  partyItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  partyTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  partyTagText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  partyItem: {
    fontSize: 14,
    flex: 1,
  },
  emptyText: {
    fontStyle: 'italic',
    opacity: 0.7,
  },
  davacıTag: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
  },
  davalıTag: {
    backgroundColor: 'rgba(239, 68, 68, 0.2)',
  },
  cocukTag: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
  },
  vekilTag: {
    backgroundColor: 'rgba(139, 92, 246, 0.2)',
  },
  defaultTag: {
    backgroundColor: 'rgba(107, 114, 128, 0.2)',
  },
});
