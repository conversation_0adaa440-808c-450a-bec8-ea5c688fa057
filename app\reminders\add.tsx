import { StyleSheet, ScrollView, TouchableOpacity, View, TextInput, Switch, Alert, Platform, ActivityIndicator } from 'react-native';
import React, { useState } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import DateTimePicker from '@react-native-community/datetimepicker';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import reminderService from '@/services/reminderService';

export default function AddReminderScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('MEDIUM');
  const [dueDate, setDueDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [repeat, setRepeat] = useState(0); // Saat cinsinden tekrarlama

  // API state
  const [loading, setLoading] = useState(false);

  // Form validation
  const [errors, setErrors] = useState({
    title: '',
  });

  // Form doğrulama
  const validateForm = () => {
    let isValid = true;
    const newErrors = { title: '' };

    if (!title.trim()) {
      newErrors.title = 'Başlık gereklidir';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Tarih seçici değişikliği
  const onDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || dueDate;
    setShowDatePicker(Platform.OS === 'ios');
    
    // Saati koruyarak tarihi güncelle
    const updatedDate = new Date(currentDate);
    updatedDate.setHours(dueDate.getHours(), dueDate.getMinutes());
    setDueDate(updatedDate);
  };

  // Saat seçici değişikliği
  const onTimeChange = (event, selectedTime) => {
    const currentTime = selectedTime || dueDate;
    setShowTimePicker(Platform.OS === 'ios');
    
    // Tarihi koruyarak saati güncelle
    const updatedDate = new Date(dueDate);
    updatedDate.setHours(currentTime.getHours(), currentTime.getMinutes());
    setDueDate(updatedDate);
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Saat formatla
  const formatTime = (date) => {
    return date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
  };

  // Kaydet
  const handleSave = async () => {
    if (validateForm()) {
      try {
        setLoading(true);

        // API'ye gönderilecek hatırlatıcı verisi
        const reminderData = {
          title,
          description,
          priority,
          dueDate: dueDate.toISOString(),
          repeatIntervalInHours: repeat
        };

        // API'ye gönder
        const response = await reminderService.createReminder(reminderData);

        console.log('Hatırlatıcı oluşturuldu:', response);

        Alert.alert(
          'Başarılı',
          'Hatırlatıcı başarıyla eklendi',
          [
            {
              text: 'Tamam',
              onPress: () => router.back()
            }
          ]
        );
      } catch (err) {
        console.error('Error creating reminder:', err);
        Alert.alert('Hata', 'Hatırlatıcı eklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Yeni Hatırlatıcı</ThemedText>
        <TouchableOpacity onPress={handleSave} disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
          ) : (
            <Ionicons name="checkmark" size={24} color={Colors[colorScheme ?? 'light'].tint} />
          )}
        </TouchableOpacity>
      </ThemedView>

      <ScrollView style={styles.scrollView}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Başlık <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.title ? styles.inputError : null
              ]}
              placeholder="Hatırlatıcı başlığı"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={title}
              onChangeText={setTitle}
            />
            {errors.title ? <ThemedText style={styles.errorText}>{errors.title}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Açıklama</ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                { height: 100, textAlignVertical: 'top' }
              ]}
              placeholder="Hatırlatıcı açıklaması"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
            />
          </View>
        </BlurView>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Öncelik</ThemedText>
            <View style={styles.segmentedControl}>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  priority === 'LOW' && styles.segmentButtonActive
                ]}
                onPress={() => setPriority('LOW')}
              >
                <Ionicons
                  name="flag-outline"
                  size={16}
                  color={priority === 'LOW' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                />
                <ThemedText style={priority === 'LOW' ? styles.segmentTextActive : null}>
                  Düşük
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  priority === 'MEDIUM' && styles.segmentButtonActive
                ]}
                onPress={() => setPriority('MEDIUM')}
              >
                <Ionicons
                  name="flag-outline"
                  size={16}
                  color={priority === 'MEDIUM' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                />
                <ThemedText style={priority === 'MEDIUM' ? styles.segmentTextActive : null}>
                  Orta
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  priority === 'HIGH' && styles.segmentButtonActive
                ]}
                onPress={() => setPriority('HIGH')}
              >
                <Ionicons
                  name="flag-outline"
                  size={16}
                  color={priority === 'HIGH' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                />
                <ThemedText style={priority === 'HIGH' ? styles.segmentTextActive : null}>
                  Yüksek
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  priority === 'CRITICAL' && styles.segmentButtonActive
                ]}
                onPress={() => setPriority('CRITICAL')}
              >
                <Ionicons
                  name="flag-outline"
                  size={16}
                  color={priority === 'CRITICAL' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                />
                <ThemedText style={priority === 'CRITICAL' ? styles.segmentTextActive : null}>
                  Kritik
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Tarih</ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.dateInput]}
              onPress={() => setShowDatePicker(true)}
            >
              <ThemedText>{formatDate(dueDate)}</ThemedText>
              <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={dueDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={onDateChange}
                minimumDate={new Date()}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Saat</ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.dateInput]}
              onPress={() => setShowTimePicker(true)}
            >
              <ThemedText>{formatTime(dueDate)}</ThemedText>
              <Ionicons name="time-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
            {showTimePicker && (
              <DateTimePicker
                value={dueDate}
                mode="time"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={onTimeChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Tekrarlama</ThemedText>
            <View style={styles.repeatSelector}>
              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 0 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(0)}
              >
                <ThemedText style={repeat === 0 ? styles.repeatTextActive : null}>
                  Yok
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 24 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(24)}
              >
                <ThemedText style={repeat === 24 ? styles.repeatTextActive : null}>
                  Günlük
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 168 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(168)}
              >
                <ThemedText style={repeat === 168 ? styles.repeatTextActive : null}>
                  Haftalık
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 720 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(720)}
              >
                <ThemedText style={repeat === 720 ? styles.repeatTextActive : null}>
                  Aylık
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Ionicons name="save-outline" size={20} color="white" />
              <ThemedText style={styles.saveButtonText}>Kaydet</ThemedText>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  requiredStar: {
    color: '#F44336',
  },
  input: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#000',
  },
  inputDark: {
    color: '#fff',
  },
  inputError: {
    borderColor: '#F44336',
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  segmentButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 4,
  },
  segmentButtonActive: {
    backgroundColor: Colors.light.tint,
  },
  segmentTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  repeatSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  repeatButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  repeatButtonActive: {
    backgroundColor: Colors.light.tint,
    borderColor: Colors.light.tint,
  },
  repeatTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
});
