// Add copy button functionality
function setupCopyButton() {
  const copyButton = document.getElementById('copy-jsessionid');
  if (copyButton) {
    copyButton.addEventListener('click', function() {
      const textarea = document.getElementById('full-jsessionid');
      if (textarea) {
        textarea.select();
        document.execCommand('copy');
        this.textContent = 'Kopyalandı!';
        setTimeout(() => {
          this.textContent = 'Kopyala';
        }, 2000);
      } else {
        console.error('Textarea elementi bulunamadı');
      }
    });
  } else {
    console.error('Kopyala butonu bulunamadı');
  }
}

// Export the function so it can be used in content.js
window.setupCopyButton = setupCopyButton;
