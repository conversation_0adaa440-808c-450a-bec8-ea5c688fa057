# HukApp Extension Kurulum Kılavuzu

Bu belge, HukApp Extension'ın nasıl kurulacağını ve test edileceğini açıklar.

## Gereksinimler

- Google Chrome tarayıcısı
- <PERSON>kon dosyaları (icon16.png, icon48.png, icon128.png)

## Kurulum Adımları

1. **İkon Dosyalarını Hazırlama**
   - `icons` klasörüne aşağıdaki boyutlarda ikon dosyalarını ekleyin:
     - icon16.png (16x16 piksel)
     - icon48.png (48x48 piksel)
     - icon128.png (128x128 piksel)
   - Geçici olarak herhangi bir ikon kullanabilirsiniz.

2. **Extension'ı Chrome'a Yükleme**
   - Chrome tarayıcısını açın
   - Adres çubuğuna `chrome://extensions/` yazın ve Enter tuşuna basın
   - Sağ üst köşedeki "Geliştirici modu" seçeneğini etkinleştirin
   - "Paketlenmemiş öğe yükle" butonuna tıklayın
   - `browser-extension` klasörünü seçin

3. **Extension'ı Test Etme**
   - Chrome'da https://onlineislemler.kamusm.gov.tr/landing adresine gidin
   - Sayfanın sağ alt köşesinde HukApp JSESSIONID bilgi kutusu görünecektir
   - JSESSIONID değeri görüntülendiğinde, "Kopyala" butonuna tıklayarak değeri kopyalayabilirsiniz

## Sorun Giderme

- **Extension görünmüyorsa:**
  - Chrome'da `chrome://extensions/` adresine giderek extension'ın etkin olduğundan emin olun
  - Sayfayı yenileyin (F5)

- **JSESSIONID bilgisi görünmüyorsa:**
  - Sayfayı yenileyin
  - Chrome Developer Tools'u açın (F12) ve Console sekmesinde hata mesajlarını kontrol edin

- **İkon dosyaları eksikse:**
  - Extension çalışmaya devam edecektir, ancak Chrome'da uyarı mesajları görebilirsiniz
  - İkon dosyalarını ekledikten sonra extension'ı kaldırıp tekrar yükleyin

## Notlar

- Bu extension şu an test amaçlıdır ve sadece JSESSIONID değerini gösterir
- İleride API entegrasyonu eklenecektir
- Extension'ı kullanırken gizlilik ve güvenlik konularına dikkat edin
