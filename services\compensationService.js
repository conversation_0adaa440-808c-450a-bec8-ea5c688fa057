import apiClient from './api';

// Tazminat hesaplama servisi
const compensationService = {
  // Kıdem ve ihbar tazminatı hesapla
  calculateCompensation: async (compensationData) => {
    try {
      const response = await apiClient.post('/api/user/compensation/calculate', compensationData);
      return response.data;
    } catch (error) {
      console.error('Calculate compensation error:', error);
      throw error;
    }
  }
};

export default compensationService;
