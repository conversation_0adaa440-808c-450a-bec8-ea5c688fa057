import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Modal, TouchableOpacity, FlatList, Dimensions, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import notificationService from '@/services/notificationService';

interface Notification {
  bildirimId: number;
  mesajId: number;
  baslik: string;
  mesaj: string;
  gonderilmeTarihi: string;
  okunduMu: boolean;
  // Opsiyonel alanlar
  type?: 'task' | 'hearing' | 'deadline' | 'system' | 'other';
  data?: any;
}

interface NotificationCenterProps {
  visible: boolean;
  onClose: () => void;
  initialNotifications?: Notification[];
}

export default function NotificationCenter({ visible, onClose, initialNotifications = [] }: NotificationCenterProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (visible) {
      if (initialNotifications && initialNotifications.length > 0) {
        // Header'dan gelen bildirimleri kullan
        setNotifications(initialNotifications);
      } else {
        // Bildirim yoksa API'den getir
        fetchNotifications();
      }
    }
  }, [visible, initialNotifications]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError('');

      // Kullanıcı bildirimlerini al
      console.log('NotificationCenter: Bildirimler getiriliyor...');
      const data = await notificationService.getUserNotifications();

      console.log('NotificationCenter: API yanıtı:', data);

      if (Array.isArray(data) && data.length > 0) {
        console.log(`NotificationCenter: ${data.length} bildirim alındı`);
        console.table(data); // Tablo formatında konsola yazdır
        setNotifications(data);
      } else {
        console.log('NotificationCenter: Bildirim bulunamadı veya boş veri döndü');
        // Fallback data for development
        const fallbackData = notificationService.getSampleNotifications();
        console.log('NotificationCenter: Örnek bildirimler kullanılıyor:', fallbackData.length);
        console.table(fallbackData); // Tablo formatında konsola yazdır
        setNotifications(fallbackData);
      }
    } catch (err) {
      console.error('NotificationCenter: Error fetching notifications:', err);
      console.error('NotificationCenter: Hata detayları:', JSON.stringify(err, null, 2));
      setError('Bildirimler yüklenirken bir hata oluştu.');
      // Fallback data for development
      const fallbackData = notificationService.getSampleNotifications();
      console.log('NotificationCenter: Hata sonrası örnek bildirimler kullanılıyor:', fallbackData.length);
      console.table(fallbackData); // Tablo formatında konsola yazdır
      setNotifications(fallbackData);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (id: string) => {
    try {
      await notificationService.markNotificationAsRead(id);
      setNotifications(notifications.map(notification =>
        notification.bildirimId.toString() === id ? { ...notification, okunduMu: true } : notification
      ));
    } catch (err) {
      console.error(`Error marking notification ${id} as read:`, err);
    }
  };

  // handleMarkAllAsRead ve handleDeleteNotification fonksiyonları kaldırıldı

  const handleNotificationPress = (notification: Notification) => {
    // Mark as read
    if (!notification.okunduMu) {
      handleMarkAsRead(notification.bildirimId.toString());
    }

    // Bildirim içeriğinden dava numarası veya görev ID'si çıkar
    const extractCaseNumber = (text: string) => {
      // Dava numarası formatı: YYYY/XXXX şeklinde
      const caseNumberRegex = /(\d{4}\/\d+)\s+Hukuk\s+Dava\s+Dosyası/i;
      const match = text.match(caseNumberRegex);
      return match ? match[1] : null;
    };

    // Bildirim tipini belirle
    const notificationType = determineNotificationType(notification.baslik);
    const caseNumber = extractCaseNumber(notification.mesaj);

    // Yönlendirme işlemi
    switch (notificationType) {
      case 'hearing':
        if (caseNumber) {
          console.log(`Dava sayfasına yönlendiriliyor: ${caseNumber}`);
          router.push(`/cases?search=${caseNumber}`);
        } else {
          console.log('Davalar sayfasına yönlendiriliyor');
          router.push('/cases');
        }
        break;
      case 'task':
        console.log('Görevler sayfasına yönlendiriliyor');
        router.push('/tasks');
        break;
      case 'deadline':
        console.log('Görevler sayfasına yönlendiriliyor');
        router.push('/tasks');
        break;
      case 'system':
        // Sistem bildirimlerini işle
        console.log('Sistem bildirimi, herhangi bir yönlendirme yapılmadı');
        break;
      default:
        // Varsayılan işlem
        console.log('Varsayılan olarak ana sayfaya yönlendiriliyor');
        router.push('/');
        break;
    }

    // Bildirime tıklayınca ekranı kapatma işlemi kaldırıldı
    // onClose();
  };

  // Bildirim başlığına göre tipini belirle
  const determineNotificationType = (title: string): string => {
    const lowerTitle = title.toLowerCase();

    if (lowerTitle.includes('duruşma') || lowerTitle.includes('durusma')) {
      return 'hearing';
    } else if (lowerTitle.includes('dava') || lowerTitle.includes('dosya')) {
      return 'hearing';
    } else if (lowerTitle.includes('görev') || lowerTitle.includes('gorev')) {
      return 'task';
    } else if (lowerTitle.includes('son tarih') || lowerTitle.includes('deadline') || lowerTitle.includes('süre')) {
      return 'deadline';
    } else if (lowerTitle.includes('sistem') || lowerTitle.includes('güncelleme') || lowerTitle.includes('bakım')) {
      return 'system';
    } else {
      return 'other';
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'task':
        return 'checkmark-circle';
      case 'hearing':
        return 'briefcase';
      case 'deadline':
        return 'alarm';
      case 'system':
        return 'information-circle';
      default:
        return 'notifications';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 60) {
      return `${diffMins} dakika önce`;
    } else if (diffHours < 24) {
      return `${diffHours} saat önce`;
    } else if (diffDays < 7) {
      return `${diffDays} gün önce`;
    } else {
      return date.toLocaleDateString('tr-TR');
    }
  };

  const renderNotificationItem = ({ item }: { item: Notification }) => {
    // Bildirim tipini belirle
    const notificationType = determineNotificationType(item.baslik);

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          isDark ? styles.notificationItemDark : styles.notificationItemLight,
          item.okunduMu && styles.readNotification
        ]}
        onPress={() => handleNotificationPress(item)}
      >
        <View style={styles.notificationIcon}>
          <Ionicons
            name={getNotificationIcon(notificationType)}
            size={24}
            color={item.okunduMu ? (isDark ? '#9ca3af' : '#64748b') : Colors[colorScheme ?? 'light'].tint}
          />
        </View>

        <View style={styles.notificationContent}>
          <ThemedText type="defaultSemiBold" style={styles.notificationTitle}>{item.baslik}</ThemedText>
          <ThemedText style={styles.notificationBody}>{item.mesaj}</ThemedText>
          <ThemedText style={styles.notificationTime}>{formatDate(item.gonderilmeTarihi)}</ThemedText>
        </View>

        {/* Silme butonu kaldırıldı */}
      </TouchableOpacity>
    );
  };

  // Okunmamış bildirim sayısı artık kullanılmıyor
  // const unreadCount = notifications.filter(n => !n.okunduMu).length;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, isDark ? styles.modalContentDark : styles.modalContentLight]}>
          <View style={styles.header}>
            <ThemedText type="subtitle">Bildirimler</ThemedText>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={Colors[colorScheme ?? 'light'].tint} />
            </TouchableOpacity>
          </View>

          {/* Tümünü Okundu İşaretle butonu kaldırıldı */}

          {loading ? (
            <ThemedView style={styles.loadingContainer}>
              <Ionicons name="hourglass-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
              <ThemedText style={styles.loadingText}>Bildirimler yükleniyor...</ThemedText>
            </ThemedView>
          ) : error ? (
            <ThemedView style={styles.errorContainer}>
              <Ionicons name="alert-circle-outline" size={48} color="#EF4444" />
              <ThemedText style={styles.errorText}>{error}</ThemedText>
              <TouchableOpacity style={styles.retryButton} onPress={fetchNotifications}>
                <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          ) : notifications.length === 0 ? (
            <ThemedView style={styles.emptyContainer}>
              <Ionicons name="notifications-off-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
              <ThemedText style={styles.emptyText}>Bildirim bulunmuyor</ThemedText>
            </ThemedView>
          ) : (
            <FlatList
              data={notifications}
              renderItem={renderNotificationItem}
              keyExtractor={item => item.bildirimId.toString()}
              contentContainerStyle={styles.notificationList}
            />
          )}

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <ThemedText style={styles.closeButtonText}>Kapat</ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: height * 0.7,
    borderRadius: 16,
    overflow: 'hidden',
    padding: 16,
  },
  modalContentLight: {
    backgroundColor: '#FFFFFF',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 10,
        }
    ),
    elevation: 5,
  },
  modalContentDark: {
    backgroundColor: '#1F2937',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.3)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 10,
        }
    ),
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.2)',
  },
  // markAllButton ve markAllText stilleri kaldırıldı
  notificationList: {
    paddingVertical: 8,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  notificationItemLight: {
    backgroundColor: 'rgba(100, 100, 255, 0.1)',
  },
  notificationItemDark: {
    backgroundColor: 'rgba(100, 100, 255, 0.15)',
  },
  readNotification: {
    opacity: 0.7,
  },
  notificationIcon: {
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  notificationBody: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 6,
  },
  notificationTime: {
    fontSize: 12,
    opacity: 0.6,
  },
  // deleteButton stili kaldırıldı
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 12,
    opacity: 0.7,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 12,
    marginBottom: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    marginTop: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  closeButton: {
    marginTop: 16,
    backgroundColor: Colors.light.tint,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
