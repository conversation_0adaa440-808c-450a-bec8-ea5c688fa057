import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface TaskEventReminderProps {
  id: string;
  title: string;
  type: 'task' | 'event';
  time: string;
  onDismiss: () => void;
}

export default function TaskEventReminder({ id, title, type, time, onDismiss }: TaskEventReminderProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const handleView = () => {
    if (type === 'task') {
      router.push(`/tasks/${id}`);
    } else {
      router.push(`/events/${id}`);
    }
    onDismiss();
  };
  
  const getIcon = () => {
    if (type === 'task') {
      return 'checkmark-circle';
    } else {
      return 'calendar';
    }
  };
  
  return (
    <Modal
      transparent
      animationType="fade"
      visible={true}
      onRequestClose={onDismiss}
    >
      <View style={styles.modalContainer}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.reminderCard}>
          <View style={styles.iconContainer}>
            <Ionicons name={getIcon()} size={32} color={Colors[colorScheme ?? 'light'].tint} />
          </View>
          
          <ThemedText type="subtitle" style={styles.title}>
            {type === 'task' ? 'Görev Hatırlatıcısı' : 'Etkinlik Hatırlatıcısı'}
          </ThemedText>
          
          <ThemedText style={styles.taskTitle}>{title}</ThemedText>
          
          <ThemedText style={styles.timeText}>
            {time}
          </ThemedText>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
              <ThemedText style={styles.dismissButtonText}>Kapat</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.viewButton} onPress={handleView}>
              <ThemedText style={styles.viewButtonText}>Görüntüle</ThemedText>
            </TouchableOpacity>
          </View>
        </BlurView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  reminderCard: {
    width: '80%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(100, 100, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  taskTitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  timeText: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  dismissButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.tint,
  },
  dismissButtonText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  viewButton: {
    backgroundColor: Colors.light.tint,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  viewButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
