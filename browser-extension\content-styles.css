#hukapp-jsession-info {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-family: Arial, sans-serif;
  font-size: 12px;
  z-index: 9999;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: opacity 0.3s;
  opacity: 0.8;
}

#hukapp-jsession-info:hover {
  opacity: 1;
}

.jsession-title {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.jsession-close-button {
  cursor: pointer;
  margin-left: 10px;
  font-size: 18px;
}

.jsession-item {
  margin-bottom: 5px;
}

.jsession-label {
  font-weight: bold;
}

.jsession-value {
  word-break: break-all;
}

.jsession-timestamp {
  font-size: 10px;
  color: #ccc;
}

.jsession-textarea-container {
  margin-top: 10px;
}

.jsession-textarea {
  width: 100%;
  height: 40px;
  font-size: 10px;
  padding: 5px;
  resize: none;
  word-break: break-all;
}

.jsession-copy-button {
  margin-left: 5px;
  padding: 2px 5px;
  font-size: 10px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}
