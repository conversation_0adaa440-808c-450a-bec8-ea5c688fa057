import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Modal, TouchableOpacity, FlatList, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import TaskReminder from './TaskReminder';
import EventReminder from './EventReminder';

// Reminder types
interface BaseReminder {
  id: string;
  title: string;
  dueDate: string;
}

interface TaskReminderItem extends BaseReminder {
  type: 'task';
  description?: string;
  priority: 'DÜŞÜK' | 'ORTA' | 'YÜKSEK' | 'KRİTİK';
  taskType: 'GÖREV' | 'DURUŞMA' | 'TOPLANTI' | 'HATIRLATMA';
}

interface EventReminderItem extends BaseReminder {
  type: 'event';
  eventType: 'Hearing' | 'Deposition' | 'Client Meeting' | 'Deadline' | 'Other';
  time: string;
  location?: string;
  caseId?: string;
  caseName?: string;
}

type ReminderItem = TaskReminderItem | EventReminderItem;

interface TaskReminderManagerProps {
  visible: boolean;
  onClose: () => void;
  reminders: ReminderItem[];
  onDismissReminder: (id: string) => void;
  onSnoozeReminder?: (id: string, minutes: number) => void;
}

export default function TaskReminderManager({
  visible,
  onClose,
  reminders,
  onDismissReminder,
  onSnoozeReminder
}: TaskReminderManagerProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [activeReminders, setActiveReminders] = useState<ReminderItem[]>([]);
  
  useEffect(() => {
    setActiveReminders(reminders);
  }, [reminders]);
  
  const handleDismiss = (id: string) => {
    setActiveReminders(activeReminders.filter(reminder => reminder.id !== id));
    onDismissReminder(id);
  };
  
  const handleSnooze = (id: string) => {
    if (onSnoozeReminder) {
      onSnoozeReminder(id, 15); // Default snooze for 15 minutes
    }
    setActiveReminders(activeReminders.filter(reminder => reminder.id !== id));
  };
  
  const renderReminder = ({ item }: { item: ReminderItem }) => {
    if (item.type === 'task') {
      return (
        <TaskReminder
          id={item.id}
          title={item.title}
          description={item.description}
          dueDate={item.dueDate}
          priority={item.priority}
          type={item.taskType}
          onDismiss={() => handleDismiss(item.id)}
          onSnooze={() => handleSnooze(item.id)}
        />
      );
    } else {
      return (
        <EventReminder
          id={item.id}
          title={item.title}
          eventType={item.eventType}
          date={new Date(item.dueDate).toLocaleDateString('tr-TR')}
          time={item.time}
          location={item.location}
          caseId={item.caseId}
          caseName={item.caseName}
          onDismiss={() => handleDismiss(item.id)}
        />
      );
    }
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.modalContent}>
          <View style={styles.header}>
            <ThemedText type="subtitle">Hatırlatıcılar</ThemedText>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={Colors[colorScheme ?? 'light'].tint} />
            </TouchableOpacity>
          </View>
          
          {activeReminders.length > 0 ? (
            <FlatList
              data={activeReminders}
              renderItem={renderReminder}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.reminderList}
            />
          ) : (
            <ThemedView style={styles.emptyContainer}>
              <Ionicons name="notifications-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
              <ThemedText style={styles.emptyText}>Aktif hatırlatıcı bulunmuyor</ThemedText>
            </ThemedView>
          )}
          
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <ThemedText style={styles.closeButtonText}>Kapat</ThemedText>
          </TouchableOpacity>
        </BlurView>
      </View>
    </Modal>
  );
}

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: height * 0.7,
    borderRadius: 16,
    overflow: 'hidden',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.2)',
  },
  reminderList: {
    paddingVertical: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    marginTop: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  closeButton: {
    marginTop: 16,
    backgroundColor: Colors.light.tint,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
