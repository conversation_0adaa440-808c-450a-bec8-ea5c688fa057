/**
 * Logger initialization utility
 * This file is imported at app startup to configure the logger
 */

import { Platform } from 'react-native';
import logger from './logger';

/**
 * Initialize the logger with appropriate settings based on environment
 */
export function initLogger() {
  // Determine if we're in development or production
  const isDev = process.env.NODE_ENV === 'development';
  
  // Configure logger based on platform and environment
  logger.configure({
    // In production, disable most logs on mobile
    enableMobileLogs: isDev,
    
    // Always enable web logs in development, but limit them in production
    enableWebLogs: true,
    
    // Configure log levels
    levels: {
      debug: isDev,
      info: true,
      warn: true,
      error: true,
      api: isDev && Platform.OS === 'web', // Only show API logs in web development
    }
  });
  
  // Log initialization
  logger.info(`Logger initialized: ${Platform.OS} in ${isDev ? 'development' : 'production'} mode`);
}

// Export a function to temporarily enable all logs (useful for debugging)
export function enableAllLogs() {
  logger.configure({
    enableMobileLogs: true,
    enableWebLogs: true,
    levels: {
      debug: true,
      info: true,
      warn: true,
      error: true,
      api: true,
    }
  });
  logger.info('All logs enabled temporarily');
}

// Export a function to disable verbose logs (useful after debugging)
export function disableVerboseLogs() {
  logger.configure({
    enableMobileLogs: false,
    enableWebLogs: true,
    levels: {
      debug: false,
      info: true,
      warn: true,
      error: true,
      api: false,
    }
  });
  logger.info('Verbose logs disabled');
}
