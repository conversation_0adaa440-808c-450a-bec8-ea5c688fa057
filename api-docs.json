{"openapi": "3.0.1", "info": {"title": "HUKAPP Service API", "version": "v1"}, "servers": [{"url": "http://localhost:4244", "description": "Generated server url"}], "tags": [{"name": "Case Notes", "description": "API for managing case notes"}], "paths": {"/api/user/office/transactions/types/{id}": {"get": {"tags": ["transaction-types-controller"], "summary": "Get transaction type by id", "operationId": "getTransactionTypeById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["transaction-types-controller"], "summary": "Update an existing transaction type", "operationId": "updateTransactionType", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionTypeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["transaction-types-controller"], "summary": "Delete an existing transaction type", "operationId": "deleteTransactionType", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense/{id}": {"put": {"tags": ["income-expense-transactions-controller"], "summary": "Update Income/Expense transaction by id", "operationId": "putMethodName", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["income-expense-transactions-controller"], "summary": "Delete an Income/Expense transaction by id", "operationId": "deleteMethodName", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/notes/{id}": {"get": {"tags": ["Case Notes"], "summary": "Get a specific case note", "description": "Retrieves a specific case note by its ID", "operationId": "getCaseNoteById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Case Notes"], "summary": "Update a case note", "description": "Updates an existing case note", "operationId": "updateCaseNote", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseNoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Case Notes"], "summary": "Delete a case note", "description": "Deletes a case note by its ID", "operationId": "deleteCaseNote", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/tasks/{id}": {"get": {"tags": ["task-controller"], "summary": "Get a task by id of the authenticated user", "operationId": "getTaskById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["task-controller"], "summary": "Update an existing task of the authenticated user by id", "operationId": "updateTask", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["task-controller"], "summary": "Delete a task of the authenticated user by id", "operationId": "deleteTask", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/tasks/notes/{noteId}": {"put": {"tags": ["task-controller"], "summary": "Updates a note of the authenticated user by id", "operationId": "updateNote", "parameters": [{"name": "noteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["task-controller"], "summary": "Delete a note of the authenticated user by id", "operationId": "deleteNote", "parameters": [{"name": "noteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/reminders/{id}": {"get": {"tags": ["reminder-controller"], "summary": "Get a reminder of the authenticated user by id", "operationId": "getReminderById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReminderResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["reminder-controller"], "summary": "Update a reminder of the authenticated user by id", "operationId": "updateReminder", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReminderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReminderResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["reminder-controller"], "summary": "Delete a reminder of the authenticated user by id", "operationId": "deleteReminder", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/auth/user/login": {"post": {"tags": ["person-controller"], "summary": "Login a person", "description": "Logs in a person with the provided credentials", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successfully logged in", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonLoginResponse"}}}}, "401": {"description": "Invalid credentials"}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "400": {"description": "Invalid input"}}}}, "/auth/user/forgot-password/validate-otp": {"post": {"tags": ["person-controller"], "summary": "Validate OTP", "description": "Validates the provided OTP for the given email", "operationId": "validateOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateOtpRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid OTP"}, "200": {"description": "OTP is valid"}}}}, "/auth/user/forgot-password/update-password": {"post": {"tags": ["person-controller"], "summary": "Update password", "description": "Updates the password for the given email", "operationId": "updatePassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input"}, "200": {"description": "Password updated successfully"}}}}, "/auth/user/forgot-password/send-otp": {"post": {"tags": ["person-controller"], "summary": "Forgot password. Generates an OTP and sends it to the provided email", "description": "If the user exists, sends a random 8-digit OTP to email with 3 minutes expiration", "operationId": "forgotPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input"}, "200": {"description": "OTP sent"}}}}, "/auth/user/create": {"post": {"tags": ["person-controller"], "summary": "Create a new person", "description": "Creates a new person with the provided details", "operationId": "create<PERSON>erson", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonCreateRequest"}}}, "required": true}, "responses": {"201": {"description": "Successfully created person", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonCreateResponse"}}}}, "409": {"description": "<PERSON><PERSON><PERSON> eden kayıt."}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "400": {"description": "Invalid input"}}}}, "/api/user/sync": {"post": {"tags": ["sync-controller"], "summary": "Sync UYAP", "description": "Sync to UYAP system using provided JSID. Requires authentication with <PERSON><PERSON> token", "operationId": "syncToUyap", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/types": {"get": {"tags": ["transaction-types-controller"], "summary": "Get all transaction types. You can also filter by category", "operationId": "getTransactionTypes", "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "enum": ["INCOME", "EXPENSE"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["transaction-types-controller"], "summary": "Create Transaction Type", "description": "Create a new transaction type", "operationId": "createTransactionType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionTypeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense": {"get": {"tags": ["income-expense-transactions-controller"], "summary": "Get all Income/Expense transactions, you can filter by category", "operationId": "getAllTransactions", "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "enum": ["INCOME", "EXPENSE"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["income-expense-transactions-controller"], "summary": "Save Income/Expense", "description": "Create a new income or expense", "operationId": "saveIncomeExpense", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/compensation/calculate": {"post": {"tags": ["compensation-calculator-controller"], "summary": "Calculate severance/notice compensation", "operationId": "postMethodName", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompensationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CompensationResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/notes": {"get": {"tags": ["Case Notes"], "summary": "Get all case notes", "description": "Retrieves all case notes for the authenticated user", "operationId": "getAllCaseNotes", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Case Notes"], "summary": "Create a new case note", "description": "Creates a new note for a specific case", "operationId": "createCaseNote", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseNoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/tasks": {"get": {"tags": ["task-controller"], "summary": "Get all tasks of the authenticated user", "operationId": "getAllTasks", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["task-controller"], "summary": "Create a new task", "operationId": "createTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/tasks/{taskId}/notes": {"post": {"tags": ["task-controller"], "summary": "Create a new note for a task of the authenticated user", "operationId": "createNoteForTask", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/reminders": {"get": {"tags": ["reminder-controller"], "summary": "Get all reminders of the authenticated user", "operationId": "getAllReminders", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReminderResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["reminder-controller"], "summary": "Create a new reminder for the authenticated user", "operationId": "createReminder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReminderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReminderResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/auth/user": {"get": {"tags": ["person-controller"], "summary": "Get all persons. Development purpose only. Shouldn't exist on production environment", "description": "Returns a list of all persons", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"204": {"description": "No content found"}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "200": {"description": "Successfully retrieved list", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Person"}}}}}}}}, "/auth/user/{id}": {"get": {"tags": ["person-controller"], "summary": "Get a person by TCKN. Development purpose only. Shouldn't exist on production environment", "description": "Returns a person based on TCKN", "operationId": "getPersonByIdentityNumber", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "404": {"description": "Person not found"}, "200": {"description": "Successfully retrieved person", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Person"}}}}}}}, "/api/user/trials": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user trials", "operationId": "getUserTrials", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/photo": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user photo", "operationId": "getUserPhoto", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPhotoResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense/currencies": {"get": {"tags": ["income-expense-transactions-controller"], "summary": "Get Currency List", "description": "Get available currencies", "operationId": "getCurrencyList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyDto"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/notifications": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user notifications", "operationId": "getUserNotifications", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/details": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user details", "operationId": "getUserDetails", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user cases by case status", "operationId": "getUserCases", "parameters": [{"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/taraflar": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case parties", "operationId": "getCaseParties", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/taraflar-all": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get all parties", "operationId": "getAllParties", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/tahsilat-reddiyat": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case collections and denials", "operationId": "getCaseCollectionAndDenial", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/safahat": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case history", "operationId": "getCaseHistory", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/notes/case": {"get": {"tags": ["Case Notes"], "summary": "Get notes for a specific case", "description": "Retrieves all notes for a specific case number", "operationId": "getCaseNotesByCaseNumber", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/case-detail": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case details", "operationId": "getCase", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/announcements": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get UYAP announcements", "operationId": "getUyapAnnouncements", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"TransactionTypeRequest": {"required": ["category", "name"], "type": "object", "properties": {"name": {"maxLength": 2147483647, "minLength": 2, "type": "string", "description": "Transaction type name", "example": "Electric Bill"}, "category": {"type": "string", "description": "Transaction Type category", "example": "EXPENSE", "enum": ["INCOME", "EXPENSE"]}}, "description": "Transaction Type Create Or Update Request"}, "TransactionTypeResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "category": {"type": "string", "enum": ["INCOME", "EXPENSE"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerName": {"type": "string"}}, "description": "Transaction Type Create Or Update Response"}, "TransactionRequest": {"required": ["amount", "transactionDate", "transactionTypeId"], "type": "object", "properties": {"transactionTypeId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "amount": {"minimum": 1, "type": "number"}, "transactionDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}}, "description": "Transaction Create Or Update Request"}, "TransactionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "amount": {"type": "number"}, "transactionDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerName": {"type": "string"}, "ownerEmail": {"type": "string"}, "transactionType": {"$ref": "#/components/schemas/TransactionTypeResponse"}}, "description": "Transaction Create Or Update Response"}, "CaseNoteRequest": {"required": ["caseNumber", "content"], "type": "object", "properties": {"content": {"type": "string", "description": "Content of the note", "example": "This is a sample case note content."}, "caseNumber": {"type": "string", "description": "Case number to which this note belongs", "example": "2023/123"}}, "description": "Request object for creating or updating a case note"}, "CaseNoteResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "content": {"type": "string"}, "caseNumber": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerId": {"type": "integer", "format": "int64"}, "ownerName": {"type": "string"}, "ownerEmail": {"type": "string"}}, "description": "Response object for case note operations"}, "TaskRequest": {"required": ["description", "dueDate", "priority", "status", "title"], "type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "startDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}, "status": {"type": "string", "enum": ["OPEN", "IN_PROGRESS", "COMPLETED", "CANCELLED"]}}, "description": "Request for creating or updating a task"}, "NoteResponse": {"required": ["content"], "type": "object", "properties": {"content": {"type": "string", "description": "Content of the note", "example": "This is a sample note content."}, "id": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "description": "Response object for creating or updating a note"}, "TaskResponse": {"required": ["description", "priority", "status", "title"], "type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "startDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}, "status": {"type": "string", "enum": ["OPEN", "IN_PROGRESS", "COMPLETED", "CANCELLED"]}, "id": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "reporter": {"type": "integer", "format": "int64"}, "notes": {"type": "array", "items": {"$ref": "#/components/schemas/NoteResponse"}}}, "description": "Response for creating or updating a task"}, "NoteRequest": {"required": ["content"], "type": "object", "properties": {"content": {"type": "string", "description": "Content of the note", "example": "This is a sample note content."}}, "description": "Request object for creating or updating a note"}, "ReminderRequest": {"required": ["description", "dueDate", "priority", "repeatIntervalInHours", "title"], "type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "dueDate": {"type": "string", "format": "date-time"}, "repeatIntervalInHours": {"minimum": 1, "type": "integer", "format": "int64"}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}}}, "ReminderResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "description": {"type": "string"}, "dueDate": {"type": "string", "format": "date-time"}, "repeatIntervalInHours": {"type": "integer", "format": "int32"}, "priority": {"type": "string"}, "reporterId": {"type": "integer", "format": "int64"}}, "description": "Response for creating or updating a reminder"}, "PersonLoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"password": {"pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\w\\s])\\S{8,50}$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "********"}, "email": {"type": "string", "description": "Person's email address", "example": "<EMAIL>"}}, "description": "Person Data Transfer Object for Login"}, "PersonLoginResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "surname": {"type": "string"}, "isNewUser": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "jwt": {"type": "string"}}, "description": "Person Response DTO"}, "ValidateOtpRequest": {"required": ["otpEmail", "otpValue"], "type": "object", "properties": {"otpValue": {"maxLength": 8, "minLength": 8, "type": "string"}, "otpEmail": {"type": "string"}}}, "UpdatePasswordRequest": {"required": ["password"], "type": "object", "properties": {"password": {"pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\w\\s])\\S{8,50}$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "********"}, "validatedOtp": {"$ref": "#/components/schemas/ValidateOtpRequest"}}}, "ForgotPasswordRequest": {"required": ["email"], "type": "object", "properties": {"email": {"type": "string"}}}, "PersonCreateRequest": {"required": ["birthDate", "email", "identityNumber", "mobilePhone", "name", "password", "surname"], "type": "object", "properties": {"password": {"pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\w\\s])\\S{8,50}$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "********"}, "email": {"type": "string", "description": "Person's email address", "example": "<EMAIL>"}, "name": {"maxLength": 50, "minLength": 2, "type": "string", "description": "Person's name", "example": "<PERSON>"}, "surname": {"maxLength": 50, "minLength": 2, "type": "string", "description": "Person's surname", "example": "<PERSON><PERSON>"}, "personInfoText": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Additional information about the person"}, "identityNumber": {"type": "integer", "description": "Person's identity number (TCKN)", "format": "int64", "example": 12345678901}, "birthDate": {"type": "string", "description": "Person's birth date", "format": "date", "example": "1990-01-30"}, "mobilePhone": {"maxLength": 10, "minLength": 10, "type": "string", "description": "Person's mobile phone number", "example": "5551234567"}}, "description": "Person Data Transfer Object"}, "PersonCreateResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "surname": {"type": "string"}, "personInfoText": {"type": "string"}, "identityNumber": {"type": "integer", "format": "int64"}, "birthDate": {"type": "string", "format": "date"}, "email": {"type": "string"}, "mobilePhone": {"type": "string"}, "isNewUser": {"type": "boolean"}, "isDeleted": {"type": "boolean"}}, "description": "Person Response DTO"}, "SyncRequest": {"required": ["jsid"], "type": "object", "properties": {"jsid": {"type": "string"}}}, "BaseResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}, "CompensationRequest": {"required": ["endDate", "grossSalary", "startDate"], "type": "object", "properties": {"startDate": {"type": "string", "description": "Start date for the compensation calculation", "format": "date", "example": "2025-01-15"}, "endDate": {"type": "string", "description": "End date for the compensation calculation", "format": "date", "example": "2025-01-15"}, "grossSalary": {"type": "number", "description": "Base salary", "example": 30000}, "cumulativeIncomeTaxBasis": {"type": "number", "description": "Kümülatif gelir vergisi matrahı", "example": 100000}}, "description": "Request for calculating compensation"}, "CompensationResponse": {"type": "object", "properties": {"totalDays": {"type": "integer", "format": "int32"}, "grossSeverancePay": {"type": "string"}, "severancePayStampTax": {"type": "string"}, "netSeverancePay": {"type": "string"}, "noticePeriodInDays": {"type": "integer", "format": "int32"}, "jobSearchLeaveHours": {"type": "integer", "format": "int32"}, "grossNoticePay": {"type": "string"}, "noticePayStampTax": {"type": "string"}, "noticePayIncomeTax": {"type": "string"}, "netNoticePay": {"type": "string"}, "totalCompensation": {"type": "string"}, "severanceNoticeText": {"type": "string"}}, "description": "Response for calculating compensation"}, "Person": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "isDeleted": {"type": "boolean"}, "version": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "surname": {"type": "string"}, "personInfoText": {"type": "string"}, "identityNumber": {"type": "integer", "format": "int64"}, "birthDate": {"type": "string", "format": "date"}, "email": {"type": "string"}, "isEmailVerified": {"type": "boolean"}, "mobilePhone": {"type": "string"}, "isMobilePhoneVerified": {"type": "boolean"}, "password": {"type": "string"}, "isNewUser": {"type": "boolean"}}}, "UserPhotoResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "photo": {"type": "string"}}}, "CurrencyDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}