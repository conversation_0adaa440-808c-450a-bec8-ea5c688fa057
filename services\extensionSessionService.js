import apiClient from './api';

// Extension session servisi
const extensionSessionService = {
  // JSESSIONID'yi kaydet
  saveSession: async (sessionData) => {
    try {
      console.log('Saving JSESSIONID:', sessionData);
      
      // API endpoint'e istek gönder
      const response = await apiClient.post('/api/extension/session', sessionData);
      
      console.log('JSESSIONID saved successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Save JSESSIONID error:', error);
      throw error;
    }
  },
  
  // Kullanıcının aktif JSESSIONID'sini getir
  getActiveSession: async (userId) => {
    try {
      const response = await apiClient.get(`/api/extension/session?userId=${userId}`);
      return response.data;
    } catch (error) {
      console.error('Get active session error:', error);
      throw error;
    }
  },
  
  // JSESSIONID'nin durumunu kontrol et
  checkSessionStatus: async (sessionId) => {
    try {
      const response = await apiClient.get(`/api/extension/session/status?sessionId=${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Check session status error:', error);
      throw error;
    }
  }
};

export default extensionSessionService;
