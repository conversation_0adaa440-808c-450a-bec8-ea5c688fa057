import { StyleSheet, TouchableOpacity, FlatList, View, TextInput, ActivityIndicator, Platform, ScrollView, ImageBackground } from 'react-native';
import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { router, usePathname } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '@/utils/logger';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useCaseParties } from '@/contexts/CasePartiesContext';
import caseService from '@/services/caseService';
import Pagination from '@/components/Pagination';
import Footer from '@/components/layout/Footer';

// Dava veri tipi tanımı
interface CaseItem {
  id: number;
  dosyaId: string;
  dosyaNo: string;
  esasYil: string;
  dosyaNumara: string;
  birimAdi: string;
  dosyaTur: string;
  dosyaDurum: string;
  acilisTarihi: string;
  status: 'Active' | 'Closed'; // Aktif veya kapalı durumu
}

// Fallback dava verileri (API bağlantısı başarısız olduğunda kullanılır)
const CASES_DATA: CaseItem[] = [];

export default function CasesScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const pathname = usePathname(); // Get current pathname for navigation detection
  const [activeFilter, setActiveFilter] = useState('All'); // All, Active, Closed
  const [sortBy, setSortBy] = useState('dosyaNo');
  const [sortOrder, setSortOrder] = useState('desc'); // En güncel dosya en üstte olsun
  const [currentPage, setCurrentPage] = useState(1);
  const [searchText, setSearchText] = useState('');
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [isSortModalVisible, setIsSortModalVisible] = useState(false);
  const itemsPerPage = 10; // Her sayfada 10 kart gösterilecek
  const flatListRef = useRef<FlatList>(null); // FlatList için ref

  // Expanded card state
  const [expandedCardIds, setExpandedCardIds] = useState<string[]>([]);

  // API state
  const [cases, setCases] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [needsReset, setNeedsReset] = useState(false);

  // Taraflar context'ini kullan
  const { allParties, loading: partiesLoading, fetchPartiesIfNeeded, getPartiesForCase } = useCaseParties();

  // Dosya numarasını formatla
  const formatCaseNumber = (item: any): string => {
    // Esas yıl ve dosya numarası varsa kullan
    if (item.esasYil && item.dosyaNumara) {
      return `${item.esasYil}/${item.dosyaNumara}`;
    }

    // DosyaNo varsa parse et
    if (item.dosyaNo && item.dosyaNo.includes('/')) {
      const parts = item.dosyaNo.split('/');
      if (parts.length >= 2) {
        return item.dosyaNo; // Zaten formatlı ise olduğu gibi döndür
      }
    }

    // Alternatif format: 2023-123 gibi
    if (item.dosyaNo && item.dosyaNo.includes('-')) {
      return item.dosyaNo.replace('-', '/');
    }

    // Hiçbir format bulunamazsa dosyaNo'yu olduğu gibi döndür
    return item.dosyaNo || 'Belirtilmemiş';
  };

  // Get case type color
  const getCaseTypeColor = (type: string): string => {
    // Dava türüne göre renk belirle
    if (type.includes('Ceza')) {
      return '#EF4444'; // Red
    } else if (type.includes('Hukuk')) {
      return '#10B981'; // Green
    } else if (type.includes('Aile')) {
      return '#3B82F6'; // Blue
    } else if (type.includes('Miras')) {
      return '#8B5CF6'; // Purple
    } else if (type.includes('İflas') || type.includes('Ticaret')) {
      return '#F59E0B'; // Amber
    } else if (type.includes('İş')) {
      return '#EC4899'; // Pink
    } else if (type.includes('Talimat')) {
      return '#14B8A6'; // Teal
    } else if (type.includes('Sulh')) {
      return '#6366F1'; // Indigo
    } else if (type.includes('CBS') || type.includes('Soruşturma')) {
      return '#F43F5E'; // Rose
    } else if (type.includes('İcra')) {
      return '#F97316'; // Orange
    } else {
      return '#6B7280'; // Gray
    }
  };



  // Sıralama fonksiyonu
  const sortCases = (cases: any[]) => {
    if (!Array.isArray(cases) || cases.length === 0) {
      return [];
    }

    return [...cases].sort((a, b) => {
      if (!a || !b) return 0;

      let valueA: string | number;
      let valueB: string | number;

      // Sıralama alanına göre değerleri al
      switch (sortBy) {
        case 'dosyaNo':
          valueA = String(a.dosyaNo || '');
          valueB = String(b.dosyaNo || '');
          break;
        case 'birimAdi':
          valueA = String(a.birimAdi || '').toLowerCase();
          valueB = String(b.birimAdi || '').toLowerCase();
          break;
        case 'dosyaTur':
          valueA = String(a.dosyaTur || '').toLowerCase();
          valueB = String(b.dosyaTur || '').toLowerCase();
          break;
        case 'esasYil':
          valueA = Number(a.esasYil || 0);
          valueB = Number(b.esasYil || 0);
          break;
        default:
          valueA = String(a.dosyaNo || '');
          valueB = String(b.dosyaNo || '');
      }

      // Sıralama yönüne göre karşılaştır
      if (sortBy === 'esasYil') {
        // Sayısal değerler için
        return sortOrder === 'asc'
          ? (valueA as number) - (valueB as number)
          : (valueB as number) - (valueA as number);
      } else {
        // Metin değerleri için
        if (sortOrder === 'asc') {
          return (valueA as string).localeCompare(valueB as string);
        } else {
          return (valueB as string).localeCompare(valueA as string);
        }
      }
    });
  };

  // Add custom CSS to remove focus outline and add transitions
  useEffect(() => {
    if (Platform.OS === 'web') {
      // Create a style element
      const style = document.createElement('style');
      style.textContent = `
        .search-input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
        input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
        .search-container {
          transition: all 0.3s ease !important;
        }
        .search-container:focus-within {
          border-color: ${Colors.light.tint} !important;
          box-shadow: 0 0 0 1px ${Colors.light.tint} !important;
        }
      `;
      // Add it to the document head
      document.head.appendChild(style);

      // Clean up when component unmounts
      return () => {
        document.head.removeChild(style);
      };
    }
  }, []);

  // Track initial mount and reset state
  const [initialMount, setInitialMount] = useState(true);

  // Check if reset is needed when navigating from other tabs
  useEffect(() => {
    const checkNeedsReset = async () => {
      console.log('🔍 Cases page: Checking if reset needed...');
      if (Platform.OS === 'web') {
        // For web, check localStorage
        const needsResetFlag = localStorage.getItem('casesNeedsReset');
        console.log('🏷️ Cases reset flag:', needsResetFlag);

        // TEMPORARY TEST: Force reset to test the logic
        // localStorage.setItem('casesNeedsReset', 'true');
        // console.log('🧪 TEST: Manually set reset flag for testing');

        if (needsResetFlag === 'true') {
          console.log('✅ Cases page needs reset - setting needsReset to true');
          logger.info('Dosyalar page needs reset');
          setNeedsReset(true);
          // Clear the flag
          localStorage.removeItem('casesNeedsReset');
        } else {
          console.log('❌ Cases page does not need reset');
        }
      } else {
        // For mobile, check AsyncStorage
        try {
          const needsResetFlag = await AsyncStorage.getItem('casesNeedsReset');
          if (needsResetFlag === 'true') {
            logger.info('Mobile: Dosyalar page needs reset');
            setNeedsReset(true);
            // Clear the flag
            await AsyncStorage.removeItem('casesNeedsReset');
          }
        } catch (error) {
          logger.error('Error checking reset state:', error);
        }
      }
    };

    checkNeedsReset();
  }, []);

  // Always reset on first mount, but allow pagination to work
  useEffect(() => {
    // This effect runs when component mounts or when needsReset changes
    console.log('🔄 Cases reset effect triggered:', { initialMount, needsReset });
    if (initialMount || needsReset) {
      console.log('🚀 Cases page: Starting reset process...');
      logger.info('Dosyalar page mounted or needs reset - resetting to default state');

      // For web: Check if we're coming from pagination (URL has query parameters)
      const isPaginationNavigation = Platform.OS === 'web' && window.location.href.includes('?');
      console.log('🔗 Pagination navigation detected:', isPaginationNavigation);

      // For mobile: Always reset
      // For web: Reset if not pagination navigation or if we need to reset
      if (Platform.OS !== 'web' || !isPaginationNavigation || needsReset) {
        console.log('✅ Cases page: Performing reset to default state...');
        // Reset to default values
        setCurrentPage(1);
        setSearchText('');
        setActiveFilter('All');
        setSortBy('dosyaNo');
        setSortOrder('desc');

        // Clear any stored pagination state in web
        if (Platform.OS === 'web') {
          localStorage.removeItem('casesCurrentPage');
          localStorage.removeItem('casesSearchText');
          localStorage.removeItem('casesActiveFilter');
          localStorage.removeItem('casesSortBy');
          localStorage.removeItem('casesSortOrder');

          // Clear the needs reset flag
          setNeedsReset(false);
        }

        // Clear any stored state in mobile
        if (Platform.OS !== 'web') {
          const clearMobileState = async () => {
            try {
              await AsyncStorage.removeItem('casesCurrentPage');
              await AsyncStorage.removeItem('casesSearchText');
              await AsyncStorage.removeItem('casesActiveFilter');
              await AsyncStorage.removeItem('casesSortBy');
              await AsyncStorage.removeItem('casesSortOrder');
              await AsyncStorage.removeItem('dosyalarNeedsReset');
              await AsyncStorage.removeItem('lastVisitedPath');
            } catch (error) {
              logger.error('Error clearing mobile state:', error);
            }
          };
          clearMobileState();
        }
      } else {
        // We're navigating with pagination on web, restore state
        if (Platform.OS === 'web') {
          const storedPage = localStorage.getItem('casesCurrentPage');
          const storedSearchText = localStorage.getItem('casesSearchText');
          const storedActiveFilter = localStorage.getItem('casesActiveFilter');
          const storedSortBy = localStorage.getItem('casesSortBy');
          const storedSortOrder = localStorage.getItem('casesSortOrder');

          if (storedPage) setCurrentPage(parseInt(storedPage, 10));
          if (storedSearchText) setSearchText(storedSearchText);
          if (storedActiveFilter) setActiveFilter(storedActiveFilter as 'All' | 'Active' | 'Closed');
          if (storedSortBy) setSortBy(storedSortBy);
          if (storedSortOrder) setSortOrder(storedSortOrder as 'asc' | 'desc');
        }
      }

      // Mark initial mount as complete only if this was the initial mount
      if (initialMount) {
        setInitialMount(false);
      }
    }
  }, [initialMount, needsReset]); // Include needsReset in dependencies

  // API'den davaları getir
  useEffect(() => {
    const fetchCases = async () => {
      try {
        setLoading(true);
        setError('');

        // Token kontrolü yap
        const token = await AsyncStorage.getItem('auth_token');
        if (!token) {
          console.log('No auth token found, skipping API call');
          setLoading(false);
          setError('Oturum açık değil. Lütfen tekrar giriş yapın.');
          setCases(CASES_DATA);
          return;
        }

        // Önce tarafları yükle (eğer yüklenmemişse)
        console.log('Fetching parties if needed...');
        try {
          await fetchPartiesIfNeeded();
          console.log('Parties fetched successfully or already loaded');
        } catch (partiesError) {
          console.error('Error fetching parties:', partiesError);
          // Taraflar yüklenemese bile devam et
        }

        // Aktif ve kapalı davaları getir
        console.log('Fetching active and closed cases...');
        let activeResponse, closedResponse;

        try {
          activeResponse = await caseService.getUserCases(true);
          console.log('Active cases fetched successfully');
        } catch (activeError) {
          console.error('Error fetching active cases:', activeError);
          activeResponse = [];
        }

        try {
          closedResponse = await caseService.getUserCases(false);
          console.log('Closed cases fetched successfully');
        } catch (closedError) {
          console.error('Error fetching closed cases:', closedError);
          closedResponse = [];
        }

        // Aktif davaları işle
        let activeCases = [];
        if (Array.isArray(activeResponse) && activeResponse.length > 0) {
          activeCases = activeResponse[0] || [];

          // Aktif olarak işaretle
          activeCases = activeCases.map((item: any) => ({ ...item, status: 'Active' as const }));
        }

        // Kapalı davaları işle
        let closedCases = [];
        if (Array.isArray(closedResponse) && closedResponse.length > 0) {
          closedCases = closedResponse[0] || [];

          // Kapalı olarak işaretle
          closedCases = closedCases.map((item: any) => ({ ...item, status: 'Closed' as const }));
        }

        // Tüm davaları birleştir
        let data = [...activeCases, ...closedCases];

        // Veri hazır
        console.log(`Processed ${data.length} cases (${activeCases.length} active, ${closedCases.length} closed)`);

        // Davaları dosyaNo'ya göre sırala (en güncel en üstte)
        const sortedData = [...data].sort((a, b) => {
          // Null/undefined kontrolü
          const dosyaNoA = a.dosyaNo || '';
          const dosyaNoB = b.dosyaNo || '';

          // DosyaNo'dan yıl ve numara bilgisini çıkar
          let yearA = 0;
          let yearB = 0;

          if (dosyaNoA && dosyaNoA.includes('/')) {
            yearA = parseInt(dosyaNoA.split('/')[0]) || 0;
          }

          if (dosyaNoB && dosyaNoB.includes('/')) {
            yearB = parseInt(dosyaNoB.split('/')[0]) || 0;
          }

          // Önce yıla göre sırala (büyükten küçüğe)
          if (yearA !== yearB) {
            return yearB - yearA;
          }

          // Yıl aynıysa, tam dosya numarasına göre sırala
          return dosyaNoB.localeCompare(dosyaNoA);
        });

        setCases(sortedData);

        // Taraflar Context'ten alınıyor
        setLoading(false);
      } catch (err: any) {
        console.error('Error in fetchCases:', err);
        setError(`Dosyalar yüklenirken bir hata oluştu: ${err.message || 'Bilinmeyen hata'}`);
        setLoading(false);
        // Geliştirme aşamasında örnek verileri kullan
        setCases(CASES_DATA);
      }
    };

    fetchCases();
  }, []);

  // Arama ve filtreleme fonksiyonu - useMemo ile optimize edildi
  const filteredCases = useMemo(() => {
    let filtered = cases;

    // Durum filtreleme
    if (activeFilter !== 'All') {
      filtered = filtered.filter(item => item.status === activeFilter);
    }

    // Metin araması - case-insensitive ve dosya durumu dahil
    if (searchText.trim() !== '') {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(item =>
        // Dosya No
        (item.dosyaNo?.toLowerCase() || '').includes(searchLower) ||
        // Birim Adı (Mahkeme)
        (item.birimAdi?.toLowerCase() || '').includes(searchLower) ||
        // Dosya Türü
        (item.dosyaTur?.toLowerCase() || '').includes(searchLower) ||
        // Dosya Durumu - yeni eklendi
        (item.dosyaDurum?.toLowerCase() || '').includes(searchLower) ||
        // Durum (Aktif/Kapalı) - yeni eklendi
        (item.status === 'Active' && 'aktif'.includes(searchLower)) ||
        (item.status === 'Closed' && 'kapalı'.includes(searchLower)) ||
        // Esas Yıl
        (String(item.esasYil) || '').includes(searchLower)
      );
    }

    // Sıralama
    return sortCases(filtered);
  }, [cases, activeFilter, searchText, sortBy, sortOrder]);

  // Platform'a göre sayfalama stratejisi
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedCases, setDisplayedCases] = useState<any[]>([]);

  // Filtrelenmiş ve sıralanmış davalar artık useMemo ile hesaplanıyor

  // Filtrelenmiş davalar değiştiğinde mobil için displayedCases'i güncelle
  useEffect(() => {
    if (Platform.OS !== 'web') {
      // Mobil için ilk sayfayı yükle
      setDisplayedCases(filteredCases.slice(0, itemsPerPage));
    }
  }, [filteredCases, itemsPerPage]); // Sıralama değiştiğinde de güncellenmesi için tüm filteredCases'i kullan

  // Sayfa değiştiğinde sayfayı en üste kaydır
  const scrollToTop = useCallback(() => {
    if (Platform.OS === 'web') {
      // Force immediate scroll to top without animation
      window.scrollTo(0, 0);

      // For safety, also try to scroll the container element if it exists
      const container = document.getElementById('cases-container');
      if (container) {
        container.scrollTop = 0;
      }

      // Also try to scroll the body and html elements
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
    }
  }, []);

  // Sayfa değiştiğinde sayfayı en üste kaydır
  useEffect(() => {
    scrollToTop();
  }, [currentPage, scrollToTop]);

  // Sayfalama için geçerli sayfadaki davalar
  const currentCases = Platform.OS === 'web'
    ? filteredCases.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : displayedCases;

  // Toplam sayfa sayısı
  const totalPages = Math.ceil(filteredCases.length / itemsPerPage);

  // Define sections for FlatList
  const sections = [
    { id: 'content', type: 'content' },
    { id: 'pagination', type: 'pagination' },
    { id: 'footer', type: 'footer' }
  ];

  // Mobil için daha fazla yükle
  const loadMoreItems = () => {
    if (isLoadingMore || displayedCases.length >= filteredCases.length) return;

    setIsLoadingMore(true);

    // Simule edilmiş yükleme gecikmesi (gerçek uygulamada API çağrısı olabilir)
    setTimeout(() => {
      // Sıralanmış filteredCases'ten sonraki sayfayı al
      const nextItems = filteredCases.slice(
        displayedCases.length,
        displayedCases.length + itemsPerPage
      );

      setDisplayedCases([...displayedCases, ...nextItems]);
      setIsLoadingMore(false);

      // Mobil cihazlarda da sayfayı en üste kaydır
      if (Platform.OS !== 'web' && displayedCases.length === 0) {
        // FlatList referansı varsa en üste kaydır
        if (flatListRef.current) {
          flatListRef.current.scrollToOffset({ offset: 0, animated: true });
        }
      }
    }, 300); // Daha hızlı güncelleme için süreyi azalttık
  };



  const navigateToCaseDetail = (id: string | number, dosyaNo?: string, caseData?: any): void => {
    // Dosya numarasını ve case data'yı query parametresi olarak geç
    const queryParam = dosyaNo ? `?dosyaNo=${encodeURIComponent(dosyaNo)}&from=cases` : '?from=cases';

    // Store the case data in localStorage to pass it to the details page
    if (Platform.OS === 'web') {
      // Store the current path to return to later
      localStorage.setItem('previousPath', window.location.pathname);

      // Store the case data if available
      if (caseData) {
        // Make sure acilisTarihi is properly included in the case data
        const caseDataToStore = { ...caseData };

        // If acilisTarihi is not already set, try to extract it from dosyaAcilisTarihi
        if (!caseDataToStore.acilisTarihi && caseDataToStore.dosyaAcilisTarihi && caseDataToStore.dosyaAcilisTarihi.date) {
          const date = caseDataToStore.dosyaAcilisTarihi.date;
          caseDataToStore.acilisTarihi = `${date.day}.${date.month}.${date.year}`;
        }

        console.log('Storing case data with acilisTarihi:', caseDataToStore.acilisTarihi);
        localStorage.setItem('selectedCaseData', JSON.stringify(caseDataToStore));
      }
    }

    router.push(`/cases/${id}${queryParam}`);
  };

  // Toggle card expanded state
  const toggleCardExpanded = (id: string): void => {
    setExpandedCardIds(prevIds => {
      if (prevIds.includes(id)) {
        // Card is already expanded, collapse it
        return prevIds.filter(cardId => cardId !== id);
      } else {
        // Card is collapsed, expand it
        return [...prevIds, id];
      }
    });
  };

  // Sayfalama fonksiyonları
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      // For both web and mobile, just update the state
      setCurrentPage(currentPage + 1);

      if (Platform.OS === 'web') {
        // Save all filter states for pagination in localStorage for persistence
        localStorage.setItem('casesCurrentPage', String(currentPage + 1));
        localStorage.setItem('casesSearchText', searchText);
        localStorage.setItem('casesActiveFilter', activeFilter);
        localStorage.setItem('casesSortBy', sortBy);
        localStorage.setItem('casesSortOrder', sortOrder);

        // Scroll to top when changing pages
        scrollToTop();
      }
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      // For both web and mobile, just update the state
      setCurrentPage(currentPage - 1);

      if (Platform.OS === 'web') {
        // Save all filter states for pagination in localStorage for persistence
        localStorage.setItem('casesCurrentPage', String(currentPage - 1));
        localStorage.setItem('casesSearchText', searchText);
        localStorage.setItem('casesActiveFilter', activeFilter);
        localStorage.setItem('casesSortBy', sortBy);
        localStorage.setItem('casesSortOrder', sortOrder);

        // Scroll to top when changing pages
        scrollToTop();
      }
    }
  };

  // Belirli bir sayfaya git
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      // For both web and mobile, just update the state
      setCurrentPage(page);

      if (Platform.OS === 'web') {
        // Save all filter states for pagination in localStorage for persistence
        localStorage.setItem('casesCurrentPage', String(page));
        localStorage.setItem('casesSearchText', searchText);
        localStorage.setItem('casesActiveFilter', activeFilter);
        localStorage.setItem('casesSortBy', sortBy);
        localStorage.setItem('casesSortOrder', sortOrder);

        // Scroll to top when changing pages
        scrollToTop();
      }
    }
  };



  // Sıralama başlığına tıklandığında sıralama değiştir
  const handleSort = (field: string): void => {
    let newSortOrder = sortOrder;

    if (sortBy === field) {
      // Aynı alanın sıralama yönünü değiştir
      newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
      setSortOrder(newSortOrder);
    } else {
      // Yeni alan seçildi, varsayılan olarak artan sırala
      setSortBy(field);
      setSortOrder('asc');
      newSortOrder = 'asc';
    }

    // For both web and mobile, just update the state and let the useMemo handle the sorting
    if (Platform.OS === 'web') {
      // Save the new sort state in localStorage for persistence
      localStorage.setItem('casesSortBy', field);
      localStorage.setItem('casesSortOrder', newSortOrder);
      // Reset to page 1
      localStorage.setItem('casesCurrentPage', '1');
    }

    // Reset to page 1 for consistency
    setCurrentPage(1);
    // The filteredCases useMemo will automatically re-sort based on the new sortBy and sortOrder

    // Close the modal
    setIsSortModalVisible(false);
  };



  const renderCaseItem = ({ item }: { item: any }) => {
    // Alanları çıkar
    const dosyaId = item.dosyaId || '';
    const dosyaNo = item.dosyaNo || '';
    const birimAdi = item.birimAdi || '';
    const dosyaTur = item.dosyaTur || '';

    // DosyaNo'yu hazırla

    // Dava türüne göre renk belirle
    const caseTypeColor = getCaseTypeColor(dosyaTur);

    // Tarafları al - dosyaNo'ya göre (Context'ten)
    // Tarafları bul - dosyaNo'yu string olarak kullan
    let parties = [];

    try {
      // Önce doğrudan dosyaNo ile dene
      if (allParties && allParties[dosyaNo]) {
        parties = allParties[dosyaNo] || [];
      }

      // Eğer taraflar bulunamadıysa, tüm anahtarları kontrol et
      if (!parties || parties.length === 0) {
        if (allParties && typeof allParties === 'object') {
          for (const key of Object.keys(allParties)) {
            if (key === dosyaNo) {
              parties = allParties[key] || [];
              break;
            }
          }
        }
      }

      // Hala bulunamadıysa, dosyaNo'yu farklı formatlarda dene
      if (!parties || parties.length === 0) {
        // Dosya numarasını farklı formatlarda dene
        const alternativeFormats = [
          dosyaNo,
          dosyaNo.replace('/', '-'),
          dosyaNo.replace('-', '/'),
          dosyaNo.trim(),
        ];

        for (const format of alternativeFormats) {
          if (allParties && allParties[format] && allParties[format].length > 0) {
            console.log(`FOUND MATCH FOR ALTERNATIVE FORMAT: ${format}`);
            parties = allParties[format] || [];
            break;
          }
        }
      }

      // Son çare olarak getPartiesForCase fonksiyonunu dene
      if (!parties || parties.length === 0) {
        parties = getPartiesForCase(dosyaNo);
      }

      // Log the result without using sample data
      if (!parties || parties.length === 0) {
        console.log(`INFO: No parties found for dosya ${dosyaNo} - will show "no data" message`);
        parties = []; // Keep it empty instead of using sample data
      } else {
        console.log(`SUCCESS: Found ${parties.length} parties for dosya ${dosyaNo}`);
      }

      // Tarafları konsola yazdır (sadece geliştirme ortamında)
      if (__DEV__) {
        console.log(`Parties for dosya ${dosyaNo}:`, parties && parties.length > 0 ? 'Found' : 'Not found');
        if (parties && parties.length > 0) {
          console.log(`First party:`, parties[0]);
        }
      }
    } catch (error) {
      console.error('Error processing parties data:', error);
      parties = [];
    }





    // Açılış tarihi
    let acilisTarihi = '';
    if (item.dosyaAcilisTarihi && item.dosyaAcilisTarihi.date) {
      const date = item.dosyaAcilisTarihi.date;
      acilisTarihi = `${date.day}.${date.month}.${date.year}`;

      // Make sure acilisTarihi is set on the item object itself for passing to detail page
      if (!item.acilisTarihi) {
        item.acilisTarihi = acilisTarihi;
      }
    } else if (item.acilisTarihi) {
      acilisTarihi = item.acilisTarihi;
    }

    // Find davacı and davalı parties - not used directly but kept for reference
    // let davaci = null;
    // let davali = null;

    // This code was used to find davaci/davali parties
    // Now we use the parties directly in the UI without these variables
    try {
      // Parties are already available from the context
      // We don't need to find specific parties here anymore
      if (parties && Array.isArray(parties) && parties.length > 0) {
        // The parties are filtered directly in the rendering code below
      }
    } catch (error) {
      logger.error('Error processing parties:', error);
    }

    // Check if card is expanded
    const isExpanded = expandedCardIds.includes(dosyaId);

    return (
      <TouchableOpacity
        style={styles.caseCard}
        onPress={() => toggleCardExpanded(dosyaId)}
        activeOpacity={0.8}
      >
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.cardBlur}>
          {/* Renk indikatörü */}
          <View style={[styles.caseTypeIndicator, { backgroundColor: caseTypeColor }]} />

          {/* Single row header with all key information */}
          <View style={styles.singleRowHeader}>
            {/* Color indicator and dosya title */}
            <View style={styles.headerLeftSection}>
              <View style={styles.dosyaNoContainer}>
                <View style={styles.caseTitleContainer}>
                  <ThemedText numberOfLines={1} style={styles.caseTitle}>
                    Dosya {formatCaseNumber(item)}
                  </ThemedText>

                  {/* Moved dosya tipi chip here */}
                  <View style={[styles.typeChip, { backgroundColor: `${caseTypeColor}20`, marginLeft: 8 }]}>
                    <ThemedText style={[styles.typeChipText, { color: caseTypeColor }]}>{dosyaTur || 'Belirtilmemiş'}</ThemedText>
                  </View>
                </View>
              </View>

              {/* Key information in a single row */}
              <View style={styles.inlineInfoContainer}>
                {/* Dosya No */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Dosya No:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{dosyaNo || 'Belirtilmemiş'}</ThemedText>
                </View>

                {/* Açılış Tarihi */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Açılış:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{acilisTarihi || 'Belirtilmemiş'}</ThemedText>
                </View>

                {/* Dosya Durumu */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Durum:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{item.dosyaDurum || 'Belirtilmemiş'}</ThemedText>
                </View>

                {/* Aktif/Kapalı */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Aktiflik:</ThemedText>
                  <ThemedText
                    style={[
                      styles.inlineInfoValue,
                      {color: item.status === 'Active' ? '#10B981' : '#EF4444'}
                    ]}
                  >
                    {item.status === 'Active' ? 'Aktif' : 'Kapalı'}
                  </ThemedText>
                </View>

                {/* Mahkeme info */}
                <View style={[styles.inlineInfoItem, styles.mahkemeInfoItem]}>
                  <ThemedText style={styles.inlineInfoLabel}>Mahkeme:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{birimAdi || 'Belirtilmemiş'}</ThemedText>
                </View>
              </View>
            </View>

            {/* Right section with expand/collapse indicator */}
            <View style={styles.headerRightSection}>
              {/* Expand/Collapse indicator */}
              <Ionicons
                name={isExpanded ? "chevron-up-outline" : "chevron-down-outline"}
                size={20}
                color={isDark ? '#9ca3af' : '#64748b'}
                style={styles.expandIcon}
              />
            </View>
          </View>

          {/* Expanded View */}
          {isExpanded && (
            <View style={styles.expandedContent}>
              {/* Taraflar (Parties) */}
              <View style={styles.expandedSection}>
                <View style={styles.expandedSectionHeader}>
                  <Ionicons name="people-outline" size={18} color={isDark ? '#9ca3af' : '#64748b'} />
                  <ThemedText style={styles.expandedSectionTitle}>Tüm Taraflar</ThemedText>
                </View>

                {parties && Array.isArray(parties) && parties.length > 0 ? (
                  <View style={styles.partiesList}>
                    {Array.isArray(parties) ? (
                      <>
                        {/* Davacı/Müşteki/Katılan taraflar */}
                        {parties.filter(party =>
                          party && party.rol && (
                            party.rol.includes('Davacı') ||
                            party.rol.includes('Müşteki') ||
                            party.rol.includes('Katılan') ||
                            party.rol.includes('Mağdur')
                          )
                        ).map((party, index) => {
                          const tarafAdi = party.adi && party.soyad ? `${party.adi} ${party.soyad}` :
                                     party.adi || party.tarafAdi || 'Belirtilmemiş';
                          const tarafRol = party.rol || 'Belirtilmemiş';
                          const tarafKisiKurum = party.kisiKurum || '';
                          const tarafVekil = party.vekil ?
                            (typeof party.vekil === 'string' ?
                              party.vekil.replace(/[\[\]]/g, '') :
                              Array.isArray(party.vekil) ?
                                party.vekil.join(', ') :
                                '') :
                            '';

                          return (
                            <View key={`davaci-${index}`} style={styles.partyItemContainer}>
                              <ThemedText style={[styles.partyName, { color: '#10B981' }]}>
                                • {tarafAdi}
                              </ThemedText>

                              <View style={styles.partyDetailsContainer}>
                                <ThemedText style={styles.partyRole}>{tarafRol}</ThemedText>

                                {tarafKisiKurum ? (
                                  <ThemedText style={styles.partyType}>
                                    {tarafKisiKurum === 'Kişi' ? '👤' : '🏢'} {tarafKisiKurum}
                                  </ThemedText>
                                ) : null}
                              </View>

                              {tarafVekil ? (
                                <ThemedText style={styles.partyLawyer}>
                                  ⚖️ Vekil: {tarafVekil}
                                </ThemedText>
                              ) : null}
                            </View>
                          );
                        })}

                        {/* Davalı/Şüpheli/Sanık taraflar */}
                        {parties.filter(party =>
                          party && party.rol && (
                            party.rol.includes('Davalı') ||
                            party.rol.includes('Şüpheli') ||
                            party.rol.includes('Sanık') ||
                            party.rol.includes('Aleyhine')
                          )
                        ).map((party, index) => {
                          const tarafAdi = party.adi && party.soyad ? `${party.adi} ${party.soyad}` :
                                     party.adi || party.tarafAdi || 'Belirtilmemiş';
                          const tarafRol = party.rol || 'Belirtilmemiş';
                          const tarafKisiKurum = party.kisiKurum || '';
                          const tarafVekil = party.vekil ?
                            (typeof party.vekil === 'string' ?
                              party.vekil.replace(/[\[\]]/g, '') :
                              Array.isArray(party.vekil) ?
                                party.vekil.join(', ') :
                                '') :
                            '';

                          return (
                            <View key={`davali-${index}`} style={styles.partyItemContainer}>
                              <ThemedText style={[styles.partyName, { color: '#EF4444' }]}>
                                • {tarafAdi}
                              </ThemedText>

                              <View style={styles.partyDetailsContainer}>
                                <ThemedText style={styles.partyRole}>{tarafRol}</ThemedText>

                                {tarafKisiKurum ? (
                                  <ThemedText style={styles.partyType}>
                                    {tarafKisiKurum === 'Kişi' ? '👤' : '🏢'} {tarafKisiKurum}
                                  </ThemedText>
                                ) : null}
                              </View>

                              {tarafVekil ? (
                                <ThemedText style={styles.partyLawyer}>
                                  ⚖️ Vekil: {tarafVekil}
                                </ThemedText>
                              ) : null}
                            </View>
                          );
                        })}

                        {/* Diğer taraflar */}
                        {parties.filter(party =>
                          party && party.rol && (
                            !party.rol.includes('Davacı') &&
                            !party.rol.includes('Müşteki') &&
                            !party.rol.includes('Katılan') &&
                            !party.rol.includes('Mağdur') &&
                            !party.rol.includes('Davalı') &&
                            !party.rol.includes('Şüpheli') &&
                            !party.rol.includes('Sanık') &&
                            !party.rol.includes('Aleyhine')
                          )
                        ).map((party, index) => {
                          const tarafAdi = party.adi && party.soyad ? `${party.adi} ${party.soyad}` :
                                     party.adi || party.tarafAdi || 'Belirtilmemiş';
                          const tarafRol = party.rol || 'Belirtilmemiş';
                          const tarafKisiKurum = party.kisiKurum || '';
                          const tarafVekil = party.vekil ?
                            (typeof party.vekil === 'string' ?
                              party.vekil.replace(/[\[\]]/g, '') :
                              Array.isArray(party.vekil) ?
                                party.vekil.join(', ') :
                                '') :
                            '';

                          return (
                            <View key={`diger-${index}`} style={styles.partyItemContainer}>
                              <ThemedText style={styles.partyName}>
                                • {tarafAdi}
                              </ThemedText>

                              <View style={styles.partyDetailsContainer}>
                                <ThemedText style={styles.partyRole}>{tarafRol}</ThemedText>

                                {tarafKisiKurum ? (
                                  <ThemedText style={styles.partyType}>
                                    {tarafKisiKurum === 'Kişi' ? '👤' : '🏢'} {tarafKisiKurum}
                                  </ThemedText>
                                ) : null}
                              </View>

                              {tarafVekil ? (
                                <ThemedText style={styles.partyLawyer}>
                                  ⚖️ Vekil: {tarafVekil}
                                </ThemedText>
                              ) : null}
                            </View>
                          );
                        })}
                      </>
                    ) : (
                      <ThemedText style={styles.noPartiesText}>Taraf bilgisi formatı uygun değil</ThemedText>
                    )}
                  </View>
                ) : (
                  <ThemedText style={styles.noPartiesText}>Taraf bilgisi bulunamadı</ThemedText>
                )}
              </View>

              {/* Action Buttons */}
              <View style={styles.expandedActions}>
                <TouchableOpacity
                  style={styles.expandedActionButton}
                  onPress={() => navigateToCaseDetail(dosyaId, dosyaNo, item)}
                >
                  <Ionicons name="eye-outline" size={20} color="#fff" />
                  <ThemedText style={styles.expandedActionButtonText}>Detaylar</ThemedText>
                </TouchableOpacity>

                <TouchableOpacity style={styles.expandedActionButton}>
                  <Ionicons name="document-text" size={20} color="#fff" />
                  <ThemedText style={styles.expandedActionButtonText}>Belgeler</ThemedText>
                </TouchableOpacity>

                <TouchableOpacity style={styles.expandedActionButton}>
                  <Ionicons name="create-outline" size={20} color="#fff" />
                  <ThemedText style={styles.expandedActionButtonText}>Düzenle</ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </BlurView>
      </TouchableOpacity>
    );
  };

  return (
    <ThemedView style={styles.container} id="cases-container">
      <ImageBackground
        source={require('../../assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >




      {/* Main FlatList as the scrollable component */}
      <FlatList
        ref={flatListRef}
        data={sections}
        keyExtractor={item => item.id}
        style={{ flex: 1 }}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => {
          // Content section
          if (item.type === 'content') {
            // Yükleme ve Hata Durumları
            if (loading || partiesLoading) {
              return (
                <ThemedView style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
                  <ThemedText style={styles.loadingText}>
                    {partiesLoading ? 'Taraflar yükleniyor...' : 'Davalar yükleniyor...'}
                  </ThemedText>
                </ThemedView>
              );
            } else if (error) {
              return (
                <ThemedView style={styles.errorContainer}>
                  <Ionicons name="alert-circle" size={48} color="#EF4444" />
                  <ThemedText style={styles.errorText}>{error}</ThemedText>
                  <TouchableOpacity
                    style={styles.retryButton}
                    onPress={() => {
                      setLoading(true);
                      caseService.getUserCases(true)
                        .then(data => {
                          setCases(data);
                          setError('');
                        })
                        .catch(err => {
                          console.error('Error retrying fetch cases:', err);
                          setError('Dosyalar yüklenirken bir hata oluştu.');
                          // Geliştirme aşamasında örnek verileri kullan
                          setCases(CASES_DATA);
                        })
                        .finally(() => setLoading(false));
                    }}
                  >
                    <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
                  </TouchableOpacity>
                </ThemedView>
              );
            } else if (filteredCases.length === 0) {
              return (
                <ThemedView style={styles.emptyContainer}>
                  <Ionicons name="folder-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
                  <ThemedText style={styles.emptyText}>Dava bulunamadı</ThemedText>
                </ThemedView>
              );
            } else {
              // Render the case list
              return (
                <View style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'auto', width: '100%' }}>
                  {/* Main content area */}
                  <View style={{ flex: 1, overflow: 'auto', width: '100%' }}>
                    {/* Ultra Compact Filter Bar */}
                    {Platform.OS === 'web' && (
                      <View style={styles.ultraCompactFilterBar}>
                        {/* Search Input */}
                        <View style={[
                          styles.searchContainer,
                          isDark && styles.searchInputContainerDark,
                          Platform.OS === 'web' && { className: 'search-container' }
                        ]}>
                          <Ionicons name="search" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                          <TextInput
                            style={[
                              styles.searchInput,
                              isDark && styles.searchInputDark,
                              Platform.OS === 'web' && { className: 'search-input' }
                            ]}
                            placeholder="Dosya ara..."
                            placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
                            value={searchText}
                            onChangeText={(text) => {
                              // Just update the search text state - no reloading
                              setSearchText(text);

                              // Reset to page 1 when search changes
                              if (currentPage !== 1) {
                                setCurrentPage(1);
                              }

                              // Save the search text for persistence
                              if (Platform.OS === 'web') {
                                localStorage.setItem('casesSearchText', text);
                                localStorage.setItem('casesCurrentPage', '1');
                              }
                            }}
                          />
                          {searchText.length > 0 && (
                            <TouchableOpacity
                              style={styles.clearSearchButton}
                              onPress={() => {
                                // Just clear the search text - no reloading
                                setSearchText('');

                                // Reset to page 1
                                if (currentPage !== 1) {
                                  setCurrentPage(1);
                                }

                                // Save the empty search text for persistence
                                if (Platform.OS === 'web') {
                                  localStorage.setItem('casesSearchText', '');
                                  localStorage.setItem('casesCurrentPage', '1');
                                }
                              }}
                            >
                              <Ionicons name="close-circle" size={18} color={isDark ? '#9ca3af' : '#64748b'} />
                            </TouchableOpacity>
                          )}
                        </View>

                        {/* Filter Controls */}
                        <View style={styles.filterControls}>
                          {/* Filter Button */}
                          <TouchableOpacity
                            style={styles.miniFilterButton}
                            onPress={() => setIsFilterModalVisible(true)}
                          >
                            <Ionicons
                              name="filter"
                              size={20}
                              color={activeFilter !== 'All' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                            />
                          </TouchableOpacity>

                          {/* Sort Button */}
                          <TouchableOpacity
                            style={styles.miniFilterButton}
                            onPress={() => setIsSortModalVisible(true)}
                          >
                            <Ionicons
                              name="swap-vertical"
                              size={20}
                              color={isDark ? '#9ca3af' : '#64748b'}
                            />
                          </TouchableOpacity>
                        </View>
                      </View>
                    )}

                    {/* Kart Görünümü */}
                    <View style={styles.contentContainer}>
                      <View style={styles.cardsContainer}>
                        <FlatList
                          data={currentCases}
                          renderItem={renderCaseItem}
                          keyExtractor={item => item.dosyaId?.toString() || Math.random().toString()}
                          contentContainerStyle={[
                            Platform.OS !== 'web' && { paddingBottom: 80 }
                          ]}
                          showsVerticalScrollIndicator={false}
                          onEndReached={Platform.OS !== 'web' ? loadMoreItems : undefined}
                          onEndReachedThreshold={0.3}
                          scrollEnabled={false} // Disable scrolling to prevent nested scrolling
                          ListFooterComponent={Platform.OS !== 'web' && isLoadingMore ? (
                            <View style={styles.loadMoreContainer}>
                              <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                              <ThemedText style={styles.loadMoreText}>Daha fazla yükleniyor...</ThemedText>
                            </View>
                          ) : null}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              );
            }
          }

          // Pagination section - Web only
          if (item.type === 'pagination' && Platform.OS === 'web' && totalPages > 1 && !loading && !error && filteredCases.length > 0) {
            return (
              <View style={styles.contentContainer}>
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={filteredCases.length}
                  onPageChange={(page) => {
                    // For both web and mobile, just update the state without page reload
                    setCurrentPage(page);

                    // Save all filter states for pagination in localStorage for persistence
                    if (Platform.OS === 'web') {
                      localStorage.setItem('casesCurrentPage', String(page));
                      localStorage.setItem('casesSearchText', searchText);
                      localStorage.setItem('casesActiveFilter', activeFilter);
                      localStorage.setItem('casesSortBy', sortBy);
                      localStorage.setItem('casesSortOrder', sortOrder);

                      // Scroll to top when changing pages
                      scrollToTop();
                    }
                  }}
                  itemsPerPage={itemsPerPage}
                />
              </View>
            );
          }

          // Footer section - Web only
          if (item.type === 'footer' && Platform.OS === 'web' && !loading && !error) {
            return (
              <View style={styles.fullWidthContainer}>
                <Footer />
              </View>
            );
          }

          return null;
        }}
      />

      {/* Status Filter Modal */}
      {isFilterModalVisible && (
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            activeOpacity={1}
            onPress={() => setIsFilterModalVisible(false)}
          />
          <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Dava Durumu</ThemedText>
              <ThemedText style={styles.modalSubtitle}>
                Seçili: {activeFilter === 'All' ? 'Tümü' :
                         activeFilter === 'Active' ? 'Aktif' : 'Kapalı'}
              </ThemedText>
              <TouchableOpacity onPress={() => setIsFilterModalVisible(false)} style={styles.modalCloseButton}>
                <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
              </TouchableOpacity>
            </View>

            <ScrollView style={{padding: 16, maxHeight: Platform.OS === 'web' ? '80%' : '70%'}}>
              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'All' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'All') {
                    setActiveFilter('All');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('casesActiveFilter', 'All');
                      // Reset to page 1
                      localStorage.setItem('casesCurrentPage', '1');
                      // Just update the state without page reload
                      setCurrentPage(1);
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="list"
                  size={20}
                  color={activeFilter === 'All' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'All' && styles.activeModalOptionText
                  ]}>
                    Tümü
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Tüm aktif ve kapalı davaları göster
                  </ThemedText>
                </View>
                {activeFilter === 'All' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'Active' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'Active') {
                    setActiveFilter('Active');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('casesActiveFilter', 'Active');
                      // Reset to page 1
                      localStorage.setItem('casesCurrentPage', '1');
                      // Just update the state without page reload
                      setCurrentPage(1);
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={activeFilter === 'Active' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'Active' && styles.activeModalOptionText
                  ]}>
                    Aktif
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Sadece aktif davaları göster
                  </ThemedText>
                </View>
                {activeFilter === 'Active' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'Closed' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'Closed') {
                    setActiveFilter('Closed');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('casesActiveFilter', 'Closed');
                      // Reset to page 1
                      localStorage.setItem('casesCurrentPage', '1');
                      // Just update the state without page reload
                      setCurrentPage(1);
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={activeFilter === 'Closed' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'Closed' && styles.activeModalOptionText
                  ]}>
                    Kapalı
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Sadece kapalı davaları göster
                  </ThemedText>
                </View>
                {activeFilter === 'Closed' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      )}

      {/* Sort Modal */}
      {isSortModalVisible && (
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            activeOpacity={1}
            onPress={() => setIsSortModalVisible(false)}
          />
          <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Sıralama</ThemedText>
              <ThemedText style={styles.modalSubtitle}>
                Seçili: {sortBy === 'dosyaNo' ? 'Dosya No' :
                         sortBy === 'esasYil' ? 'Esas Yıl' :
                         sortBy === 'birimAdi' ? 'Mahkeme' : 'Dosya Türü'}
                {' '}{sortOrder === 'asc' ? '(Artan)' : '(Azalan)'}
              </ThemedText>
              <TouchableOpacity onPress={() => setIsSortModalVisible(false)} style={styles.modalCloseButton}>
                <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
              </TouchableOpacity>
            </View>

            <View style={{padding: 12, paddingTop: 8}}>
              <ThemedText style={{
                fontSize: 14,
                fontWeight: '600',
                marginBottom: 8,
              }}>Sıralama Kriteri</ThemedText>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'dosyaNo' && styles.activeModalOption]}
                onPress={() => handleSort('dosyaNo')}
              >
                <Ionicons
                  name="document-text-outline"
                  size={20}
                  color={sortBy === 'dosyaNo' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'dosyaNo' && styles.activeModalOptionText
                  ]}>
                    Dosya No {sortBy === 'dosyaNo' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Davaları dosya numarasına göre sırala
                  </ThemedText>
                </View>
                {sortBy === 'dosyaNo' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'esasYil' && styles.activeModalOption]}
                onPress={() => handleSort('esasYil')}
              >
                <Ionicons
                  name="calendar-outline"
                  size={20}
                  color={sortBy === 'esasYil' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'esasYil' && styles.activeModalOptionText
                  ]}>
                    Esas Yıl {sortBy === 'esasYil' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Davaları esas yılına göre sırala
                  </ThemedText>
                </View>
                {sortBy === 'esasYil' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'birimAdi' && styles.activeModalOption]}
                onPress={() => handleSort('birimAdi')}
              >
                <Ionicons
                  name="business-outline"
                  size={20}
                  color={sortBy === 'birimAdi' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'birimAdi' && styles.activeModalOptionText
                  ]}>
                    Mahkeme {sortBy === 'birimAdi' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Davaları mahkeme adına göre sırala
                  </ThemedText>
                </View>
                {sortBy === 'birimAdi' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'dosyaTur' && styles.activeModalOption]}
                onPress={() => handleSort('dosyaTur')}
              >
                <Ionicons
                  name="folder-outline"
                  size={20}
                  color={sortBy === 'dosyaTur' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'dosyaTur' && styles.activeModalOptionText
                  ]}>
                    Dosya Türü {sortBy === 'dosyaTur' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Davaları dosya türüne göre sırala
                  </ThemedText>
                </View>
                {sortBy === 'dosyaTur' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <View style={styles.modalDivider} />

              <ThemedText style={{
                fontSize: 14,
                fontWeight: '600',
                marginBottom: 8,
                marginTop: 4,
              }}>Sıralama Yönü</ThemedText>

              <View style={styles.sortDirectionContainer}>
                <TouchableOpacity
                  style={[styles.sortDirectionButton, sortOrder === 'asc' && styles.activeSortDirectionButton]}
                  onPress={() => {
                    // Use the same logic as handleSort for consistency
                    const newSortOrder = 'asc';
                    setSortOrder(newSortOrder);

                    if (Platform.OS === 'web') {
                      localStorage.setItem('casesSortOrder', newSortOrder);
                      localStorage.setItem('casesCurrentPage', '1');
                      // Just update the state without page reload
                      setCurrentPage(1);
                    } else {
                      // For mobile, just update the state
                      setCurrentPage(1);
                    }
                    setIsSortModalVisible(false);
                  }}
                >
                  <Ionicons
                    name="arrow-up"
                    size={20}
                    color={sortOrder === 'asc' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                  />
                  <ThemedText style={[
                    styles.sortDirectionText,
                    sortOrder === 'asc' && styles.activeSortDirectionText
                  ]}>
                    Artan
                  </ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.sortDirectionButton, sortOrder === 'desc' && styles.activeSortDirectionButton]}
                  onPress={() => {
                    // Use the same logic as handleSort for consistency
                    const newSortOrder = 'desc';
                    setSortOrder(newSortOrder);

                    if (Platform.OS === 'web') {
                      localStorage.setItem('casesSortOrder', newSortOrder);
                      localStorage.setItem('casesCurrentPage', '1');
                      // Just update the state without page reload
                      setCurrentPage(1);
                    } else {
                      // For mobile, just update the state
                      setCurrentPage(1);
                    }
                    setIsSortModalVisible(false);
                  }}
                >
                  <Ionicons
                    name="arrow-down"
                    size={20}
                    color={sortOrder === 'desc' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                  />
                  <ThemedText style={[
                    styles.sortDirectionText,
                    sortOrder === 'desc' && styles.activeSortDirectionText
                  ]}>
                    Azalan
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      )}
      </ImageBackground>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 10, // Reduced further since we removed the header
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },

  // Legacy header (kept for reference)
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 4,
  },
  addButton: {
    padding: 8,
  },
  // Ultra Compact Filter Bar - Same as Duruşmalar
  ultraCompactFilterBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 6,
    marginBottom: 2,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
    ...(Platform.OS !== 'web' && {
      paddingHorizontal: 12, // Smaller padding for mobile
      paddingBottom: 4, // Smaller padding for mobile
    }),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 14,
    height: 40,
    flex: 1,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
    // Web-specific styles added via custom CSS in useEffect
  },
  searchInputContainerDark: {
    backgroundColor: 'rgba(30, 41, 59, 0.9)',
    borderColor: 'rgba(75, 85, 99, 0.5)',
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.2)', // CSS shadow for web
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 15,
    color: '#1F2937',
    marginLeft: 10,
    paddingVertical: 0,
    fontWeight: '500',
    letterSpacing: 0.2, // Slightly improved letter spacing
    // outline is not supported in React Native Web
  },
  searchInputDark: {
    color: '#F3F4F6',
  },
  clearSearchButton: {
    padding: 6,
    marginLeft: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  filterControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  miniFilterButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    position: 'relative',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
  },
  activeDot: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.light.tint,
  },
  miniResultsCount: {
    marginLeft: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 24,
    alignItems: 'center',
  },
  miniResultsText: {
    fontSize: 11,
    fontWeight: '600',
  },
  // Modal Styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
    ...(Platform.OS !== 'web' && {
      // Ensure modal overlay covers the entire screen on mobile
      position: 'absolute',
      width: '100%',
      height: '100%',
    }),
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 12,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
    // Remove maxHeight to allow content to determine height
    ...(Platform.OS !== 'web' && {
      width: '90%', // Slightly narrower for better appearance
      borderRadius: 12, // Keep consistent radius
      display: 'flex', // Ensure flex display on mobile
      flexDirection: 'column', // Column layout
      marginTop: -50, // Move modal up slightly to center better
    }),
  },
  modalContainerDark: {
    backgroundColor: '#1F2937',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalHeader: {
    padding: 12, // Reduced padding
    paddingBottom: 10, // Even smaller bottom padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 16, // Smaller font size
    fontWeight: '600',
    marginBottom: 2, // Reduced margin
  },
  modalSubtitle: {
    fontSize: 12, // Smaller font size
    opacity: 0.7,
  },
  modalCloseButton: {
    position: 'absolute',
    top: 12, // Adjusted position
    right: 12, // Adjusted position
    width: 24, // Smaller size
    height: 24, // Smaller size
    borderRadius: 12, // Adjusted radius
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    padding: 16,
    ...(Platform.OS === 'web' ? {
      maxHeight: '80%',
    } : {
      flexGrow: 1,
      paddingBottom: 24, // Add extra padding at the bottom for mobile
      overflow: 'scroll', // Use scroll for mobile
    }),
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 8,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8, // Reduced vertical padding
    paddingHorizontal: 12, // Reduced horizontal padding
    borderRadius: 8,
    marginBottom: 6, // Reduced margin
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  activeModalOption: {
    backgroundColor: 'rgba(10, 126, 164, 0.1)',
  },
  modalOptionIcon: {
    marginRight: 12,
  },
  modalOptionTextContainer: {
    flex: 1,
  },
  modalOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeModalOptionText: {
    color: Colors.light.tint,
  },
  modalOptionDescription: {
    fontSize: 11, // Smaller font size
    opacity: 0.7,
    marginTop: 1, // Reduced margin
  },
  modalDivider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginVertical: 10, // Reduced margin
  },
  sortDirectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4, // Reduced margin
  },
  sortDirectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8, // Reduced padding
    paddingHorizontal: 12, // Reduced padding
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    width: '48%',
  },
  activeSortDirectionButton: {
    backgroundColor: Colors.light.tint,
  },
  sortDirectionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  activeSortDirectionText: {
    color: 'white',
  },

  // Legacy styles kept for compatibility
  activeFilterText: {
    color: 'white',
    fontWeight: '600',
  },
  // Content container - Same as Duruşmalar
  contentContainer: {
    flex: 1,
    padding: 16,
    paddingTop: 16,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
  },
  // Kart listesi - Same as Duruşmalar
  cardsContainer: {
    width: '100%',
    marginBottom: 16,
  },
  fullWidthContainer: {
    width: '100%',
    maxWidth: '100%',
    marginLeft: 0,
    marginRight: 0,
  },
  // Dava kartı - Same as Duruşmalar
  caseCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: 'white',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
    // Dosyalar sayfasındaki gibi daha kompakt kart yapısı
    paddingVertical: 0, // No vertical padding as we have the BlurView inside
    paddingHorizontal: 0, // No horizontal padding as we have the BlurView inside
  },
  cardBlur: {
    borderRadius: 16, // Match the card radius
    overflow: 'hidden',
    borderWidth: 0, // No border as the card already has one
    borderColor: 'rgba(255, 255, 255, 0.2)',
    ...(Platform.OS !== 'web' && {
      borderRadius: 16, // Match the card radius for mobile
      borderWidth: 0, // No border as the card already has one
    }),
  },
  caseTypeIndicator: {
    height: 3, // Reduced height
    width: '100%',
  },
  // Single row header
  singleRowHeader: {
    padding: 8,
    paddingHorizontal: 12,
    paddingBottom: 10, // Slightly more padding at the bottom for the inline info
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...(Platform.OS !== 'web' && {
      padding: 6, // Smaller padding for mobile
      paddingHorizontal: 10, // Smaller horizontal padding for mobile
    }),
  },
  headerLeftSection: {
    flex: 4, // Give more space to the left section
    paddingRight: 4,
  },
  headerRightSection: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  inlineInfoContainer: {
    flexDirection: 'row',
    flexWrap: 'nowrap', // Prevent wrapping to keep all items in one row
    marginTop: 4,
    marginBottom: 4,
    justifyContent: 'space-between', // Equal spacing between items
    ...(Platform.OS !== 'web' && {
      marginTop: 2, // Smaller margin for mobile
      marginBottom: 2, // Smaller margin for mobile
      flexDirection: 'column', // Stack items vertically on mobile for better readability
    }),
  },
  inlineInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    width: '20%', // Five items with equal width
    paddingRight: 4, // Add padding to prevent text from touching next item
    ...(Platform.OS !== 'web' && {
      width: '100%', // Full width on mobile since we're stacking vertically
      marginBottom: 4, // Slightly larger margin for mobile
      paddingVertical: 2, // Add some vertical padding for better spacing
    }),
  },
  mahkemeInfoItem: {
    width: '20%', // Same width as other items for consistency
    ...(Platform.OS !== 'web' && {
      width: '100%', // Full width on mobile since we're stacking vertically
    }),
  },
  inlineInfoLabel: {
    fontSize: 10, // Smaller font size to fit in one row
    opacity: 0.7,
    marginRight: 2,
    minWidth: 0, // Remove minimum width to allow more flexibility
    ...(Platform.OS !== 'web' && {
      fontSize: 11, // Keep font size readable on mobile
      minWidth: 100, // Wider labels on mobile for better readability
      marginRight: 8, // More space between label and value on mobile
    }),
  },
  inlineInfoValue: {
    fontSize: 11, // Smaller font size to fit in one row
    fontWeight: '500',
    flex: 1,
    ...(Platform.OS === 'web' && {
      // Web-specific styles
      // @ts-ignore - These are valid CSS properties for web
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    }),
    ...(Platform.OS !== 'web' && {
      fontSize: 12, // Keep font size readable on mobile
      fontWeight: '600', // Make it slightly bolder on mobile
    }),
  },
  // Keep for backward compatibility
  tableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 8,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  tableHeaderLeft: {
    flex: 3,
    justifyContent: 'center',
  },
  tableHeaderRight: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  dosyaNoContainer: {
    flexDirection: 'column',
    justifyContent: 'flex-start', // Changed to flex-start
    marginBottom: 2, // Reduced margin
  },
  caseTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  caseTitle: {
    fontSize: 14, // Increased font size
    fontWeight: '700',
    marginRight: 8, // Added margin
    ...(Platform.OS !== 'web' && {
      fontSize: 13, // Smaller font for mobile
      marginRight: 4, // Smaller margin for mobile
    }),
  },
  caseSubtitle: {
    fontSize: 11, // Increased font size
    opacity: 0.7,
    marginTop: 1,
  },
  typeChip: {
    paddingVertical: 2, // Slightly increased padding
    paddingHorizontal: 4, // Slightly increased padding
    borderRadius: 4, // Smallest radius
    marginLeft: 2, // Minimal margin
  },
  typeChipText: {
    fontSize: 10, // Increased font size
    fontWeight: '600',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2, // Reduced margin
  },
  statusText: {
    fontSize: 11, // Smaller font
    fontWeight: '600',
    marginLeft: 2, // Reduced margin
  },
  dateText: {
    fontSize: 11, // Smaller font
    opacity: 0.7,
  },
  // Table-like content
  tableContent: {
    padding: 0,
    minHeight: 40, // Further reduced height
    maxHeight: 120, // Increased max height for all rows
  },
  // Horizontal layout styles
  horizontalInfoContainer: {
    flex: 1,
    padding: 8,
    paddingHorizontal: 12,
  },
  horizontalInfoRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  horizontalInfoItem: {
    flexDirection: 'column',
    marginBottom: 4,
    marginRight: 4,
    minWidth: '22%', // Ensure items take roughly equal space
    maxWidth: '24%', // Limit maximum width
  },
  horizontalInfoLabel: {
    fontSize: 11,
    opacity: 0.7,
    marginBottom: 2,
  },
  horizontalInfoValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Keep for backward compatibility
  infoContainer: {
    flex: 1,
    padding: 8,
    paddingHorizontal: 12,
  },
  // Keep these for backward compatibility
  tableColumn: {
    flex: 1,
    borderRightWidth: 1,
    borderRightColor: 'rgba(255, 255, 255, 0.1)',
    padding: 6,
    marginHorizontal: 4,
  },
  columnHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 4,
    marginBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  columnTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  columnContent: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  partyRow: {
    marginBottom: 4, // Reduced margin
    flexDirection: 'row', // Changed to row layout
    alignItems: 'center', // Center items vertically
    justifyContent: 'space-between', // Space between name and role
  },
  partyName: {
    fontSize: 13, // Increased font size
    fontWeight: '600',
    flex: 1, // Take available space
    marginRight: 4, // Add margin
  },
  partyRole: {
    fontSize: 11, // Smaller font
    opacity: 0.7,
    textAlign: 'right', // Align to right
  },
  infoRow: {
    marginBottom: 4, // Reduced margin
    flexDirection: 'row', // Row layout
    alignItems: 'center', // Center items vertically
    justifyContent: 'flex-start', // Start from left
  },
  infoLabel: {
    fontSize: 11, // Smaller font
    opacity: 0.7,
    marginRight: 4, // Add margin
    width: 'auto', // Auto width
    minWidth: 80, // Minimum width for alignment
  },
  infoValue: {
    fontSize: 12, // Smaller font
    flex: 1, // Take available space
    textAlign: 'left', // Align to left
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  cardActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Legacy styles kept for reference
  cardHeader: {
    padding: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  cardContent: {
    padding: 16,
    paddingTop: 12,
    paddingBottom: 12,
  },
  caseInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  caseInfoLabel: {
    marginLeft: 8,
    marginRight: 4,
    fontWeight: '500',
    width: 120,
    fontSize: 14,
  },
  caseInfoValue: {
    flex: 1,
    fontSize: 14,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  cardActions: {
    flexDirection: 'row',
    gap: 12,
  },
  // Expand/collapse icon
  expandIcon: {
    marginTop: 0,
    marginLeft: 4,
  },
  // Expanded view styles
  expandedContent: {
    padding: 12, // Reduced padding
    ...(Platform.OS !== 'web' && {
      padding: 8, // Even smaller padding for mobile
    }),
  },
  expandedSection: {
    marginBottom: 16, // Reduced margin
    ...(Platform.OS !== 'web' && {
      marginBottom: 12, // Smaller margin for mobile
    }),
  },
  expandedSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8, // Reduced margin
    paddingBottom: 6, // Reduced padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  expandedSectionTitle: {
    fontSize: 14, // Smaller font
    fontWeight: '600',
    marginLeft: 6, // Reduced margin
  },
  // Horizontal expanded layout
  horizontalExpandedRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  horizontalExpandedItem: {
    flexDirection: 'column',
    marginBottom: 6,
    minWidth: '22%',
    maxWidth: '24%',
  },
  horizontalExpandedLabel: {
    fontSize: 13,
    opacity: 0.7,
    marginBottom: 2,
  },
  horizontalExpandedValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Regular expanded grid
  expandedInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between', // Added space between
  },
  expandedInfoItem: {
    width: '48%', // Slightly reduced width to add space between items
    marginBottom: 8, // Reduced margin
    paddingRight: 4, // Reduced padding
    flexDirection: 'row', // Changed to row layout
    alignItems: 'center', // Center items vertically
  },
  expandedInfoLabel: {
    fontSize: 13, // Increased font size
    opacity: 0.7,
    marginRight: 4, // Add margin
    minWidth: 90, // Minimum width for alignment
  },
  expandedInfoValue: {
    fontSize: 14, // Increased font size
    fontWeight: '500',
    flex: 1, // Take available space
  },
  partiesList: {
    marginTop: 6, // Reduced margin
  },
  partyItemContainer: {
    marginBottom: 8, // Reduced margin
    paddingBottom: 6, // Reduced padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
    ...(Platform.OS !== 'web' && {
      marginBottom: 6, // Even smaller margin for mobile
      paddingBottom: 4, // Even smaller padding for mobile
      borderBottomWidth: StyleSheet.hairlineWidth, // Thinner border for mobile
    }),
  },
  partyDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 1, // Reduced margin
  },
  partyRoleSmall: {
    fontSize: 12, // Increased font size
    opacity: 0.7,
    marginRight: 6, // Reduced margin
  },
  partyType: {
    fontSize: 12, // Increased font size
    opacity: 0.7,
  },
  partyLawyer: {
    fontSize: 12, // Increased font size
    marginTop: 2, // Reduced margin
    opacity: 0.8,
  },
  noPartiesText: {
    fontSize: 13, // Increased font size
    opacity: 0.7,
    fontStyle: 'italic',
  },
  expandedActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12, // Reduced margin
    paddingTop: 12, // Reduced padding
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  expandedActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    paddingVertical: 6, // Reduced padding
    paddingHorizontal: 12, // Reduced padding
    borderRadius: 6, // Smaller radius
    ...(Platform.OS !== 'web' && {
      paddingVertical: 5, // Even smaller padding for mobile
      paddingHorizontal: 10, // Even smaller padding for mobile
      borderRadius: 4, // Even smaller radius for mobile
    }),
  },
  expandedActionButtonText: {
    color: '#fff',
    marginLeft: 6, // Reduced margin
    fontSize: 13, // Increased font size
    fontWeight: '500',
    ...(Platform.OS !== 'web' && {
      marginLeft: 4, // Smaller margin for mobile
      fontSize: 12, // Smaller font for mobile
    }),
  },
  // Pagination styles moved to Pagination component
  // Belgeler ve duruşmalar için eski stiller (referans için korundu)
  documentsContainer: {
    gap: 8,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
  },
  documentName: {
    flex: 1,
    marginLeft: 8,
  },
  documentDate: {
    fontSize: 12,
    color: '#9ca3af',
  },
  hearingsContainer: {
    gap: 12,
  },
  hearingItem: {
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
  },
  hearingHeader: {
    marginBottom: 8,
  },
  hearingDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hearingDate: {
    marginLeft: 4,
    marginRight: 12,
  },
  hearingTimeIcon: {
    marginLeft: 4,
  },
  hearingTime: {
    marginLeft: 4,
  },
  hearingNotes: {
    fontSize: 14,
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    marginTop: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    marginTop: 20,
  },
  errorText: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    maxWidth: '80%',
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    marginTop: 20,
  },
  emptyText: {
    marginTop: 8,
    fontSize: 14,
    marginBottom: 16,
  },
  addEmptyButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  addEmptyButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  // Taraflar bölümü stilleri
  partiesSection: {
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 10,
    paddingBottom: 5,
  },
  partiesTitle: {
    fontWeight: '600',
    marginBottom: 8,
    fontSize: 14,
    color: '#64748b',
  },
  partiesListStyle: {
    marginLeft: 8,
  },
  partyItemContainerStyle: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(150, 150, 150, 0.2)',
  },
  partyNameStyle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    ...(Platform.OS !== 'web' && {
      fontSize: 13, // Smaller font for mobile
      marginBottom: 2, // Smaller margin for mobile
    }),
  },
  partyDetailsContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    flexWrap: 'wrap',
    ...(Platform.OS !== 'web' && {
      marginBottom: 2, // Smaller margin for mobile
    }),
  },
  partyRoleStyle: {
    fontSize: 13,
    opacity: 0.8,
    marginRight: 8,
    ...(Platform.OS !== 'web' && {
      fontSize: 12, // Smaller font for mobile
      marginRight: 6, // Smaller margin for mobile
    }),
  },
  partyTypeStyle: {
    fontSize: 13,
    opacity: 0.8,
    ...(Platform.OS !== 'web' && {
      fontSize: 12, // Smaller font for mobile
    }),
  },
  partyLawyerStyle: {
    fontSize: 13,
    fontStyle: 'italic',
    opacity: 0.8,
    ...(Platform.OS !== 'web' && {
      fontSize: 12, // Smaller font for mobile
    }),
  },
  partyItemStyle: {
    fontSize: 13,
    marginBottom: 6,
    lineHeight: 18,
    flexWrap: 'wrap',
  },

  noPartiesTextStyle: {
    fontSize: 13,
    fontStyle: 'italic',
    opacity: 0.7,
    marginLeft: 8,
  },
  loadMoreContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loadMoreText: {
    marginLeft: 8,
    fontSize: 14,
  },
});