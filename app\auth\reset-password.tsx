import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  ImageBackground,
  Dimensions,
  Animated,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { Link, router, useLocalSearchParams } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import authService from '@/services/authService';
import Footer from '@/components/layout/Footer';

export default function ResetPasswordScreen() {
  const params = useLocalSearchParams();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [confirmSecureTextEntry, setConfirmSecureTextEntry] = useState(true);

  const [passwordFocused, setPasswordFocused] = useState(false);
  const [confirmPasswordFocused, setConfirmPasswordFocused] = useState(false);

  const passwordRef = useRef(null);
  const confirmPasswordRef = useRef(null);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { width } = Dimensions.get('window');

  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];

  // Get email from params
  useEffect(() => {
    if (params && params.email) {
      setEmail(params.email.toString());
    }
  }, [params]);

  // Animate the screen when it mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const toggleSecureEntry = () => {
    setSecureTextEntry(!secureTextEntry);
  };

  const toggleConfirmSecureEntry = () => {
    setConfirmSecureTextEntry(!confirmSecureTextEntry);
  };

  // State for redirect countdown
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(5);

  // Redirect countdown timer
  useEffect(() => {
    if (showSuccessModal && redirectCountdown > 0) {
      const timer = setTimeout(() => {
        setRedirectCountdown(redirectCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (showSuccessModal && redirectCountdown === 0) {
      // Redirect to login page when countdown reaches zero
      console.log('Redirect countdown reached zero, navigating to login screen');
      router.replace('/auth/login');
    }
  }, [showSuccessModal, redirectCountdown]);

  const handleResetPassword = async () => {
    // Basic validation
    if (!password || !confirmPassword) {
      setError('Lütfen tüm alanları doldurun');
      return;
    }

    if (password !== confirmPassword) {
      setError('Şifreler eşleşmiyor');
      return;
    }

    if (password.length < 8) {
      setError('Şifre en az 8 karakter olmalıdır');
      return;
    }

    // Password complexity validation
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (!(hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar)) {
      setError('Şifre en az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter içermelidir');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Get the validated OTP from AsyncStorage
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const validatedOtpString = await AsyncStorage.getItem('validatedOtp');

      if (!validatedOtpString) {
        setError('Doğrulama bilgisi bulunamadı. Lütfen tekrar doğrulama yapın.');
        setIsLoading(false);
        return;
      }

      const validatedOtp = JSON.parse(validatedOtpString);

      // Ensure the validatedOtp has the required fields
      if (!validatedOtp.otpValue || !validatedOtp.otpEmail) {
        // Create a new validatedOtp object with the correct structure
        const fixedValidatedOtp = {
          otpValue: validatedOtp.otpValue || validatedOtp.otp || '',
          otpEmail: validatedOtp.otpEmail || email || ''
        };

        // Copy any additional fields
        if (validatedOtp.id) fixedValidatedOtp.id = validatedOtp.id;
        if (validatedOtp.createdAt) fixedValidatedOtp.createdAt = validatedOtp.createdAt;
        if (validatedOtp.jwt) fixedValidatedOtp.jwt = validatedOtp.jwt;

        // Replace the original validatedOtp with the fixed one
        validatedOtp = fixedValidatedOtp;

        // Save the fixed validatedOtp back to AsyncStorage
        await AsyncStorage.setItem('validatedOtp', JSON.stringify(validatedOtp));

        // If still missing required fields, show error
        if (!validatedOtp.otpValue || !validatedOtp.otpEmail) {
          setError('Doğrulama bilgileri eksik. Lütfen tekrar deneyin.');
          return;
        }
      }

      // Check if this is a mock OTP (for development)
      const isMockOtp = validatedOtp.id && validatedOtp.id.startsWith('mock-otp-');

      if (isMockOtp && __DEV__) {
        // Simulate a successful response in development mode
        const mockUpdateResponse = {
          success: true,
          message: "Password updated successfully (mock)",
          timestamp: new Date().toISOString()
        };
      } else {
        // Update the password with real API call
        try {
          const updateResponse = await authService.updatePassword(password, validatedOtp);
        } catch (updateError) {
          // If in development mode, try a direct API call as a fallback
          if (__DEV__) {
            try {
              const axios = require('axios').default;

              const directResponse = await axios.post(
                'http://193.35.154.97:4244/auth/user/forgot-password/update-password',
                {
                  password,
                  validatedOtp: {
                    otpValue: validatedOtp.otpValue,
                    otpEmail: validatedOtp.otpEmail
                  }
                },
                {
                  headers: {
                    'Content-Type': 'application/json'
                  }
                }
              );
            } catch (directError) {
              throw updateError; // Re-throw the original error
            }
          } else {
            throw updateError; // Re-throw the error in production
          }
        }
      }

      setSuccess(true);

      // Clear the validated OTP from storage
      await AsyncStorage.removeItem('validatedOtp');
      await AsyncStorage.removeItem('password_reset_flow');

      // Store the email for login convenience
      await AsyncStorage.setItem('verifiedEmail', email);

      // Show success modal and start redirect countdown
      setShowSuccessModal(true);
    } catch (err: any) {
      setIsLoading(false);

      // Show appropriate error message
      if (err.response && err.response.status === 400) {
        setError('Şifre güncellenirken bir hata oluştu. Lütfen tekrar deneyin.');
      } else if (err.response && err.response.data && err.response.data.message) {
        setError(err.response.data.message);
      } else if (err.message && err.message.includes('Network Error')) {
        setError('Sunucuya bağlanılamıyor. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        setError('Bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Success Modal */}
      {showSuccessModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalIconContainer}>
              <Ionicons name="checkmark-circle" size={60} color="#10B981" />
            </View>
            <Text style={styles.modalTitle}>Şifre Sıfırlama Başarılı</Text>
            <Text style={styles.modalMessage}>
              Şifreniz başarıyla sıfırlandı. {redirectCountdown} saniye içinde giriş sayfasına yönlendirileceksiniz.
            </Text>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => router.replace('/auth/login')}
            >
              <Text style={styles.modalButtonText}>Hemen Giriş Yap</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={{flex: 1, width: '100%', display: 'flex', flexDirection: 'column'}}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <ImageBackground
            source={require('../../assets/images/law-background.jpg')}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          >
            <View style={styles.mainContainer}>
              <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
              >
                <Animated.View
                  style={[
                    styles.formWrapper,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                  <View style={styles.formHeader}>
                    <Text style={styles.formTitle}>Şifre Sıfırlama</Text>
                    <View style={styles.formTitleUnderline} />
                  </View>

                  <View style={styles.formContainer}>
                    {/* Info Message */}
                    <View style={styles.infoContainer}>
                      <Ionicons name="information-circle" size={24} color="#4A78B0" />
                      <Text style={styles.infoText}>
                        Lütfen yeni şifrenizi girin. Şifreniz en az 8 karakter uzunluğunda olmalı ve en az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter içermelidir.
                      </Text>
                    </View>

                    {/* Error Message */}
                    {error ? (
                      <Animated.View style={styles.errorContainer}>
                        <Ionicons name="alert-circle" size={20} color="#EF4444" />
                        <Text style={styles.errorText}>{error}</Text>
                      </Animated.View>
                    ) : null}

                    {/* Email Display */}
                    <View style={styles.emailDisplay}>
                      <ThemedText style={styles.emailLabel}>E-posta Adresi:</ThemedText>
                      <ThemedText style={styles.emailValue}>{email}</ThemedText>
                    </View>



                    {/* Password Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="lock-closed-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Yeni Şifre</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        passwordFocused && styles.inputWrapperFocused
                      ]}>
                        <TextInput
                          ref={passwordRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Yeni şifrenizi girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={password}
                          onChangeText={setPassword}
                          secureTextEntry={secureTextEntry}
                          onFocus={() => setPasswordFocused(true)}
                          onBlur={() => setPasswordFocused(false)}
                          returnKeyType="next"
                          onSubmitEditing={() => confirmPasswordRef.current?.focus()}
                        />
                        <TouchableOpacity
                          onPress={toggleSecureEntry}
                          style={styles.eyeIcon}
                          activeOpacity={0.7}
                        >
                          <Ionicons
                            name={secureTextEntry ? "eye-outline" : "eye-off-outline"}
                            size={20}
                            color={isDark ? '#94a3b8' : '#64748b'}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>

                    {/* Confirm Password Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="lock-closed-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Şifre Tekrar</ThemedText>
                      </View>
                      <View style={[
                        styles.inputWrapper,
                        confirmPasswordFocused && styles.inputWrapperFocused
                      ]}>
                        <TextInput
                          ref={confirmPasswordRef}
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Şifrenizi tekrar girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={confirmPassword}
                          onChangeText={setConfirmPassword}
                          secureTextEntry={confirmSecureTextEntry}
                          onFocus={() => setConfirmPasswordFocused(true)}
                          onBlur={() => setConfirmPasswordFocused(false)}
                          returnKeyType="done"
                          onSubmitEditing={handleResetPassword}
                        />
                        <TouchableOpacity
                          onPress={toggleConfirmSecureEntry}
                          style={styles.eyeIcon}
                          activeOpacity={0.7}
                        >
                          <Ionicons
                            name={confirmSecureTextEntry ? "eye-outline" : "eye-off-outline"}
                            size={20}
                            color={isDark ? '#94a3b8' : '#64748b'}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>

                    {/* Reset Password Button */}
                    <TouchableOpacity
                      style={[styles.button, isLoading && styles.buttonDisabled]}
                      onPress={handleResetPassword}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <View style={styles.loadingContainer}>
                          <Text style={styles.buttonText}>İşleniyor</Text>
                          <View style={styles.loadingDots}>
                            <View style={styles.loadingDot} />
                            <View style={[styles.loadingDot, styles.loadingDotMiddle]} />
                            <View style={styles.loadingDot} />
                          </View>
                        </View>
                      ) : (
                        <Text style={styles.buttonText}>Şifremi Sıfırla</Text>
                      )}
                    </TouchableOpacity>



                    {/* Back to Login Link */}
                    <View style={styles.footer}>
                      <Link href="/auth/login" asChild>
                        <TouchableOpacity>
                          <View style={styles.backToLoginContainer}>
                            <Ionicons name="arrow-back" size={16} color="#4A78B0" />
                            <ThemedText style={styles.backToLoginText}>Giriş Sayfasına Dön</ThemedText>
                          </View>
                        </TouchableOpacity>
                      </Link>
                    </View>
                  </View>
                </Animated.View>
              </ScrollView>
            </View>
          </ImageBackground>
        </KeyboardAvoidingView>
        {width > 768 && (
          <View style={styles.footerContainer}>
            <Footer />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  footerContainer: {
    width: '100%',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingBottom: Platform.OS === 'web' ? 0 : 50,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  mainContainer: {
    flexDirection: 'column',
    position: 'relative',
    overflow: 'hidden',
    width: '90%',
    maxWidth: 500,
    borderRadius: 20,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
    marginVertical: Platform.OS === 'web' ? 0 : 20,
    backgroundColor: '#FFFFFF',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 30 : 20,
    paddingVertical: Platform.OS === 'web' ? 50 : 30,
    minHeight: Platform.OS === 'web' ? '100%' : 'auto',
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginBottom: 8,
  },
  formTitleUnderline: {
    width: 40,
    height: 4,
    backgroundColor: '#4A78B0',
    borderRadius: 2,
  },
  formContainer: {
    width: '100%',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(74, 120, 176, 0.1)',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  infoText: {
    color: '#1E293B',
    marginLeft: 12,
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 20,
  },
  errorText: {
    color: '#EF4444',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  emailDisplay: {
    backgroundColor: '#F8FAFC',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  emailLabel: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 4,
  },
  emailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
  },
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    marginRight: 8,
    color: '#1E3A8A',
  },
  label: {
    fontWeight: '600',
    fontSize: 16,
    color: '#1E3A8A',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#CBD5E1',
    backgroundColor: '#FFFFFF',
    zIndex: 10,
  },
  inputWrapperFocused: {
    borderColor: '#1E3A8A',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 0px 5px rgba(30, 58, 138, 0.2)' }
      : {
          shadowColor: '#1E3A8A',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.2,
          shadowRadius: 5,
        }
    ),
    elevation: 2,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'transparent',
    color: '#1E293B',
    zIndex: 20,
  },
  inputLight: {
    color: '#1E293B',
  },
  inputDark: {
    color: '#1E293B',
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    padding: 8,
    zIndex: 30,
  },
  button: {
    backgroundColor: '#4A78B0',
    height: 50,
    borderRadius: 25,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 4px 8px rgba(74, 120, 176, 0.3)' }
      : {
          shadowColor: '#4A78B0',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }
    ),
    elevation: 5,
    paddingHorizontal: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    marginLeft: 8,
    alignItems: 'center',
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
  loadingDotMiddle: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  footer: {
    alignItems: 'center',
    marginTop: 16,
  },
  backToLoginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  backToLoginText: {
    color: '#4A78B0',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
  },
  modalIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E293B',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    color: '#64748B',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  modalButton: {
    backgroundColor: '#4A78B0',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  modalButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },

});