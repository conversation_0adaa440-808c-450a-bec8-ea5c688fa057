import { StyleSheet, TouchableOpacity, Image, Switch, Modal, ScrollView, Alert, Platform, ImageBackground } from 'react-native';
import React, { useContext, useState, useEffect } from 'react';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useTheme } from '@/contexts/ThemeContext';
import { AuthContext } from '../_layout';
import NotificationSettings from '@/components/notifications/NotificationSettings';
import notificationService from '@/services/notificationService';
import Footer from '@/components/layout/Footer';

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const { logout } = useContext(AuthContext);
  const { theme, setTheme } = useTheme();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(theme === 'dark' || (theme === 'system' && colorScheme === 'dark'));
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);

  // Tema değiştiğinde darkModeEnabled durumunu güncelle
  useEffect(() => {
    setDarkModeEnabled(theme === 'dark' || (theme === 'system' && colorScheme === 'dark'));
  }, [theme, colorScheme]);

  const [biometricEnabled, setBiometricEnabled] = useState(false);

  // Bildirim ayarlarını yükle
  useEffect(() => {
    const loadNotificationSettings = async () => {
      try {
        const settings = await notificationService.getNotificationSettings();
        if (settings) {
          setNotificationsEnabled(settings.enabled);
        }
      } catch (err) {
        console.error('Error loading notification settings:', err);
      }
    };

    loadNotificationSettings();
  }, []);

  const handleLogout = () => {
    logout();
    router.replace('/auth/login');
  };

  return (
    <ThemedView style={styles.container}>
      <ImageBackground
        source={require('../../assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <ThemedView style={styles.header}>
          <ThemedText type="title">Profil</ThemedText>
        </ThemedView>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
        <ThemedView style={styles.profileSection}>
          <Image
            source={require('@/assets/images/icon.png')}
            style={styles.profileImage}
          />
          <ThemedText type="title">Hakan YILDIZ</ThemedText>
          <ThemedText>Avukat ID: 123456</ThemedText>
          <ThemedText><EMAIL></ThemedText>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Hesap</ThemedText>

          <TouchableOpacity style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="person.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Profili Düzenle</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="lock.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Şifre Değiştir</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="creditcard.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Faturalama ve Abonelik</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Tercihler</ThemedText>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => setShowNotificationSettings(true)}
          >
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="bell.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Bildirim Ayarları</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>

          <ThemedView style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="bell.badge.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Bildirimleri Etkinleştir</ThemedText>
            </ThemedView>
            <Switch
              value={notificationsEnabled}
              onValueChange={(value) => {
                setNotificationsEnabled(value);
                notificationService.saveNotificationSettings({
                  ...notificationService.getNotificationSettings(),
                  enabled: value
                });
              }}
              trackColor={{ false: '#767577', true: Colors.light.tint }}
              thumbColor="#f4f3f4"
            />
          </ThemedView>

          <ThemedView style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="moon.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Karanlık Mod</ThemedText>
            </ThemedView>
            <Switch
              value={darkModeEnabled}
              onValueChange={(value) => {
                setDarkModeEnabled(value);
                setTheme(value ? 'dark' : 'light');
              }}
              trackColor={{ false: '#767577', true: Colors.light.tint }}
              thumbColor="#f4f3f4"
            />
          </ThemedView>

          <ThemedView style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="touchid" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Biyometrik Giriş</ThemedText>
            </ThemedView>
            <Switch
              value={biometricEnabled}
              onValueChange={setBiometricEnabled}
              trackColor={{ false: '#767577', true: Colors.light.tint }}
              thumbColor="#f4f3f4"
            />
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Destek</ThemedText>

          <TouchableOpacity style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="questionmark.circle.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Yardım ve Destek</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="doc.text.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Koşullar ve Gizlilik Politikası</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <ThemedView style={styles.menuItemLeft}>
              <IconSymbol size={20} name="info.circle.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText>Hakkında</ThemedText>
            </ThemedView>
            <IconSymbol size={16} name="chevron.right" color="#999" />
          </TouchableOpacity>
        </ThemedView>

        <TouchableOpacity style={styles.logoutButton} onPress={() => {
          if (Platform.OS === 'web') {
            handleLogout();
          } else {
            Alert.alert(
              'Çıkış Yap',
              'Hesabınızdan çıkış yapmak istediğinize emin misiniz?',
              [
                { text: 'İptal', style: 'cancel' },
                { text: 'Çıkış Yap', style: 'destructive', onPress: handleLogout }
              ]
            );
          }
        }}>
          <IconSymbol size={20} name="arrow.right.square.fill" color="white" />
          <ThemedText style={styles.logoutText}>Çıkış Yap</ThemedText>
        </TouchableOpacity>

        {/* Footer - Web only - Appears at the bottom */}
        {Platform.OS === 'web' && <Footer />}
      </ScrollView>
      </ImageBackground>

      {/* Bildirim Ayarları Modal */}
      {showNotificationSettings && (
        <Modal
          visible={showNotificationSettings}
          animationType="slide"
          transparent={false}
          onRequestClose={() => setShowNotificationSettings(false)}
        >
          <NotificationSettings onClose={() => setShowNotificationSettings(false)} />
        </Modal>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    paddingTop: 60,
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  header: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.tint,
    marginHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  logoutText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});
