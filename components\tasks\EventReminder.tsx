import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface EventReminderProps {
  id: string;
  title: string;
  eventType: 'Hearing' | 'Deposition' | 'Client Meeting' | 'Deadline' | 'Other';
  date: string;
  time: string;
  location?: string;
  caseId?: string;
  caseName?: string;
  onDismiss?: () => void;
}

export default function EventReminder({
  id,
  title,
  eventType,
  date,
  time,
  location,
  caseId,
  caseName,
  onDismiss
}: EventReminderProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const getEventTypeIcon = () => {
    switch (eventType) {
      case 'Hearing':
        return 'briefcase';
      case 'Deposition':
        return 'document-text';
      case 'Client Meeting':
        return 'people';
      case 'Deadline':
        return 'alarm';
      default:
        return 'calendar';
    }
  };
  
  const handleViewEvent = () => {
    router.push(`/events/${id}`);
  };
  
  const handleViewCase = () => {
    if (caseId) {
      router.push(`/cases/${caseId}`);
    }
  };
  
  return (
    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.container}>
      <View style={styles.header}>
        <View style={styles.typeContainer}>
          <Ionicons name={getEventTypeIcon()} size={20} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.typeText}>{eventType}</ThemedText>
        </View>
        
        <TouchableOpacity onPress={onDismiss}>
          <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
        </TouchableOpacity>
      </View>
      
      <ThemedText type="defaultSemiBold" style={styles.title}>{title}</ThemedText>
      
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <Ionicons name="calendar-outline" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
          <ThemedText style={styles.infoText}>{date}</ThemedText>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="time-outline" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
          <ThemedText style={styles.infoText}>{time}</ThemedText>
        </View>
        
        {location && (
          <View style={styles.infoItem}>
            <Ionicons name="location-outline" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
            <ThemedText style={styles.infoText}>{location}</ThemedText>
          </View>
        )}
        
        {caseName && (
          <TouchableOpacity style={styles.caseItem} onPress={handleViewCase}>
            <Ionicons name="folder-outline" size={16} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={[styles.infoText, { color: Colors[colorScheme ?? 'light'].tint }]}>
              {caseName}
            </ThemedText>
          </TouchableOpacity>
        )}
      </View>
      
      <TouchableOpacity style={styles.viewButton} onPress={handleViewEvent}>
        <ThemedText style={styles.viewButtonText}>Etkinliği Görüntüle</ThemedText>
      </TouchableOpacity>
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  title: {
    fontSize: 16,
    marginBottom: 12,
  },
  infoContainer: {
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
  },
  caseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  viewButton: {
    backgroundColor: Colors.light.tint,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
    marginTop: 8,
  },
  viewButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
});
