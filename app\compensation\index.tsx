import { StyleSheet, TouchableOpacity, View, TextInput, ScrollView, ActivityIndicator, Alert, Platform } from 'react-native';
import React, { useState } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import DateTimePicker from '@react-native-community/datetimepicker';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import compensationService from '@/services/compensationService';

export default function CompensationCalculatorScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Form state
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [grossSalary, setGrossSalary] = useState('');
  const [cumulativeIncomeTaxBasis, setCumulativeIncomeTaxBasis] = useState('');
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Result state
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Form validation
  const [errors, setErrors] = useState({
    grossSalary: '',
    dates: '',
  });

  // Form doğrulama
  const validateForm = () => {
    let isValid = true;
    const newErrors = { grossSalary: '', dates: '' };

    if (!grossSalary || isNaN(parseFloat(grossSalary)) || parseFloat(grossSalary) <= 0) {
      newErrors.grossSalary = 'Geçerli bir brüt maaş giriniz';
      isValid = false;
    }

    if (startDate >= endDate) {
      newErrors.dates = 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Tarih seçici değişikliği - Başlangıç
  const onStartDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || startDate;
    setShowStartDatePicker(Platform.OS === 'ios');
    setStartDate(currentDate);
  };

  // Tarih seçici değişikliği - Bitiş
  const onEndDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || endDate;
    setShowEndDatePicker(Platform.OS === 'ios');
    setEndDate(currentDate);
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Hesapla
  const handleCalculate = async () => {
    if (validateForm()) {
      try {
        setLoading(true);
        setError('');
        setResult(null);

        // API'ye gönderilecek veri
        const requestData = {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          grossSalary: parseFloat(grossSalary),
          cumulativeIncomeTaxBasis: cumulativeIncomeTaxBasis ? parseFloat(cumulativeIncomeTaxBasis) : undefined
        };

        // API'ye gönder
        const response = await compensationService.calculateCompensation(requestData);
        setResult(response);
      } catch (err) {
        console.error('Error calculating compensation:', err);
        setError('Tazminat hesaplanırken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    }
  };

  // Para formatla
  const formatCurrency = (value) => {
    if (!value) return '0,00 ₺';
    return value.replace(/(\d+)\.(\d+)/, '$1,$2 ₺');
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Tazminat Hesaplama</ThemedText>
        <View style={{ width: 24 }} />
      </ThemedView>

      <ScrollView style={styles.scrollView}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Hesaplama Bilgileri</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>İş Başlangıç Tarihi <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.dateInput]}
              onPress={() => setShowStartDatePicker(true)}
            >
              <ThemedText>{formatDate(startDate)}</ThemedText>
              <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={onStartDateChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>İş Bitiş Tarihi <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TouchableOpacity
              style={[styles.input, isDark && styles.inputDark, styles.dateInput]}
              onPress={() => setShowEndDatePicker(true)}
            >
              <ThemedText>{formatDate(endDate)}</ThemedText>
              <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
            {showEndDatePicker && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={onEndDateChange}
              />
            )}
            {errors.dates ? <ThemedText style={styles.errorText}>{errors.dates}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Brüt Maaş (₺) <ThemedText style={styles.requiredStar}>*</ThemedText></ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.grossSalary ? styles.inputError : null
              ]}
              placeholder="Örn: 30000"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={grossSalary}
              onChangeText={setGrossSalary}
              keyboardType="numeric"
            />
            {errors.grossSalary ? <ThemedText style={styles.errorText}>{errors.grossSalary}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Kümülatif Gelir Vergisi Matrahı (₺)</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              placeholder="Opsiyonel"
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={cumulativeIncomeTaxBasis}
              onChangeText={setCumulativeIncomeTaxBasis}
              keyboardType="numeric"
            />
          </View>

          <TouchableOpacity
            style={styles.calculateButton}
            onPress={handleCalculate}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Ionicons name="calculator-outline" size={20} color="white" />
                <ThemedText style={styles.calculateButtonText}>Hesapla</ThemedText>
              </>
            )}
          </TouchableOpacity>
        </BlurView>

        {error ? (
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.errorCard}>
            <Ionicons name="alert-circle" size={24} color="#EF4444" />
            <ThemedText style={styles.errorCardText}>{error}</ThemedText>
          </BlurView>
        ) : null}

        {result && (
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.resultCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>Hesaplama Sonuçları</ThemedText>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>Toplam Çalışma Süresi</ThemedText>
              <ThemedText style={styles.resultValue}>{result.totalDays} gün</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>İhbar Süresi</ThemedText>
              <ThemedText style={styles.resultValue}>{result.noticePeriodInDays} gün</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>İş Arama İzni</ThemedText>
              <ThemedText style={styles.resultValue}>{result.jobSearchLeaveHours} saat</ThemedText>
            </View>

            <View style={styles.resultDivider} />

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>Brüt Kıdem Tazminatı</ThemedText>
              <ThemedText style={styles.resultValue}>{formatCurrency(result.grossSeverancePay)}</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>Kıdem Tazminatı Damga Vergisi</ThemedText>
              <ThemedText style={styles.resultValue}>{formatCurrency(result.severancePayStampTax)}</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>Net Kıdem Tazminatı</ThemedText>
              <ThemedText style={[styles.resultValue, styles.highlightedValue]}>{formatCurrency(result.netSeverancePay)}</ThemedText>
            </View>

            <View style={styles.resultDivider} />

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>Brüt İhbar Tazminatı</ThemedText>
              <ThemedText style={styles.resultValue}>{formatCurrency(result.grossNoticePay)}</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>İhbar Tazminatı Damga Vergisi</ThemedText>
              <ThemedText style={styles.resultValue}>{formatCurrency(result.noticePayStampTax)}</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>İhbar Tazminatı Gelir Vergisi</ThemedText>
              <ThemedText style={styles.resultValue}>{formatCurrency(result.noticePayIncomeTax)}</ThemedText>
            </View>

            <View style={styles.resultRow}>
              <ThemedText style={styles.resultLabel}>Net İhbar Tazminatı</ThemedText>
              <ThemedText style={[styles.resultValue, styles.highlightedValue]}>{formatCurrency(result.netNoticePay)}</ThemedText>
            </View>

            <View style={styles.resultDivider} />

            <View style={styles.resultRow}>
              <ThemedText style={[styles.resultLabel, styles.totalLabel]}>Toplam Tazminat</ThemedText>
              <ThemedText style={[styles.resultValue, styles.totalValue]}>{formatCurrency(result.totalCompensation)}</ThemedText>
            </View>

            {result.severanceNoticeText && (
              <View style={styles.noticeContainer}>
                <Ionicons name="information-circle" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.noticeText}>{result.severanceNoticeText}</ThemedText>
              </View>
            )}
          </BlurView>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sectionTitle: {
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  requiredStar: {
    color: '#F44336',
  },
  input: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#000',
  },
  inputDark: {
    color: '#fff',
  },
  inputError: {
    borderColor: '#F44336',
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calculateButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
  },
  calculateButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  errorCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorCardText: {
    marginLeft: 8,
    color: '#EF4444',
    flex: 1,
  },
  resultCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  resultLabel: {
    opacity: 0.8,
  },
  resultValue: {
    fontWeight: '500',
  },
  highlightedValue: {
    fontWeight: '700',
    color: Colors.light.tint,
  },
  resultDivider: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: 8,
  },
  totalLabel: {
    fontWeight: '700',
    fontSize: 16,
    opacity: 1,
  },
  totalValue: {
    fontWeight: '700',
    fontSize: 16,
    color: Colors.light.tint,
  },
  noticeContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  noticeText: {
    marginLeft: 8,
    flex: 1,
    fontSize: 13,
    lineHeight: 18,
  },
});
