import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
  ImageBackground
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import lawbotService, { Message, Assistant } from '@/services/lawbotService';
import ChatMessage from '@/components/lawbot/ChatMessage';
import ChatInput from '@/components/lawbot/ChatInput';
import AssistantPicker from '@/components/lawbot/AssistantPicker';

// Sample assistants - replace with your actual assistant IDs
const ASSISTANTS: Assistant[] = [
  { id: 'asst_8zV90pSJGpF9cjequKKxecfU', name: 'Hu<PERSON>k Asistanı' },
  { id: 'asst_acsYBL6MOwh1MhUsv2MsKMNT', name: 'Genel Asistan' }
];

export default function LawbotScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAssistant, setSelectedAssistant] = useState(ASSISTANTS[0].id);
  const [threadId, setThreadId] = useState<string | null>(null);
  const flatListRef = useRef<FlatList>(null);
  const { width, height } = Dimensions.get('window');

  // Sayfa yüklenirken sayfayı en üste kaydır
  useLayoutEffect(() => {
    if (Platform.OS === 'web') {
      window.scrollTo(0, 0);
    }
  }, []);

  // Initialize thread
  useEffect(() => {
    const initializeThread = async () => {
      try {
        const id = await lawbotService.getThread();
        setThreadId(id);

        // Load existing messages if any
        if (id) {
          const existingMessages = await lawbotService.getMessages(id);
          // Sort messages by creation time to ensure chronological order
          setMessages(existingMessages.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime()));
        }
      } catch (error) {
        console.error('Error initializing thread:', error);
        Alert.alert('Hata', 'Sohbet başlatılamadı. Lütfen tekrar deneyin.');
      }
    };

    initializeThread();
  }, []);

  // Handle sending a message
  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    // Add user message to UI immediately
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      createdAt: new Date(),
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setIsLoading(true);

    try {
      // Send message to OpenAI and get response
      const updatedMessages = await lawbotService.sendMessage(selectedAssistant, content);
      // Sort messages by creation time to ensure chronological order
      setMessages(updatedMessages.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime()));
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Hata', 'Mesaj gönderilemedi. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle changing the assistant
  const handleAssistantChange = async (assistantId: string) => {
    if (assistantId === selectedAssistant) return;

    setSelectedAssistant(assistantId);
    setIsLoading(true);

    try {
      // Create a new thread when changing assistants
      const newThreadId = await lawbotService.createNewThread();
      setThreadId(newThreadId);
      setMessages([]); // Clear messages
    } catch (error) {
      console.error('Error changing assistant:', error);
      Alert.alert('Hata', 'Asistan değiştirilemedi. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ImageBackground
        source={require('../../assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <LinearGradient
          colors={isDark ? ['rgba(15, 23, 42, 0.8)', 'rgba(30, 41, 59, 0.8)', 'rgba(51, 65, 85, 0.8)'] : ['rgba(219, 234, 254, 0.8)', 'rgba(239, 246, 255, 0.8)', 'rgba(248, 250, 252, 0.8)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradient}
        >
        {/* Background Design Elements */}
        <View style={styles.backgroundElements}>
          <View style={[styles.circle, styles.circle1]} />
          <View style={[styles.circle, styles.circle2]} />
          <View style={[styles.circle, styles.circle3]} />
        </View>

        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
          <BlurView intensity={isDark ? 30 : 50} tint={isDark ? 'dark' : 'light'} style={styles.assistantPickerContainer}>
            <AssistantPicker
              assistants={ASSISTANTS}
              selectedAssistant={selectedAssistant}
              onAssistantChange={handleAssistantChange}
            />
          </BlurView>

          <BlurView intensity={isDark ? 30 : 50} tint={isDark ? 'dark' : 'light'} style={styles.chatContainer}>
            {isLoading && messages.length === 0 ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.loadingText}>Sohbet başlatılıyor...</ThemedText>
              </View>
            ) : (
              <FlatList
                ref={flatListRef}
                data={messages}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => <ChatMessage message={item} />}
                contentContainerStyle={styles.messagesContainer}
                keyboardShouldPersistTaps="handled"
                removeClippedSubviews={false}
                windowSize={10}
                maxToRenderPerBatch={10}
                initialNumToRender={15}
                onContentSizeChange={() => {
                  if (flatListRef.current && messages.length > 0) {
                    setTimeout(() => {
                      flatListRef.current?.scrollToEnd({ animated: false });
                    }, 100);
                  }
                }}
                onLayout={() => {
                  if (flatListRef.current && messages.length > 0) {
                    setTimeout(() => {
                      flatListRef.current?.scrollToEnd({ animated: false });
                    }, 100);
                  }
                }}
              />
            )}
          </BlurView>

          <BlurView intensity={isDark ? 30 : 50} tint={isDark ? 'dark' : 'light'} style={styles.inputContainer}>
            <ChatInput onSend={handleSendMessage} isLoading={isLoading} />
          </BlurView>
        </KeyboardAvoidingView>
        </LinearGradient>
      </ImageBackground>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  gradient: {
    flex: 1,
    position: 'relative',
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  circle: {
    position: 'absolute',
    borderRadius: 999,
  },
  circle1: {
    width: 300,
    height: 300,
    backgroundColor: 'rgba(96, 165, 250, 0.2)',
    top: -50,
    right: -100,
  },
  circle2: {
    width: 200,
    height: 200,
    backgroundColor: 'rgba(139, 92, 246, 0.15)',
    bottom: 100,
    left: -50,
  },
  circle3: {
    width: 250,
    height: 250,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    bottom: -100,
    right: 50,
  },
  keyboardAvoidingView: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    padding: 16,
    paddingTop: 24,
  },
  assistantPickerContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  chatContainer: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  inputContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  messagesContainer: {
    padding: 16,
    paddingBottom: 8,
    flexGrow: 1,
    justifyContent: 'flex-end',
    minHeight: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});
