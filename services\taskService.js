import apiClient from './api';
import tokenManager from '../utils/tokenManager';

// API temel URL'si
const API_URL = 'http://193.35.154.97:4244';

// Task servisi
const taskService = {
  // Tüm görevleri getir
  getAllTasks: async () => {
    try {
      const response = await apiClient.get('/api/tasks');

      // API yanıtını kontrol et
      if (response.data) {
        console.log('Görevler başarıyla alındı:', response.data.length);
        return response.data;
      } else {
        console.warn('API yanıtı boş veya geçersiz format');
        return [];
      }
    } catch (error) {
      console.error('Get all tasks error:', error);
      // Hata durumunda boş dizi döndür
      return [];
    }
  },

  // Görev detayını getir
  getTaskById: async (taskId) => {
    try {
      const response = await apiClient.get(`/api/tasks/${taskId}`);

      // API yanıtını kontrol et ve notları içerip içermediğini kontrol et
      if (response.data) {
        console.log(`Task ${taskId} details fetched successfully with ${response.data.notes?.length || 0} notes`);
      }

      return response.data;
    } catch (error) {
      console.error(`Get task ${taskId} error:`, error);
      throw error;
    }
  },

  // Yeni görev oluştur
  createTask: async (taskData) => {
    try {
      // Tarihleri kontrol et ve doğru formatta olduğundan emin ol
      console.log('Creating task with dates:');
      console.log('startDate:', taskData.startDate);
      console.log('dueDate:', taskData.dueDate);

      // Tarih formatını kontrol et
      let startDateFormatted = taskData.startDate;
      let dueDateFormatted = taskData.dueDate;

      // Tarih formatını doğrula ve düzelt - API Unix timestamp (saniye) bekliyor
      if (startDateFormatted) {
        try {
          // Tarih formatını doğrula
          const date = new Date(startDateFormatted);
          if (!isNaN(date.getTime())) {
            // Unix timestamp (saniye) olarak formatla
            startDateFormatted = Math.floor(date.getTime() / 1000);
          }
        } catch (e) {
          console.error('Invalid startDate format:', e);
          startDateFormatted = Math.floor(new Date().getTime() / 1000);
        }
      } else {
        startDateFormatted = Math.floor(new Date().getTime() / 1000);
      }

      if (dueDateFormatted) {
        try {
          // Tarih formatını doğrula
          const date = new Date(dueDateFormatted);
          if (!isNaN(date.getTime())) {
            // Unix timestamp (saniye) olarak formatla
            dueDateFormatted = Math.floor(date.getTime() / 1000);
          }
        } catch (e) {
          console.error('Invalid dueDate format:', e);
          dueDateFormatted = Math.floor(new Date().getTime() / 1000);
        }
      } else {
        dueDateFormatted = Math.floor(new Date().getTime() / 1000);
      }

      console.log('Formatted startDate (Unix timestamp):', startDateFormatted);
      console.log('Formatted dueDate (Unix timestamp):', dueDateFormatted);

      // API'nin beklediği formata dönüştür - sadece zorunlu alanlar
      const apiTaskData = {
        title: taskData.title,
        description: taskData.description || '',
        priority: taskData.priority || 'LOW',
        startDate: startDateFormatted,
        dueDate: dueDateFormatted,
        caseNumber: taskData.caseNumber || null,
        status: taskData.status || 'OPEN'
      };

      // Sadece location alanını ekle (clientId ve type gönderilmeyecek)
      if (taskData.location) {
        apiTaskData.location = taskData.location;
      }

      // Type alanını ekle
      if (taskData.type) {
        apiTaskData.type = taskData.type;
      }

      console.log('Gönderilen görev verisi:', JSON.stringify(apiTaskData, null, 2));
      const response = await apiClient.post('/api/tasks', apiTaskData);
      console.log('API yanıtı:', JSON.stringify(response.data, null, 2));

      // Yanıtı kontrol et
      if (!response.data || (response.status !== 200 && response.status !== 201)) {
        throw new Error(`Görev oluşturma başarısız. Durum kodu: ${response.status}`);
      }

      return response.data;
    } catch (error) {
      console.error('Create task error:', error);
      throw error;
    }
  },

  // Görev güncelle
  updateTask: async (taskId, taskData) => {
    try {
      // Tarihleri kontrol et ve doğru formatta olduğundan emin ol
      console.log('Updating task with dates:');
      console.log('startDate:', taskData.startDate);
      console.log('dueDate:', taskData.dueDate);

      // Tarih formatını kontrol et
      let startDateFormatted = taskData.startDate;
      let dueDateFormatted = taskData.dueDate;

      // Tarih formatını doğrula ve düzelt - API Unix timestamp (saniye) bekliyor
      if (startDateFormatted) {
        try {
          // Eğer zaten bir sayı ise (Unix timestamp) olduğu gibi kullan
          if (typeof startDateFormatted === 'number') {
            // Milisaniye ise saniyeye çevir
            if (startDateFormatted > 10000000000) {
              startDateFormatted = Math.floor(startDateFormatted / 1000);
            }
          } else {
            // String veya Date objesi ise
            const date = new Date(startDateFormatted);
            if (!isNaN(date.getTime())) {
              // Unix timestamp (saniye) olarak formatla
              startDateFormatted = Math.floor(date.getTime() / 1000);
            }
          }
        } catch (e) {
          console.error('Invalid startDate format:', e);
          startDateFormatted = Math.floor(new Date().getTime() / 1000);
        }
      } else {
        startDateFormatted = Math.floor(new Date().getTime() / 1000);
      }

      if (dueDateFormatted) {
        try {
          // Eğer zaten bir sayı ise (Unix timestamp) olduğu gibi kullan
          if (typeof dueDateFormatted === 'number') {
            // Milisaniye ise saniyeye çevir
            if (dueDateFormatted > 10000000000) {
              dueDateFormatted = Math.floor(dueDateFormatted / 1000);
            }
          } else {
            // String veya Date objesi ise
            const date = new Date(dueDateFormatted);
            if (!isNaN(date.getTime())) {
              // Unix timestamp (saniye) olarak formatla
              dueDateFormatted = Math.floor(date.getTime() / 1000);
            }
          }
        } catch (e) {
          console.error('Invalid dueDate format:', e);
          dueDateFormatted = Math.floor(new Date().getTime() / 1000);
        }
      } else {
        dueDateFormatted = Math.floor(new Date().getTime() / 1000);
      }

      console.log('Formatted startDate (Unix timestamp):', startDateFormatted);
      console.log('Formatted dueDate (Unix timestamp):', dueDateFormatted);

      // API'nin beklediği formata dönüştür
      const apiTaskData = {
        title: taskData.title,
        description: taskData.description || '',
        priority: taskData.priority,
        startDate: startDateFormatted,
        dueDate: dueDateFormatted,
        caseNumber: taskData.caseNumber || null,
        clientId: taskData.clientId || null,
        status: taskData.status || 'OPEN',
        type: taskData.type || 'TASK',
        location: taskData.location || null,
        repeat: taskData.repeat || 0
      };

      console.log('Gönderilen görev güncelleme verisi:', JSON.stringify(apiTaskData, null, 2));
      const response = await apiClient.put(`/api/tasks/${taskId}`, apiTaskData);
      console.log('API güncelleme yanıtı:', JSON.stringify(response.data, null, 2));
      return response.data;
    } catch (error) {
      console.error(`Update task ${taskId} error:`, error);
      throw error;
    }
  },

  // Görev sil
  deleteTask: async (taskId) => {
    try {
      console.log(`Sending DELETE request to /api/tasks/${taskId}`);

      // Get the token directly from tokenManager
      const token = await tokenManager.getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Use direct fetch instead of apiClient for DELETE request
      const url = `${API_URL}/api/tasks/${taskId}`;
      console.log(`Making direct fetch DELETE request to: ${url}`);

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Log the response status
      console.log(`Task ${taskId} deletion response status:`, response.status);

      // Parse the response data
      let responseData = {};
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.log('No JSON response body or parse error:', parseError);
      }

      console.log('Response data:', responseData);

      // Check if response status is successful (200-299)
      if (response.ok) {
        console.log(`Task ${taskId} deleted successfully with status ${response.status}`);
        return responseData;
      } else {
        console.error(`Unexpected status code ${response.status} when deleting task ${taskId}`);
        throw new Error(`Unexpected status code: ${response.status}`);
      }
    } catch (error) {
      console.error(`Delete task ${taskId} error:`, error);

      // Log more detailed error information
      if (error.response) {
        console.error('Error response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      throw error;
    }
  },

  // Göreve not ekle
  createNoteForTask: async (taskId, noteData) => {
    try {
      // API'ye sadece content gönder
      const response = await apiClient.post(`/api/tasks/${taskId}/notes`, {
        content: noteData.content
      });
      return response.data;
    } catch (error) {
      console.error(`Create note for task ${taskId} error:`, error);
      throw error;
    }
  },

  // Görevin notlarını getir
  getTaskNotes: async (taskId) => {
    try {
      const response = await apiClient.get(`/api/tasks/${taskId}/notes`);
      return response.data;
    } catch (error) {
      console.error(`Get notes for task ${taskId} error:`, error);
      throw error;
    }
  },

  // Not güncelle
  updateNote: async (noteId, noteData) => {
    try {
      // API'nin beklediği formata dönüştür
      const apiNoteData = {
        content: noteData.content
      };

      const response = await apiClient.put(`/api/tasks/notes/${noteId}`, apiNoteData);
      return response.data;
    } catch (error) {
      console.error(`Update note ${noteId} error:`, error);
      throw error;
    }
  },

  // Not sil
  deleteNote: async (noteId) => {
    try {
      console.log(`Sending DELETE request to /api/tasks/notes/${noteId}`);

      // Get the token directly from tokenManager
      const token = await tokenManager.getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Use direct fetch instead of apiClient for DELETE request
      const url = `${API_URL}/api/tasks/notes/${noteId}`;
      console.log(`Making direct fetch DELETE request to: ${url}`);

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Log the response status
      console.log(`Note ${noteId} deletion response status:`, response.status);

      // Check if response status is successful (200-299)
      if (response.ok) {
        console.log(`Note ${noteId} deleted successfully with status ${response.status}`);
        return { success: true };
      } else {
        console.error(`Unexpected status code ${response.status} when deleting note ${noteId}`);
        throw new Error(`Unexpected status code: ${response.status}`);
      }
    } catch (error) {
      console.error(`Delete note ${noteId} error:`, error);
      throw error;
    }
  },

  // Öncelik değerini API formatına dönüştür
  mapPriorityToApi: (priority) => {
    switch (priority) {
      case 'DÜŞÜK': return 'LOW';
      case 'ORTA': return 'MEDIUM';
      case 'YÜKSEK': return 'HIGH';
      case 'KRİTİK': return 'CRITICAL';
      default: return 'MEDIUM';
    }
  },

  // API öncelik değerini UI formatına dönüştür
  mapPriorityFromApi: (priority) => {
    switch (priority) {
      case 'LOW': return 'DÜŞÜK';
      case 'MEDIUM': return 'ORTA';
      case 'HIGH': return 'YÜKSEK';
      case 'CRITICAL': return 'KRİTİK';
      default: return 'ORTA';
    }
  },

  // Durum değerini API formatına dönüştür
  mapStatusToApi: (status) => {
    switch (status) {
      case 'AÇIK': return 'OPEN';
      case 'DEVAM EDİYOR': return 'IN_PROGRESS';
      case 'TAMAMLANDI': return 'COMPLETED';
      case 'İPTAL EDİLDİ': return 'CANCELLED';
      default: return 'OPEN';
    }
  },

  // API durum değerini UI formatına dönüştür
  mapStatusFromApi: (status) => {
    switch (status) {
      case 'OPEN': return 'AÇIK';
      case 'IN_PROGRESS': return 'DEVAM EDİYOR';
      case 'COMPLETED': return 'TAMAMLANDI';
      case 'CANCELLED': return 'İPTAL EDİLDİ';
      default: return 'AÇIK';
    }
  }
};

export default taskService;
