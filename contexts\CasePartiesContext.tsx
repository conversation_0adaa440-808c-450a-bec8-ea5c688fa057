import React, { createContext, useState, useContext, useEffect } from 'react';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import caseService from '@/services/caseService';

// Context tipi
type CasePartiesContextType = {
  allParties: Record<string, any[]>;
  loading: boolean;
  error: string | null;
  refreshParties: () => Promise<void>;
  fetchPartiesIfNeeded: () => Promise<void>;
  getPartiesForCase: (caseNumber: string) => any[];
};

// Context oluştur
const CasePartiesContext = createContext<CasePartiesContextType>({
  allParties: {},
  loading: true,
  error: null,
  refreshParties: async () => {},
  fetchPartiesIfNeeded: async () => {},
  getPartiesForCase: () => [],
});

// Context Provider bileşeni
export const CasePartiesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [allParties, setAllParties] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Tarafların yüklenip yüklenmediğini kontrol etmek için bir flag
  const [partiesLoaded, setPartiesLoaded] = useState(false);

  // Tüm tarafları getir - basitleştirildi ve hızlandırıldı
  const fetchAllParties = async (forceRefresh = false) => {
    try {
      // Eğer taraflar zaten yüklendiyse ve veri varsa, tekrar yükleme
      if (!forceRefresh && partiesLoaded && Object.keys(allParties).length > 0) {
        console.log('Parties already loaded, skipping fetch. Current keys:', Object.keys(allParties));
        return; // Zaten yüklenmiş, tekrar yükleme
      }

      setLoading(true);
      setError(null);

      // Token kontrolü yap
      const token = await AsyncStorage.getItem('auth_token');

      // No sample data - only use real API data

      if (!token) {
        console.log('No auth token found, keeping empty parties data');
        setAllParties({});
        setPartiesLoaded(true);
        setLoading(false);
        return;
      }

      try {
        console.log('Fetching parties from API...');

        // Use the caseService to get all parties
        const allPartiesResponse = await caseService.getAllCaseParties();
        console.log('Parties fetched successfully');

        // API yanıtını işle - Örnek verileri KULLANMA, tamamen API'den gelen verileri kullan
        const partiesData: Record<string, any[]> = {}; // Boş bir obje ile başla, örnek verileri kullanma

        // API yanıtını detaylı logla
        console.log('API Response Type:', typeof allPartiesResponse);
        console.log('API Response Length:', Array.isArray(allPartiesResponse) ? allPartiesResponse.length : 'Not an array');

        // Simplified processing logic for API response
        let processedData = false;

        // Case 1: API response is an array of parties
        if (Array.isArray(allPartiesResponse)) {
          console.log(`Processing ${allPartiesResponse.length} parties from API response...`);

          // Group parties by case number
          allPartiesResponse.forEach((party, index) => {
            try {
              if (party && party.dosyaNo) {
                const dosyaNo = party.dosyaNo.trim();

                if (!partiesData[dosyaNo]) {
                  partiesData[dosyaNo] = [];
                }

                // Add unique ID if missing
                const partyWithId = {
                  ...party,
                  id: party.id || `api-${Math.random().toString(36).substring(2, 11)}`
                };

                partiesData[dosyaNo].push(partyWithId);
                processedData = true;
              }
            } catch (error) {
              console.error('Error processing party:', error);
            }
          });
        }
        // Case 2: API response is an object with case numbers as keys
        else if (allPartiesResponse && typeof allPartiesResponse === 'object') {
          console.log('API response is an object, processing keys...');

          for (const dosyaNo in allPartiesResponse) {
            if (Array.isArray(allPartiesResponse[dosyaNo])) {
              console.log(`Processing ${allPartiesResponse[dosyaNo].length} parties for case ${dosyaNo}`);

              // Add unique IDs if missing
              const partiesWithIds = allPartiesResponse[dosyaNo].map(party => ({
                ...party,
                id: party.id || `api-${Math.random().toString(36).substring(2, 11)}`
              }));

              partiesData[dosyaNo] = partiesWithIds;
              processedData = true;
            }
          }
        }

        // Case 3: API response has a nested structure
        if (!processedData && allPartiesResponse) {
          console.log('Trying to process nested structure...');

          // Check if response has a data property
          if (allPartiesResponse.data && Array.isArray(allPartiesResponse.data)) {
            console.log(`Processing ${allPartiesResponse.data.length} parties from data property...`);

            allPartiesResponse.data.forEach((party: any) => {
              try {
                if (party && party.dosyaNo) {
                  const dosyaNo = party.dosyaNo.trim();

                  if (!partiesData[dosyaNo]) {
                    partiesData[dosyaNo] = [];
                  }

                  const partyWithId = {
                    ...party,
                    id: party.id || `api-${Math.random().toString(36).substring(2, 11)}`
                  };

                  partiesData[dosyaNo].push(partyWithId);
                  processedData = true;
                }
              } catch (error) {
                console.error('Error processing party from data property:', error);
              }
            });
          }

          // Check if response has a taraflar property
          if (!processedData && allPartiesResponse.taraflar && Array.isArray(allPartiesResponse.taraflar)) {
            console.log(`Processing ${allPartiesResponse.taraflar.length} parties from taraflar property...`);

            allPartiesResponse.taraflar.forEach((party: any) => {
              try {
                if (party && party.dosyaNo) {
                  const dosyaNo = party.dosyaNo.trim();

                  if (!partiesData[dosyaNo]) {
                    partiesData[dosyaNo] = [];
                  }

                  const partyWithId = {
                    ...party,
                    id: party.id || `api-${Math.random().toString(36).substring(2, 11)}`
                  };

                  partiesData[dosyaNo].push(partyWithId);
                  processedData = true;
                }
              } catch (error) {
                console.error('Error processing party from taraflar property:', error);
              }
            });
          }
        }

        // Log the result without using sample data
        if (!processedData || Object.keys(partiesData).length === 0) {
          console.log('No parties processed from API - keeping empty data');
        } else {
          console.log(`Successfully processed ${Object.keys(partiesData).length} case files with parties`);
        }

        // Add alternative formats for case numbers (with / and - variations)
        const processedKeys = [...Object.keys(partiesData)];
        for (const key of processedKeys) {
          if (key.includes('/')) {
            const altKey = key.replace('/', '-');
            if (!partiesData[altKey]) {
              partiesData[altKey] = [...partiesData[key]];
            }
          } else if (key.includes('-')) {
            const altKey = key.replace('-', '/');
            if (!partiesData[altKey]) {
              partiesData[altKey] = [...partiesData[key]];
            }
          }
        }

        console.log('Final case numbers with parties:', Object.keys(partiesData));

        // Set the processed data
        setAllParties(partiesData);
        setPartiesLoaded(true);
      } catch (apiError) {
        console.error('API error when fetching parties:', apiError);
        // Keep empty data instead of using sample data
        setAllParties({});
      }
    } catch (error: any) {
      console.error('Error fetching all parties:', error);
      // For all errors, set a generic message and keep empty data
      setError('Taraflar yüklenirken bir hata oluştu.');
      setAllParties({});
      setPartiesLoaded(true);
    } finally {
      setLoading(false);
    }
  };

  // Tarafları gerektiğinde yükle (davalar sayfasından çağrılacak)
  const fetchPartiesIfNeeded = async () => {
    try {
      // Eğer taraflar henüz yüklenmemişse veya boşsa, yükle
      if (!partiesLoaded || Object.keys(allParties).length === 0) {
        await fetchAllParties();
      }
      return true;
    } catch (error) {
      console.error('Error in fetchPartiesIfNeeded:', error);
      // Hata durumunda bile devam et
      return false;
    }
  };

  // Component mount olduğunda tarafları getir
  useEffect(() => {
    // Tarafları getir
    const loadParties = async () => {
      try {
        await fetchAllParties();
        console.log('Parties loaded successfully on mount');
      } catch (error) {
        console.error('Failed to load parties on mount:', error);
      }
    };

    loadParties();
  }, []);

  // Get parties for a specific case - improved with better logging and matching
  const getPartiesForCase = (caseNumber: string): any[] => {
    if (!caseNumber) {
      console.log('getPartiesForCase: No case number provided');
      return [];
    }

    console.log('getPartiesForCase: Looking for parties for case number:', caseNumber);

    // Check if we have any parties data at all
    if (!allParties || Object.keys(allParties).length === 0) {
      console.log('getPartiesForCase: No parties data available in context');
      return [];
    }

    console.log('getPartiesForCase: Available case numbers in context:', Object.keys(allParties));

    // Try different formats of the case number
    const formats = [
      caseNumber,
      caseNumber.replace('/', '-'),
      caseNumber.replace('-', '/'),
      // Try with and without leading zeros
      caseNumber.replace(/^(\d+)\/(\d+)$/, (_, year, num) => `${year}/${num.padStart(3, '0')}`),
      caseNumber.replace(/^(\d+)\/(\d+)$/, (_, year, num) => `${year}/${parseInt(num, 10)}`),
      // Try with different year formats
      caseNumber.replace(/^20(\d{2})\//, '$1/'),
      caseNumber.replace(/^(\d{2})\//, '20$1/'),
      // Try with trimmed whitespace
      caseNumber.trim(),
    ];

    // Remove duplicates and invalid formats
    const uniqueFormats = [...new Set(formats.filter(f => f))];
    console.log('getPartiesForCase: Trying formats:', uniqueFormats);

    // Check each format
    for (const format of uniqueFormats) {
      if (format && allParties[format] && Array.isArray(allParties[format]) && allParties[format].length > 0) {
        console.log(`getPartiesForCase: Found parties for format ${format}:`, allParties[format].length);
        return allParties[format];
      }
    }

    // If we didn't find an exact match, try a more flexible approach
    console.log('getPartiesForCase: No exact match found, trying partial matches...');
    for (const key in allParties) {
      // Try to normalize both strings for comparison (remove spaces, convert to lowercase)
      const normalizedKey = key.replace(/\s+/g, '').toLowerCase();
      const normalizedCaseNumber = caseNumber.replace(/\s+/g, '').toLowerCase();

      if (normalizedKey.includes(normalizedCaseNumber) || normalizedCaseNumber.includes(normalizedKey)) {
        console.log(`getPartiesForCase: Found partial match between ${key} and ${caseNumber}`);
        return allParties[key];
      }
    }

    // Try a more aggressive matching approach - match just the numeric part
    console.log('getPartiesForCase: No partial match found, trying numeric part matching...');
    const numericPart = caseNumber.match(/\d+/g);
    if (numericPart && numericPart.length > 0) {
      for (const key in allParties) {
        for (const num of numericPart) {
          if (key.includes(num) && allParties[key].length > 0) {
            console.log(`getPartiesForCase: Found numeric match between ${key} and ${caseNumber} (matching ${num})`);
            return allParties[key];
          }
        }
      }
    }

    // If still no match, return an empty array instead of sample data
    // This will allow the calling code to decide what to do
    console.log('getPartiesForCase: No parties found for case number:', caseNumber);
    return [];
  };

  // Context değeri
  const contextValue: CasePartiesContextType = {
    allParties,
    loading,
    error,
    refreshParties: () => fetchAllParties(true), // Zorla yenileme yap
    fetchPartiesIfNeeded,
    getPartiesForCase,
  };

  return (
    <CasePartiesContext.Provider value={contextValue}>
      {children}
    </CasePartiesContext.Provider>
  );
};

// Custom hook
export const useCaseParties = () => useContext(CasePartiesContext);

export default CasePartiesContext;
