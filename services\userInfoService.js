import apiClient from './api';

// Kullanıcı bilgileri servisi
const userInfoService = {
  // Kullanıcı detaylarını getir
  getUserDetails: async () => {
    try {
      const response = await apiClient.get('/api/user/details');
      return response.data;
    } catch (error) {
      console.error('Get user details error:', error);
      throw error;
    }
  },
  
  // Kullanıcı fotoğrafını getir
  getUserPhoto: async () => {
    try {
      const response = await apiClient.get('/api/user/photo');
      return response.data;
    } catch (error) {
      console.error('Get user photo error:', error);
      throw error;
    }
  },
  
  // Kullanıcı bildirimlerini getir
  getUserNotifications: async () => {
    try {
      const response = await apiClient.get('/api/user/notifications');
      return response.data;
    } catch (error) {
      console.error('Get user notifications error:', error);
      throw error;
    }
  },
  
  // Kullanıcı duruşmalarını getir
  getUserTrials: async () => {
    try {
      const response = await apiClient.get('/api/user/trials');
      return response.data;
    } catch (error) {
      console.error('Get user trials error:', error);
      throw error;
    }
  },
  
  // UYAP duyurularını getir
  getUyapAnnouncements: async () => {
    try {
      const response = await apiClient.get('/api/user/announcements');
      return response.data;
    } catch (error) {
      console.error('Get UYAP announcements error:', error);
      throw error;
    }
  }
};

export default userInfoService;
