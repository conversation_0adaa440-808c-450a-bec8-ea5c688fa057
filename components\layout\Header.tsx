import React, { useState, useContext, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Platform, Dimensions, Text } from 'react-native';

import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { router, usePathname } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import NotificationCenter from '@/components/notifications/NotificationCenter';
import { AuthContext } from '@/app/_layout';
import notificationService from '@/services/notificationService';

interface HeaderProps {
  title?: string;
  showMenuButton?: boolean;
  onMenuPress?: () => void;
}

export default function Header({ title = 'AVAS', showMenuButton = false, onMenuPress }: HeaderProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';
  const isWideScreen = isWeb && width > 768;
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  // Mevcut sayfayı belirlemek için pathname kullan
  const pathname = usePathname();
  // AuthContext'i kullanmıyoruz ama import edilmiş olması gerekiyor
  // const { logout } = useContext(AuthContext);

  // Bildirimleri yükle
  useEffect(() => {
    fetchNotifications();

    // 1 dakikada bir bildirimleri güncelle
    const interval = setInterval(() => {
      fetchNotifications();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Bildirimleri getir
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      console.log('Header: Bildirimler getiriliyor...');
      const data = await notificationService.getUserNotifications();

      console.log('Header: Bildirim verileri alındı:', data);

      if (Array.isArray(data)) {
        setNotifications(data);
        // Okunmamış bildirim sayısını hesapla
        const unreadCount = data.filter(notification => !notification.okunduMu).length;
        console.log('Header: Okunmamış bildirim sayısı:', unreadCount);
        setNotificationCount(unreadCount);
      } else {
        console.warn('Header: Bildirim verisi dizi değil veya boş');
        setNotifications([]);
        setNotificationCount(0);
      }
    } catch (error) {
      console.error('Header: Bildirimler alınırken hata oluştu:', error);
      setNotifications([]);
      setNotificationCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  return (
    <>
      <View style={styles.headerContainer}>
        <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
          <View style={styles.header}>
            <View style={styles.leftSection}>
              {showMenuButton && isWideScreen && (
                <TouchableOpacity style={styles.menuButton} onPress={onMenuPress}>
                  <Ionicons name="menu-outline" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              )}
              <View style={styles.titleContainer}>
                <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                <Text style={styles.titleText}>AVAS</Text>
                {title !== 'AVAS' && <Text style={styles.subtitleText}> | {title}</Text>}
                {pathname === '/(tabs)/cases' && (
                  <View style={styles.pageIndicator}>
                    <Ionicons name="folder-open" size={18} color="#FFFFFF" style={{marginRight: 4}} />
                    <Text style={styles.pageIndicatorText}>Dosyalar</Text>
                  </View>
                )}
              </View>
            </View>

            <View style={styles.rightSection}>
              <TouchableOpacity style={styles.iconButton} onPress={toggleNotifications}>
                <View>
                  <Ionicons
                    name={isLoading ? "notifications-circle-outline" : "notifications-outline"}
                    size={22}
                    color="#FFFFFF"
                  />
                  {notificationCount > 0 && (
                    <View style={styles.notificationBadge}>
                      {notificationCount > 9 ? (
                        <Text style={styles.notificationBadgeText}>9+</Text>
                      ) : notificationCount > 0 ? (
                        <Text style={styles.notificationBadgeText}>{notificationCount}</Text>
                      ) : null}
                    </View>
                  )}
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => router.push('/(tabs)/profile')}
              >
                <Ionicons name="person-outline" size={22} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      <NotificationCenter
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
        initialNotifications={notifications}
      />
    </>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    width: '100%',
    zIndex: 10,
  },
  headerBackground: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerBackgroundLight: {
    backgroundColor: '#4A78B0',
  },
  headerBackgroundDark: {
    backgroundColor: '#2C4A6B',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitleText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  pageIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginLeft: 12,
  },
  pageIndicatorText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  logo: {
    marginRight: 8,
  },
  menuButton: {
    marginRight: 16,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginLeft: 16,
  },
  logoutText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#EF4444',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 2,
  },
  notificationBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
