import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Message } from '@/services/lawbotService';

interface ChatMessageProps {
  message: Message;
}

export default function ChatMessage({ message }: ChatMessageProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isUser = message.role === 'user';

  // On Android, use a regular View instead of BlurView for better performance
  const MessageContainer = Platform.OS === 'android' ? View : BlurView;

  return (
    <View style={[
      styles.container,
      isUser ? styles.userContainer : styles.assistantContainer
    ]}>
      <MessageContainer
        intensity={Platform.OS !== 'android' ? (isDark ? 40 : 60) : undefined}
        tint={Platform.OS !== 'android' ? (isDark ? 'dark' : 'light') : undefined}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessage : styles.assistantMessage,
          Platform.OS === 'android' && {
            backgroundColor: isDark ? '#333' : '#e6e6e6',
          }
        ]}
      >
        <ThemedText style={styles.messageText}>{message.content}</ThemedText>
        <ThemedText style={styles.timestamp}>
          {message.createdAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </ThemedText>
      </MessageContainer>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    maxWidth: Platform.OS === 'web' ? '80%' : '85%',
    alignSelf: 'flex-start',
  },
  userContainer: {
    alignSelf: 'flex-end',
  },
  assistantContainer: {
    alignSelf: 'flex-start',
  },
  messageContainer: {
    borderRadius: 16,
    padding: 12,
    overflow: 'hidden',
    // Add shadow for Android
    ...Platform.select({
      android: {
        elevation: 1,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
      },
    }),
  },
  userMessage: {
    borderTopRightRadius: 4,
  },
  assistantMessage: {
    borderTopLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 12,
    opacity: 0.6,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
});
