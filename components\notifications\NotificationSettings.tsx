import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Switch, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import Slider from '@react-native-community/slider';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import notificationService from '@/services/notificationService';

interface NotificationSettingsProps {
  onClose: () => void;
}

export default function NotificationSettings({ onClose }: NotificationSettingsProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [settings, setSettings] = useState({
    enabled: true,
    taskReminders: true,
    hearingReminders: true,
    deadlineReminders: true,
    reminderTime: 30, // Dakika cinsinden varsayılan hatırlatma süresi
  });
  
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    loadSettings();
  }, []);
  
  const loadSettings = async () => {
    try {
      setLoading(true);
      const savedSettings = await notificationService.getNotificationSettings();
      if (savedSettings) {
        setSettings(savedSettings);
      }
    } catch (err) {
      console.error('Error loading notification settings:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const saveSettings = async () => {
    try {
      await notificationService.saveNotificationSettings(settings);
      Alert.alert('Başarılı', 'Bildirim ayarları kaydedildi.');
      onClose();
    } catch (err) {
      console.error('Error saving notification settings:', err);
      Alert.alert('Hata', 'Bildirim ayarları kaydedilirken bir hata oluştu.');
    }
  };
  
  const formatReminderTime = (minutes) => {
    if (minutes < 60) {
      return `${minutes} dakika önce`;
    } else if (minutes === 60) {
      return '1 saat önce';
    } else if (minutes < 1440) { // 24 saat
      const hours = Math.floor(minutes / 60);
      return `${hours} saat önce`;
    } else if (minutes === 1440) {
      return '1 gün önce';
    } else {
      const days = Math.floor(minutes / 1440);
      return `${days} gün önce`;
    }
  };
  
  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <ThemedText type="subtitle">Bildirim Ayarları</ThemedText>
        <TouchableOpacity onPress={onClose}>
          <Ionicons name="close" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.settingsCard}>
          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <ThemedText type="defaultSemiBold">Bildirimleri Etkinleştir</ThemedText>
              <ThemedText style={styles.settingDescription}>
                Tüm bildirimleri açın veya kapatın
              </ThemedText>
            </View>
            <Switch
              value={settings.enabled}
              onValueChange={(value) => setSettings({ ...settings, enabled: value })}
              trackColor={{ false: '#767577', true: Colors.light.tint }}
              thumbColor="#f4f3f4"
            />
          </View>
          
          {settings.enabled && (
            <>
              <View style={styles.divider} />
              
              <View style={styles.settingItem}>
                <View style={styles.settingTextContainer}>
                  <ThemedText type="defaultSemiBold">Görev Hatırlatıcıları</ThemedText>
                  <ThemedText style={styles.settingDescription}>
                    Görevler için hatırlatıcı bildirimleri alın
                  </ThemedText>
                </View>
                <Switch
                  value={settings.taskReminders}
                  onValueChange={(value) => setSettings({ ...settings, taskReminders: value })}
                  trackColor={{ false: '#767577', true: Colors.light.tint }}
                  thumbColor="#f4f3f4"
                />
              </View>
              
              <View style={styles.divider} />
              
              <View style={styles.settingItem}>
                <View style={styles.settingTextContainer}>
                  <ThemedText type="defaultSemiBold">Duruşma Hatırlatıcıları</ThemedText>
                  <ThemedText style={styles.settingDescription}>
                    Duruşmalar için hatırlatıcı bildirimleri alın
                  </ThemedText>
                </View>
                <Switch
                  value={settings.hearingReminders}
                  onValueChange={(value) => setSettings({ ...settings, hearingReminders: value })}
                  trackColor={{ false: '#767577', true: Colors.light.tint }}
                  thumbColor="#f4f3f4"
                />
              </View>
              
              <View style={styles.divider} />
              
              <View style={styles.settingItem}>
                <View style={styles.settingTextContainer}>
                  <ThemedText type="defaultSemiBold">Son Tarih Hatırlatıcıları</ThemedText>
                  <ThemedText style={styles.settingDescription}>
                    Son tarihler için hatırlatıcı bildirimleri alın
                  </ThemedText>
                </View>
                <Switch
                  value={settings.deadlineReminders}
                  onValueChange={(value) => setSettings({ ...settings, deadlineReminders: value })}
                  trackColor={{ false: '#767577', true: Colors.light.tint }}
                  thumbColor="#f4f3f4"
                />
              </View>
              
              <View style={styles.divider} />
              
              <View style={styles.sliderContainer}>
                <ThemedText type="defaultSemiBold">Hatırlatma Zamanı</ThemedText>
                <ThemedText style={styles.settingDescription}>
                  Etkinliklerden ne kadar önce hatırlatılmak istediğinizi seçin
                </ThemedText>
                
                <Slider
                  style={styles.slider}
                  minimumValue={5}
                  maximumValue={1440} // 24 saat
                  step={5}
                  value={settings.reminderTime}
                  onValueChange={(value) => setSettings({ ...settings, reminderTime: value })}
                  minimumTrackTintColor={Colors.light.tint}
                  maximumTrackTintColor={isDark ? '#555' : '#ccc'}
                  thumbTintColor={Colors.light.tint}
                />
                
                <ThemedText style={styles.sliderValue}>
                  {formatReminderTime(settings.reminderTime)}
                </ThemedText>
              </View>
            </>
          )}
        </BlurView>
      </ScrollView>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
          <ThemedText style={styles.cancelButtonText}>İptal</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.saveButton} onPress={saveSettings}>
          <ThemedText style={styles.saveButtonText}>Kaydet</ThemedText>
        </TouchableOpacity>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  content: {
    flex: 1,
  },
  settingsCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  settingDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(150, 150, 150, 0.2)',
    marginVertical: 4,
  },
  sliderContainer: {
    paddingVertical: 16,
  },
  slider: {
    width: '100%',
    height: 40,
    marginTop: 8,
  },
  sliderValue: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.tint,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: Colors.light.tint,
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
