import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Platform } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function Footer() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View style={[
      styles.footer,
      {
        backgroundColor: isDark ? '#2C4A6B' : '#4A78B0',
        borderTopColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
      }
    ]}>
      <View style={styles.content}>
        <View style={styles.leftSection}>
          <View style={styles.logoContainer}>
            <MaterialCommunityIcons name="scale-balance" size={18} color="#FFFFFF" style={styles.logo} />
            <Text style={styles.logoText}>AVAS</Text>
          </View>
          <Text style={styles.copyright}>© 2025 AVAS - Avukatlık Asistanı. Tüm hakları saklıdır.</Text>
        </View>

        <View style={styles.rightSection}>
          <TouchableOpacity style={styles.footerLink}>
            <Text style={styles.linkText}>Gizlilik Politikası</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.footerLink}>
            <Text style={styles.linkText}>Kullanım Şartları</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.footerLink}>
            <Text style={styles.linkText}>Yardım</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.footerLink}>
            <Text style={styles.linkText}>İletişim</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  footer: {
    width: '100%',
    backgroundColor: '#4A78B0',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 0, // Changed from 16 to 0 to remove gap
    // Not sticky - appears after the content ends
    left: 0,
    right: 0,
    zIndex: 10,
    position: 'relative',
    bottom: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
    width: '100%', // Full width like the header
    maxWidth: '100%', // Full width like the header
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  leftSection: {
    flexDirection: 'column',
    flex: 1,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  logo: {
    marginRight: 6,
  },
  logoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  copyright: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
  },
  footerLink: {
    marginLeft: 24,
    paddingVertical: 4,
  },
  linkText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
    opacity: 0.9,
  },
});
