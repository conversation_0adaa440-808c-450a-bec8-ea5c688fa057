import React, { useState, useEffect } from 'react';
import { View, ScrollView, TouchableOpacity, ActivityIndicator, StyleSheet, FlatList, Platform, Text, Dimensions, TextInput } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService from '@/services/notificationService';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import caseService from '@/services/caseService';
import caseNoteService from '@/services/caseNoteService';
import AddCaseDetailModal from '@/components/cases/AddCaseDetailModal';
import { useCaseParties } from '@/contexts/CasePartiesContext';
import NotificationCenter from '@/components/notifications/NotificationCenter';
import BackgroundWrapper from '@/components/BackgroundWrapper';

export default function CaseDetailScreen() {
  const { id, dosyaNo, from } = useLocalSearchParams();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';
  const isWideScreen = isWeb && width > 768;

  // Store the source of navigation for back button
  const [navigatedFrom, setNavigatedFrom] = useState<string | null>(null);

  // Notification state
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      setIsLoadingNotifications(true);
      console.log('Header: Bildirimler getiriliyor...');
      const data = await notificationService.getUserNotifications();

      console.log('Header: Bildirim verileri alındı:', data);

      if (Array.isArray(data)) {
        setNotifications(data);
        // Okunmamış bildirim sayısını hesapla
        const unreadCount = data.filter(notification => !notification.okunduMu).length;
        console.log('Header: Okunmamış bildirim sayısı:', unreadCount);
        setNotificationCount(unreadCount);
      } else {
        console.warn('Header: Bildirim verisi dizi değil veya boş');
        setNotifications([]);
        setNotificationCount(0);
      }
    } catch (error) {
      console.error('Header: Bildirimler alınırken hata oluştu:', error);
      setNotifications([]);
      setNotificationCount(0);
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  // Load notifications on mount
  useEffect(() => {
    fetchNotifications();

    // Refresh notifications every minute
    const interval = setInterval(() => {
      fetchNotifications();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Define interfaces for our data types
  interface CaseData {
    dosyaId?: string;
    dosyaNo?: string;
    esasYil?: string;
    dosyaNumara?: string;
    birimAdi?: string;
    dosyaTur?: string;
    dosyaDurum?: string;
    acilisTarihi?: string;
    status?: 'Active' | 'Closed';
    [key: string]: any;
  }

  interface CaseDetailData {
    id?: number;
    caseNumber?: string;
    caseType?: string;
    crimeType?: string;
    derdest?: boolean;
    caseValue?: string | number;
    caseReason?: string;
    caseTitle?: string;
    createdAt?: string;
    updatedAt?: string;
    ownerId?: number;
    ownerName?: string;
    ownerEmail?: string;
    [key: string]: any;
  }

  interface CaseParty {
    isim?: string;
    soyad?: string;
    sifat?: string;
    [key: string]: any;
  }

  interface CaseHistoryItem {
    tarih?: string;
    islemTuru?: string;
    aciklama?: string;
    [key: string]: any;
  }

  interface CaseNote {
    id?: string;
    title?: string;
    content?: string;
    createdAt?: string;
    [key: string]: any;
  }

  interface CaseCollectionDenial {
    toplamTahsilat?: string | number;
    toplamReddiyat?: string | number;
    netBakiye?: string | number;
    [key: string]: any;
  }

  interface CaseDetailFormData {
    caseType: string;
    crimeType: string;
    derdest: boolean;
    caseValue: string;
    caseReason: string;
    caseTitle: string;
  }

  // Get case parties from context
  const { getPartiesForCase, fetchPartiesIfNeeded } = useCaseParties();

  // State
  const [activeTab, setActiveTab] = useState('bilgiler');
  const [caseData, setCaseData] = useState<CaseData | null>(null);
  const [caseDetailData, setCaseDetailData] = useState<CaseDetailData | null>(null);
  const [caseParties, setCaseParties] = useState<CaseParty[]>([]);
  const [caseHistory, setCaseHistory] = useState<CaseHistoryItem[]>([]);
  const [caseNotes, setCaseNotes] = useState<CaseNote[]>([]);
  const [caseCollectionDenial, setCaseCollectionDenial] = useState<CaseCollectionDenial | null>(null);
  const [safahatInfo, setSafahatInfo] = useState<any>(null);
  const [tahsilatReddiyatData, setTahsilatReddiyatData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddDetailModal, setShowAddDetailModal] = useState(false);
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);
  const [noteTitle, setNoteTitle] = useState('');
  const [noteContent, setNoteContent] = useState('');
  const [addingNote, setAddingNote] = useState(false);
  const [deletingNoteId, setDeletingNoteId] = useState<string | number | null>(null);

  // Set the navigated from state based on URL parameter
  useEffect(() => {
    if (from) {
      // Set navigatedFrom based on the 'from' query parameter
      setNavigatedFrom(from as string);
      console.log('Setting navigatedFrom from URL parameter:', from);
    } else if (isWeb) {
      // Check localStorage for previous path
      const previousPath = localStorage.getItem('previousPath');
      if (previousPath) {
        if (previousPath.includes('/clients')) {
          setNavigatedFrom('clients');
          console.log('Setting navigatedFrom from localStorage (clients)');
        } else if (previousPath.includes('/cases')) {
          setNavigatedFrom('cases');
          console.log('Setting navigatedFrom from localStorage (cases)');
        }
      }
    }
  }, [from, isWeb]);

  // Log safahat information when the Safahat tab is opened
  useEffect(() => {
    if (activeTab === 'safahat') {
      console.log('Safahat tab opened, safahat info:', safahatInfo);

      // Only log the safahat info when the tab is opened, don't make a new request
      // This avoids triggering the rate limit
      if (safahatInfo) {
        console.log('Using existing safahat info');

        if (safahatInfo.error) {
          console.log('SAFAHAT ERROR DETECTED:', safahatInfo.error);
        } else if (safahatInfo.safahatlar) {
          console.log('SAFAHAT ENTRIES COUNT:', safahatInfo.safahatlar.length);
        }

        // If we have cached data that's marked as expired, show a notification
        if (safahatInfo.isExpiredCache) {
          console.log('Using expired cached data for safahat');
        }
      } else {
        console.log('No safahat info available yet');

        // Only fetch if we don't have any data at all
        const fetchSafahatIfNeeded = async () => {
          try {
            if (caseData && !safahatInfo) {
              const formattedCaseNumber = formatCaseNumber(caseData);
              console.log('Fetching safahat for case number (no existing data):', formattedCaseNumber);

              // Ensure case number is properly formatted
              const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
                ? formattedCaseNumber.replace('-', '/')
                : formattedCaseNumber;

              // Make a direct API call only if we don't have any data
              const response = await caseService.getSafahatInfo(formattedCaseNumberForAPI);
              console.log('SAFAHAT RESPONSE:', response);

              // Update state with response
              setSafahatInfo(response);
            }
          } catch (error) {
            console.error('Error fetching safahat:', error);
          }
        };

        fetchSafahatIfNeeded();
      }
    }
  }, [activeTab, caseData, safahatInfo]);

  // Fetch case data
  useEffect(() => {
    const fetchCaseData = async () => {
      try {
        setLoading(true);
        setError('');

        // Ensure parties data is loaded
        await fetchPartiesIfNeeded();

        // Check for token
        const token = await AsyncStorage.getItem('auth_token');
        if (!token) {
          setLoading(false);
          setError('Oturum açık değil. Lütfen tekrar giriş yapın.');
          return;
        }

        // First check if we have case data in localStorage from the previous page
        let storedCaseData = null;
        if (Platform.OS === 'web') {
          const storedData = localStorage.getItem('selectedCaseData');
          if (storedData) {
            try {
              storedCaseData = JSON.parse(storedData);
              console.log('Found stored case data:', storedCaseData);
              setCaseData(storedCaseData);
              // Clear the stored data to avoid using stale data on refresh
              localStorage.removeItem('selectedCaseData');
            } catch (e) {
              console.error('Error parsing stored case data:', e);
            }
          }
        }

        // If dosyaNo is provided directly in URL params, use it first
        let formattedCaseNumber = dosyaNo as string;
        console.log('URL provided dosyaNo:', formattedCaseNumber);

        // If we have stored case data, use it
        if (storedCaseData) {
          // Use the formatted dosya number from stored case data
          if (!formattedCaseNumber) {
            formattedCaseNumber = formatCaseNumber(storedCaseData);
          }
        }
        // If no stored data and no dosyaNo in URL, fetch case details by ID
        else if (!formattedCaseNumber) {
          console.log('No dosyaNo in URL, fetching case by ID:', id);
          const caseDetails = await caseService.getCaseById(id as string);
          setCaseData(caseDetails);

          // Get the formatted dosya number from case details
          formattedCaseNumber = formatCaseNumber(caseDetails);
        }

        console.log('Using case number for API calls:', formattedCaseNumber);

        if (formattedCaseNumber) {
          // Fetch case detail data from new API endpoint
          try {
            // Use the direct API endpoint with the case number
            console.log(`Fetching case details with case number: ${formattedCaseNumber}`);
            const detailData = await caseService.getCaseDetailsByCaseNumber(formattedCaseNumber);

            setCaseDetailData(detailData);
            console.log('Case detail data:', detailData);
          } catch (detailError: any) {
            console.error('Error fetching case detail data:', detailError);
            // If we get a 404, it means the case details haven't been added yet
            if (detailError.response && detailError.response.status === 404) {
              console.log('Case details not found, need to be added');
            } else {
              // Log more detailed error information
              console.error('Detail error object:', JSON.stringify(detailError, null, 2));
              if (detailError.response) {
                console.error('Error response data:', detailError.response.data);
                console.error('Error response status:', detailError.response.status);
              }
            }
          }

          // Try to get parties from context first
          console.log('Formatted case number for parties:', formattedCaseNumber);

          // Initialize with empty array instead of sample data
          setCaseParties([]);

          // Try to get real data from context
          const contextParties = getPartiesForCase(formattedCaseNumber);
          console.log('Context parties result:', contextParties ? contextParties.length : 0, 'parties found');

          if (contextParties && contextParties.length > 0) {
            console.log('Using parties from context:', contextParties.length);

            // Map the parties to ensure they have both old and new field names for compatibility
            const mappedParties = contextParties.map(party => {
              return {
                id: party.id || `party-${Math.random().toString(36).substring(2, 11)}`,
                // Support both naming conventions
                isim: party.adi || party.isim || party.name || 'İsimsiz',
                soyad: party.soyad || party.surname || '',
                sifat: party.rol || party.sifat || party.role || 'Belirtilmemiş',
                // Keep original fields too
                adi: party.adi || party.isim || party.name || 'İsimsiz',
                rol: party.rol || party.sifat || party.role || 'Belirtilmemiş',
                kisiKurum: party.kisiKurum || party.type || 'Kişi',
                dosyaNo: party.dosyaNo || formattedCaseNumber
              };
            });

            setCaseParties(mappedParties);
          } else {
            // If no parties in context, try API
            try {
              console.log('Fetching parties from API for case:', formattedCaseNumber);
              // Ensure case number is properly formatted
              const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
                ? formattedCaseNumber.replace('-', '/')
                : formattedCaseNumber;
              const parties = await caseService.getCaseParties(formattedCaseNumberForAPI);
              console.log('API parties result:', parties ? parties.length : 0, 'parties found');

              if (parties && parties.length > 0) {
                console.log('Using parties from API:', parties.length);

                // Map the parties to ensure they have both old and new field names for compatibility
                const mappedParties = parties.map((party: { id: any; adi: any; isim: any; name: any; soyad: any; surname: any; rol: any; sifat: any; role: any; kisiKurum: any; type: any; dosyaNo: any; }) => {
                  return {
                    id: party.id || `api-${Math.random().toString(36).substring(2, 11)}`,
                    // Support both naming conventions
                    isim: party.adi || party.isim || party.name || 'İsimsiz',
                    soyad: party.soyad || party.surname || '',
                    sifat: party.rol || party.sifat || party.role || 'Belirtilmemiş',
                    // Keep original fields too
                    adi: party.adi || party.isim || party.name || 'İsimsiz',
                    rol: party.rol || party.sifat || party.role || 'Belirtilmemiş',
                    kisiKurum: party.kisiKurum || party.type || 'Kişi',
                    dosyaNo: party.dosyaNo || formattedCaseNumber
                  };
                });

                setCaseParties(mappedParties);
              } else {
                console.log('No parties found in API response');
                // Keep empty array - no sample data
              }
            } catch (partiesError) {
              console.error('Error fetching case parties from API:', partiesError);
              console.log('Error fetching parties - keeping empty array');
              // Keep empty array - no sample data
            }
          }

          // Fetch case history
          try {
            // Ensure case number is properly formatted
            const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
              ? formattedCaseNumber.replace('-', '/')
              : formattedCaseNumber;
            const history = await caseService.getCaseHistory(formattedCaseNumberForAPI);
            setCaseHistory(history || []);
          } catch (historyError) {
            console.error('Error fetching case history:', historyError);
          }

          // Fetch safahat info
          try {
            console.log('Starting safahat info fetch for case number:', formattedCaseNumber);
            // Ensure case number is properly formatted
            const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
              ? formattedCaseNumber.replace('-', '/')
              : formattedCaseNumber;
            const info = await caseService.getSafahatInfo(formattedCaseNumberForAPI);

            // Parse the response if it's a string
            let parsedInfo = info;
            if (typeof info === 'string') {
              try {
                parsedInfo = JSON.parse(info);
              } catch (parseError) {
                console.error('Error parsing safahat info:', parseError);
                // If parsing fails, use the original string
                parsedInfo = info;
              }
            }

            setSafahatInfo(parsedInfo);
            console.log('Safahat info received:', parsedInfo);
            console.log('Safahat info type:', typeof parsedInfo);

            if (parsedInfo && parsedInfo.safahatlar) {
              console.log('Safahat entries count:', parsedInfo.safahatlar.length);
            }
          } catch (safahatError) {
            console.error('Error fetching safahat info:', safahatError);
            console.error('Safahat error details:', JSON.stringify(safahatError, null, 2));
            setSafahatInfo(null);
          }

          // Fetch tahsilat-reddiyat data
          try {
            console.log('Starting tahsilat-reddiyat fetch for case number:', formattedCaseNumber);
            // Ensure case number is properly formatted
            const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
              ? formattedCaseNumber.replace('-', '/')
              : formattedCaseNumber;
            const tahsilatReddiyat = await caseService.getCaseCollectionAndDenial(formattedCaseNumberForAPI);
            setTahsilatReddiyatData(tahsilatReddiyat);
            console.log('Tahsilat-reddiyat data received:', tahsilatReddiyat);
          } catch (tahsilatError) {
            console.error('Error fetching tahsilat-reddiyat data:', tahsilatError);
            console.error('Tahsilat-reddiyat error details:', JSON.stringify(tahsilatError, null, 2));
            setTahsilatReddiyatData(null);
          }

          // Fetch case notes
          try {
            // Ensure case number is properly formatted
            const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
              ? formattedCaseNumber.replace('-', '/')
              : formattedCaseNumber;
            const notes = await caseNoteService.getCaseNotesByCaseNumber(formattedCaseNumberForAPI);
            setCaseNotes(notes || []);
          } catch (notesError) {
            console.error('Error fetching case notes:', notesError);
          }

          // Fetch collection and denial
          try {
            // Ensure case number is properly formatted
            const formattedCaseNumberForAPI = formattedCaseNumber.includes('-')
              ? formattedCaseNumber.replace('-', '/')
              : formattedCaseNumber;
            const collectionDenial = await caseService.getCaseCollectionAndDenial(formattedCaseNumberForAPI);
            setCaseCollectionDenial(collectionDenial || null);
          } catch (collectionError) {
            console.error('Error fetching collection and denial:', collectionError);
          }
        }
      } catch (error: any) {
        console.error('Error fetching case data:', error);
        // More detailed error message for debugging
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);
          setError(`API Error (${error.response.status}): ${JSON.stringify(error.response.data)}`);
        } else if (error.request) {
          // The request was made but no response was received
          console.error('Error request:', error.request);
          setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error message:', error.message);
          setError(`Dava bilgileri yüklenirken bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCaseData();
  }, [id, dosyaNo]);

  // Delete case details
  const handleDeleteCaseDetails = async () => {
    try {
      if (!caseDetailData || !caseDetailData.id) {
        console.error('No case detail data or ID available');
        setError('Dava detayı bulunamadı. Lütfen tekrar deneyin.');
        return;
      }

      // Confirm deletion
      if (!confirm('Dava detayını silmek istediğinize emin misiniz? Bu işlem geri alınamaz.')) {
        return;
      }

      setLoading(true);
      console.log(`Deleting case detail with ID: ${caseDetailData.id}`);

      // Send the delete request
      await caseService.deleteCaseDetail(caseDetailData.id);
      console.log('Case detail deleted successfully');

      // Clear the case detail data
      setCaseDetailData(null);

      // Show success message
      alert('Dava detayı başarıyla silindi.');
    } catch (error: any) {
      console.error('Error deleting case details:', error);

      // More detailed error message
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        setError(`API Hatası (${error.response.status}): ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        console.error('Error request:', error.request);
        setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        console.error('Error message:', error.message);
        setError(`Dava detayı silinirken bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Add or update case details
  const handleAddCaseDetails = async (details: CaseDetailFormData) => {
    try {
      setLoading(true);

      // Use dosyaNo from URL if available, otherwise use the one from caseData
      let formattedCaseNumber = dosyaNo as string;
      if (!formattedCaseNumber) {
        formattedCaseNumber = formatCaseNumber(caseData);
      }

      if (!formattedCaseNumber) {
        console.error('No case number available');
        setError('Dava numarası bulunamadı. Lütfen tekrar deneyin.');
        return;
      }

      console.log('Using case number for details:', formattedCaseNumber);

      // Get the opening date from caseData if available
      let openingDate = '';
      if (caseData && caseData.acilisTarihi) {
        openingDate = caseData.acilisTarihi;
        console.log('Using opening date from caseData:', openingDate);
      } else if (caseData && caseData.dosyaAcilisTarihi && caseData.dosyaAcilisTarihi.date) {
        const date = caseData.dosyaAcilisTarihi.date;
        openingDate = `${date.day}.${date.month}.${date.year}`;
        console.log('Using opening date from dosyaAcilisTarihi:', openingDate);
      }

      // Convert caseValue to number if it's a string
      let caseValueNum: number | null = null;
      if (typeof details.caseValue === 'string' && details.caseValue.trim() !== '') {
        caseValueNum = parseFloat(details.caseValue);
        // Check if the conversion resulted in a valid number
        if (isNaN(caseValueNum)) {
          caseValueNum = null;
        }
      } else if (typeof details.caseValue === 'number') {
        caseValueNum = details.caseValue;
      }

      // Prepare the request payload
      const caseDetails = {
        caseNumber: formattedCaseNumber,
        caseType: details.caseType,
        // Send empty string instead of null for crimeType when not CEZA_DAVASI
        crimeType: details.caseType === 'CEZA_DAVASI' ? details.crimeType : "",
        derdest: details.derdest,
        caseValue: caseValueNum,
        caseReason: details.caseReason || "",
        caseTitle: details.caseTitle
      };

      // Log the payload for debugging
      console.log('Case details payload:', JSON.stringify(caseDetails, null, 2));

      console.log('Sending case details to API:', JSON.stringify(caseDetails, null, 2));

      let response;

      // Check if we're updating or adding
      if (caseDetailData && caseDetailData.id) {
        // Update existing case details
        console.log(`Updating case detail with ID: ${caseDetailData.id}`);
        response = await caseService.updateCaseDetail(caseDetailData.id, caseDetails);
        console.log('Update API response:', response);
      } else {
        // Add new case details
        console.log('Adding new case details');
        response = await caseService.addCaseDetails(caseDetails);
        console.log('Add API response:', response);
      }

      // Fetch updated case detail data
      const updatedDetailData = await caseService.getCaseDetailsByCaseNumber(formattedCaseNumber);
      setCaseDetailData(updatedDetailData);
      console.log('Updated case detail data:', updatedDetailData);

      // Close modal
      setShowAddDetailModal(false);

      // Show success message
      const actionType = caseDetailData && caseDetailData.id ? 'güncellendi' : 'eklendi';
      alert(`Dava detayı başarıyla ${actionType}.`);
    } catch (error: any) {
      console.error('Error with case details:', error);

      // More detailed error message
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        setError(`API Hatası (${error.response.status}): ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        console.error('Error request:', error.request);
        setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        console.error('Error message:', error.message);
        const actionType = caseDetailData && caseDetailData.id ? 'güncellenirken' : 'eklenirken';
        setError(`Dava detayı ${actionType} bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // This section has been moved above

  // Add a new case note
  const handleAddNote = async () => {
    try {
      // Validate input
      if (!noteContent.trim()) {
        alert('Lütfen not içeriğini giriniz.');
        return;
      }

      // Get the case number
      let formattedCaseNumber = dosyaNo as string;
      if (!formattedCaseNumber) {
        formattedCaseNumber = formatCaseNumber(caseData);
      }

      if (!formattedCaseNumber) {
        console.error('No case number available');
        setError('Dava numarası bulunamadı. Lütfen tekrar deneyin.');
        return;
      }

      // Set loading state
      setAddingNote(true);

      // Format the content with title if provided
      const finalContent = noteTitle.trim()
        ? `${noteTitle}\n\n${noteContent}`
        : noteContent;

      // Prepare the request payload
      const noteData = {
        content: finalContent,
        caseNumber: formattedCaseNumber
      };

      console.log('Adding new case note:', noteData);

      // Send the request to the API
      const response = await caseNoteService.createCaseNote(noteData);
      console.log('Note added successfully:', response);

      // Add the new note to the state
      setCaseNotes(prevNotes => [response, ...prevNotes]);

      // Clear the form and close the modal
      setNoteContent('');
      setNoteTitle('');
      setShowAddNoteModal(false);

      // Show success message
      alert('Not başarıyla eklendi.');
    } catch (error: any) {
      console.error('Error adding case note:', error);

      // More detailed error message
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        setError(`API Hatası (${error.response.status}): ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        console.error('Error request:', error.request);
        setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        console.error('Error message:', error.message);
        setError(`Not eklenirken bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`);
      }
    } finally {
      setAddingNote(false);
    }
  };

  // Delete a case note
  const handleDeleteNote = async (noteId: string | number) => {
    try {
      // Confirm deletion
      if (!confirm('Bu notu silmek istediğinize emin misiniz?')) {
        return;
      }

      // Set loading state
      setDeletingNoteId(noteId);

      console.log('Deleting case note:', noteId);

      // Send the request to the API
      await caseNoteService.deleteCaseNote(noteId);
      console.log('Note deleted successfully');

      // Remove the note from the state
      setCaseNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));

      // Show success message
      alert('Not başarıyla silindi.');
    } catch (error: any) {
      console.error('Error deleting case note:', error);

      // More detailed error message
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        setError(`API Hatası (${error.response.status}): ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        console.error('Error request:', error.request);
        setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        console.error('Error message:', error.message);
        setError(`Not silinirken bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`);
      }
    } finally {
      setDeletingNoteId(null);
    }
  };

  // Get case type color
  const getCaseTypeColor = (type?: string): string => {
    switch (type?.toLowerCase()) {
      case 'hukuk':
        return '#3B82F6'; // Blue
      case 'ceza':
        return '#EF4444'; // Red
      case 'idari':
        return '#10B981'; // Green
      case 'icra':
        return '#F59E0B'; // Amber
      case 'aile':
        return '#8B5CF6'; // Purple
      case 'iş':
        return '#EC4899'; // Pink
      case 'ticaret':
        return '#06B6D4'; // Cyan
      default:
        return '#6B7280'; // Gray
    }
  };

  // Format case number
  const formatCaseNumber = (caseItem?: CaseData | null): string => {
    if (!caseItem) return '';

    // If dosyaNo exists and contains a slash, use it directly
    if (caseItem.dosyaNo && caseItem.dosyaNo.includes('/')) {
      return caseItem.dosyaNo;
    }

    // If dosyaNo exists but has a dash instead of slash, replace it
    if (caseItem.dosyaNo && caseItem.dosyaNo.includes('-')) {
      return caseItem.dosyaNo.replace('-', '/');
    }

    // If we have esasYil and dosyaNumara, format them
    if (caseItem.esasYil && caseItem.dosyaNumara) {
      return `${caseItem.esasYil}/${caseItem.dosyaNumara}`;
    }

    // If we only have dosyaNo but it doesn't have a slash or dash, return it as is
    if (caseItem.dosyaNo) {
      return caseItem.dosyaNo;
    }

    // Last resort, return dosyaId or empty string
    return caseItem.dosyaId || '';
  };

  // Format case type
  const formatCaseType = (caseType?: string): string => {
    switch (caseType) {
      case 'CEZA_DAVASI': return 'Ceza Davası';
      case 'HUKUK_DAVASI': return 'Hukuk Davası';
      case 'ICRA_DAVASI': return 'İcra Davası';
      case 'IDARI_DAVA': return 'İdari Dava';
      case 'IS_DAVASI': return 'İş Davası';
      case 'TICARET_DAVASI': return 'Ticaret Davası';
      case 'AILE_DAVASI': return 'Aile Davası';
      case 'TUKETICI_DAVASI': return 'Tüketici Davası';
      default: return caseType || 'Belirtilmemiş';
    }
  };

  // Map dosyaTur to API caseType
  const mapDosyaTurToCaseType = (dosyaTur?: string): string => {
    if (!dosyaTur) return '';

    const dosyaTurLower = dosyaTur.toLowerCase();

    if (dosyaTurLower.includes('ceza')) return 'CEZA_DAVASI';
    if (dosyaTurLower.includes('hukuk')) return 'HUKUK_DAVASI';
    if (dosyaTurLower.includes('icra')) return 'ICRA_DAVASI';
    if (dosyaTurLower.includes('idari')) return 'IDARI_DAVA';
    if (dosyaTurLower.includes('iş')) return 'IS_DAVASI';
    if (dosyaTurLower.includes('ticaret')) return 'TICARET_DAVASI';
    if (dosyaTurLower.includes('aile')) return 'AILE_DAVASI';
    if (dosyaTurLower.includes('tüketici')) return 'TUKETICI_DAVASI';

    return 'HUKUK_DAVASI'; // Default to HUKUK_DAVASI if no match
  };

  // Format crime type
  const formatCrimeType = (crimeType?: string): string => {
    switch (crimeType) {
      case 'DOLANDIRICILIK': return 'Dolandırıcılık';
      case 'HIRSIZLIK': return 'Hırsızlık';
      case 'KASTEN_YARALAMA': return 'Kasten Yaralama';
      case 'TEHDIT': return 'Tehdit';
      case 'HAKARET': return 'Hakaret';
      case 'GUVENI_KOTUYE_KULLANMA': return 'Güveni Kötüye Kullanma';
      case 'SAHTECILIK': return 'Sahtecilik';
      case 'ZIMMET': return 'Zimmet';
      case 'RUSVET': return 'Rüşvet';
      case 'DIGER': return 'Diğer';
      default: return crimeType || 'Belirtilmemiş';
    }
  };

  // Format date - handles both epoch time (milliseconds or seconds) and ISO strings
  // Adds GMT+3 adjustment for Turkey timezone
  const formatDate = (dateInput?: string | number): string => {
    if (!dateInput) return '';

    try {
      let date: Date;

      // Check if the input is a number (epoch time)
      if (typeof dateInput === 'number' || !isNaN(Number(dateInput))) {
        const epochTime = Number(dateInput);

        // If the epoch time is in seconds (10 digits), convert to milliseconds
        // Otherwise assume it's already in milliseconds (13 digits)
        if (epochTime < 10000000000) {
          date = new Date(epochTime * 1000); // Convert seconds to milliseconds
        } else {
          date = new Date(epochTime);
        }

        console.log('Converted epoch time to date:', date);
      } else {
        // Handle as a date string
        date = new Date(dateInput);
      }

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.error('Invalid date:', dateInput);
        return String(dateInput);
      }

      // Add GMT+3 adjustment for Turkey timezone
      const turkeyTime = new Date(date.getTime());

      return turkeyTime.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error, 'Input:', dateInput);
      return String(dateInput);
    }
  };

  // Format case reason from case data
  const formatCaseReason = (caseData?: CaseData | null): string => {
    if (!caseData) return '';

    // Debug the case data to see what's available
    console.log('Case data for formatCaseReason:', JSON.stringify(caseData, null, 2));
    console.log('acilisTarihi value:', caseData.acilisTarihi);

    let reasonParts = [];

    // Extract and format opening date from different possible sources
    let openingDate = 'Belirtilmemiş';

    // Try to get acilisTarihi directly
    if (caseData.acilisTarihi) {
      openingDate = caseData.acilisTarihi;
    }
    // Try to extract from dosyaAcilisTarihi if available
    else if (caseData.dosyaAcilisTarihi && caseData.dosyaAcilisTarihi.date) {
      const date = caseData.dosyaAcilisTarihi.date;
      openingDate = `${date.day}.${date.month}.${date.year}`;

      // Set it on the caseData object for future use
      caseData.acilisTarihi = openingDate;
    }

    // Add opening date
    reasonParts.push(`Açılış Tarihi: ${openingDate}`);

    // Add court name if available
    if (caseData.birimAdi) {
      reasonParts.push(`Mahkeme: ${caseData.birimAdi}`);
    }

    // Add status if available
    if (caseData.dosyaDurum) {
      reasonParts.push(`Durum: ${caseData.dosyaDurum}`);
    }

    // Add any other relevant information
    if (caseData.aktiflik) {
      reasonParts.push(`Aktiflik: ${caseData.aktiflik}`);
    }

    return reasonParts.join('\n');
  };

  // Render loading state
  if (loading) {
    return (
      <BackgroundWrapper style={styles.container}>
        <View style={styles.headerContainer}>
          <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => {
                    // Navigate back based on where we came from
                    if (navigatedFrom === 'clients') {
                      router.push('/(tabs)/clients');
                    } else if (navigatedFrom === 'cases') {
                      router.push('/(tabs)/cases');
                    } else if (Platform.OS === 'web' && localStorage.getItem('previousPath')) {
                      // Use the stored path if available
                      const previousPath = localStorage.getItem('previousPath');
                      if (previousPath) {
                        if (previousPath.includes('/clients')) {
                          router.push('/(tabs)/clients');
                        } else if (previousPath.includes('/cases')) {
                          router.push('/(tabs)/cases');
                        } else {
                          router.push(previousPath as any);
                        }
                      } else {
                        router.push('/cases');
                      }
                    } else {
                      // Default fallback
                      router.push('/cases');
                    }
                  }}
                >
                  <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <View style={styles.titleContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                  <Text style={styles.titleText}>AVAS</Text>
                  <Text style={styles.subtitleText}> | Dava Detayı</Text>
                </View>
              </View>

              <View style={styles.rightSection}>
                <TouchableOpacity style={styles.iconButton} onPress={toggleNotifications}>
                  <View>
                    <Ionicons
                      name={isLoadingNotifications ? "notifications-circle-outline" : "notifications-outline"}
                      size={22}
                      color="#FFFFFF"
                    />
                    {notificationCount > 0 && (
                      <View style={styles.notificationBadge}>
                        {notificationCount > 9 ? (
                          <Text style={styles.notificationBadgeText}>9+</Text>
                        ) : notificationCount > 0 ? (
                          <Text style={styles.notificationBadgeText}>{notificationCount}</Text>
                        ) : null}
                      </View>
                    )}
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={() => router.push('/(tabs)/profile')}
                >
                  <Ionicons name="person-outline" size={22} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.loadingCard}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>Dava bilgileri yükleniyor...</ThemedText>
        </BlurView>
      </BackgroundWrapper>
    );
  }

  // Render error state
  if (error) {
    return (
      <BackgroundWrapper style={styles.container}>
        <View style={styles.headerContainer}>
          <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => {
                    // Navigate back based on where we came from
                    if (navigatedFrom === 'clients') {
                      router.push('/(tabs)/clients');
                    } else if (navigatedFrom === 'cases') {
                      router.push('/(tabs)/cases');
                    } else if (Platform.OS === 'web' && localStorage.getItem('previousPath')) {
                      // Use the stored path if available
                      const previousPath = localStorage.getItem('previousPath');
                      if (previousPath) {
                        if (previousPath.includes('/clients')) {
                          router.push('/(tabs)/clients');
                        } else if (previousPath.includes('/cases')) {
                          router.push('/(tabs)/cases');
                        } else {
                          router.push(previousPath as any);
                        }
                      } else {
                        router.push('/cases');
                      }
                    } else {
                      // Default fallback
                      router.push('/cases');
                    }
                  }}
                >
                  <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <View style={styles.titleContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                  <Text style={styles.titleText}>AVAS</Text>
                  <Text style={styles.subtitleText}> | Dava Detayı</Text>
                </View>
              </View>

              <View style={styles.rightSection}>
                <TouchableOpacity style={styles.iconButton} onPress={toggleNotifications}>
                  <View>
                    <Ionicons
                      name={isLoadingNotifications ? "notifications-circle-outline" : "notifications-outline"}
                      size={22}
                      color="#FFFFFF"
                    />
                    {notificationCount > 0 && (
                      <View style={styles.notificationBadge}>
                        {notificationCount > 9 ? (
                          <Text style={styles.notificationBadgeText}>9+</Text>
                        ) : notificationCount > 0 ? (
                          <Text style={styles.notificationBadgeText}>{notificationCount}</Text>
                        ) : null}
                      </View>
                    )}
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={() => router.push('/(tabs)/profile')}
                >
                  <Ionicons name="person-outline" size={22} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.errorCard}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <ThemedText style={styles.errorTextFull}>{error}</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setLoading(true);

              // Use dosyaNo from URL if available, otherwise use ID
              const retryFetchData = async () => {
                try {
                  let caseDetails;
                  let formattedCaseNumber = dosyaNo as string;

                  // First check if we have case data in localStorage from the previous page
                  let storedCaseData = null;
                  if (Platform.OS === 'web') {
                    const storedData = localStorage.getItem('selectedCaseData');
                    if (storedData) {
                      try {
                        storedCaseData = JSON.parse(storedData);
                        console.log('Found stored case data on retry:', storedCaseData);
                        setCaseData(storedCaseData);
                        // Use the formatted dosya number from stored case data if needed
                        if (!formattedCaseNumber) {
                          formattedCaseNumber = formatCaseNumber(storedCaseData);
                        }
                        // Clear the stored data to avoid using stale data on refresh
                        localStorage.removeItem('selectedCaseData');
                      } catch (e) {
                        console.error('Error parsing stored case data on retry:', e);
                      }
                    }
                  }

                  // If no stored data and no dosyaNo, fetch by ID
                  if (!storedCaseData && !formattedCaseNumber) {
                    // If no dosyaNo, fetch by ID
                    console.log('Retrying with ID:', id);
                    caseDetails = await caseService.getCaseById(id as string);
                    setCaseData(caseDetails);
                    formattedCaseNumber = formatCaseNumber(caseDetails);
                  }

                  setError('');

                  // Fetch case details using the case number
                  if (formattedCaseNumber) {
                    try {
                      const detailData = await caseService.getCaseDetailsByCaseNumber(formattedCaseNumber);
                      setCaseDetailData(detailData);
                      console.log('Case detail data on retry:', detailData);
                    } catch (detailErr: any) {
                      console.error('Error fetching case details on retry:', detailErr);
                    }
                  }
                } catch (err: any) {
                  console.error('Error retrying to fetch case:', err);
                  setError(`Dava bilgileri yüklenirken bir hata oluştu: ${err.message || 'Bilinmeyen hata'}`);
                } finally {
                  setLoading(false);
                }
              };

              retryFetchData();
            }}
          >
            <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
          </TouchableOpacity>
        </BlurView>
      </BackgroundWrapper>
    );
  }

  return (
    <BackgroundWrapper style={styles.container}>
      <View style={styles.headerContainer}>
        <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
          <View style={styles.header}>
            <View style={styles.leftSection}>
              <TouchableOpacity
                style={styles.menuButton}
                onPress={() => {
                  // Navigate back based on where we came from
                  if (navigatedFrom === 'clients') {
                    router.push('/(tabs)/clients');
                  } else if (navigatedFrom === 'cases') {
                    router.push('/(tabs)/cases');
                  } else if (Platform.OS === 'web' && localStorage.getItem('previousPath')) {
                    // Use the stored path if available
                    const previousPath = localStorage.getItem('previousPath');
                    if (previousPath) {
                      if (previousPath.includes('/clients')) {
                        router.push('/(tabs)/clients');
                      } else if (previousPath.includes('/cases')) {
                        router.push('/(tabs)/cases');
                      } else {
                        router.push(previousPath as any);
                      }
                    } else {
                      router.push('/cases');
                    }
                  } else {
                    // Default fallback
                    router.push('/cases');
                  }
                }}
              >
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <View style={styles.titleContainer}>
                <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                <Text style={styles.titleText}>AVAS</Text>
                <Text style={styles.subtitleText}> | Dava Detayı</Text>
              </View>
            </View>

            <View style={styles.rightSection}>
              <TouchableOpacity style={styles.iconButton} onPress={toggleNotifications}>
                <View>
                  <Ionicons
                    name={isLoadingNotifications ? "notifications-circle-outline" : "notifications-outline"}
                    size={22}
                    color="#FFFFFF"
                  />
                  {notificationCount > 0 && (
                    <View style={styles.notificationBadge}>
                      {notificationCount > 9 ? (
                        <Text style={styles.notificationBadgeText}>9+</Text>
                      ) : notificationCount > 0 ? (
                        <Text style={styles.notificationBadgeText}>{notificationCount}</Text>
                      ) : null}
                    </View>
                  )}
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => router.push('/(tabs)/profile')}
              >
                <Ionicons name="person-outline" size={22} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Dosya Detayı Header */}
        <View style={[
          styles.modernHeader,
          isDark && {
            backgroundColor: 'rgba(17, 24, 39, 0.8)',
            borderBottomColor: 'rgba(255, 255, 255, 0.1)'
          }
        ]}>
          <View style={styles.headerContent}>
            <View style={[
              styles.headerIconContainer,
              isDark && { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
            ]}>
              <Ionicons
                name="folder-open"
                size={24}
                color={isDark ? Colors.dark.tint : Colors.light.tint}
              />
            </View>
            <ThemedText style={styles.headerTitle}></ThemedText>
            <View style={[
              styles.headerStatsContainer,
              isDark && { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
            ]}>
              <ThemedText style={styles.headerStats}>
                Dosya Detayı
              </ThemedText>
            </View>
          </View>
        </View>

          {/* Tabs */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'bilgiler' && styles.activeTabButton]}
              onPress={() => setActiveTab('bilgiler')}
            >
              <ThemedText style={activeTab === 'bilgiler' ? styles.activeTabText : null}>Bilgiler</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'taraflar' && styles.activeTabButton]}
              onPress={() => setActiveTab('taraflar')}
            >
              <ThemedText style={activeTab === 'taraflar' ? styles.activeTabText : null}>Taraflar</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'safahat' && styles.activeTabButton]}
              onPress={() => setActiveTab('safahat')}
            >
              <ThemedText style={activeTab === 'safahat' ? styles.activeTabText : null}>Safahat</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'notlar' && styles.activeTabButton]}
              onPress={() => setActiveTab('notlar')}
            >
              <ThemedText style={activeTab === 'notlar' ? styles.activeTabText : null}>Notlar</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'tahsilat' && styles.activeTabButton]}
              onPress={() => setActiveTab('tahsilat')}
            >
              <ThemedText style={activeTab === 'tahsilat' ? styles.activeTabText : null}>Tahsilat</ThemedText>
            </TouchableOpacity>
          </View>

          {/* Bilgiler Tab */}
          {activeTab === 'bilgiler' && (
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>


              {/* Basic Case Information */}
              <View style={styles.basicInfoCard}>
                <View style={styles.basicInfoHeader}>
                  <ThemedText style={styles.basicInfoTitle}>
                    Dosya {formatCaseNumber(caseData)}
                  </ThemedText>
                  {caseData?.dosyaTur && (
                    <View style={[
                      styles.basicInfoTypeBadge,
                      { backgroundColor: `${getCaseTypeColor(caseData.dosyaTur)}20` }
                    ]}>
                      <ThemedText style={[
                        styles.basicInfoTypeText,
                        { color: getCaseTypeColor(caseData.dosyaTur) }
                      ]}>
                        {caseData.dosyaTur}
                      </ThemedText>
                    </View>
                  )}
                </View>

                <View style={styles.basicInfoGrid}>
                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Dosya No:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{formatCaseNumber(caseData)}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Açılış:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{caseData?.acilisTarihi || 'Belirtilmemiş'}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Durum:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{caseData?.dosyaDurum || 'Belirtilmemiş'}</ThemedText>
                  </View>

                  <View style={styles.basicInfoItem}>
                    <ThemedText style={styles.basicInfoLabel}>Aktiflik:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{caseData?.aktiflik || 'Aktif'}</ThemedText>
                  </View>

                  <View style={[styles.basicInfoItem, styles.basicInfoItemFull]}>
                    <ThemedText style={styles.basicInfoLabel}>Mahkeme:</ThemedText>
                    <ThemedText style={styles.basicInfoValue}>{caseData?.birimAdi || 'Belirtilmemiş'}</ThemedText>
                  </View>
                </View>
              </View>

              <View style={styles.sectionHeader}>
                <ThemedText type="subtitle">Detaylı Bilgiler</ThemedText>
                {!caseDetailData ? (
                  <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => setShowAddDetailModal(true)}
                  >
                    <Ionicons name="add-circle" size={20} color="#FFFFFF" />
                    <ThemedText style={styles.addButtonText}>Detay Ekle</ThemedText>
                  </TouchableOpacity>
                ) : (
                  <View style={{ flexDirection: 'row' }}>
                    <TouchableOpacity
                      style={[styles.editButton, { marginRight: 8 }]}
                      onPress={() => setShowAddDetailModal(true)}
                    >
                      <Ionicons name="create-outline" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                      <ThemedText style={styles.editButtonText}>Düzenle</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={handleDeleteCaseDetails}
                    >
                      <Ionicons name="trash-outline" size={20} color="#EF4444" />
                      <ThemedText style={styles.deleteButtonText}>Sil</ThemedText>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {/* Add/Edit Case Detail Modal */}
              <AddCaseDetailModal
                visible={showAddDetailModal}
                onClose={() => setShowAddDetailModal(false)}
                onSave={handleAddCaseDetails}
                loading={loading}
                existingData={caseDetailData ? {
                  ...caseDetailData,
                  id: caseDetailData.id?.toString()
                } : caseData ? {
                  // Pre-populate with case data when adding new details
                  caseType: mapDosyaTurToCaseType(caseData.dosyaTur),
                  caseTitle: `Dosya ${formatCaseNumber(caseData)} - ${caseData.birimAdi || caseData.dosyaTur || 'Dava'}`,
                  derdest: caseData.dosyaDurum?.toLowerCase() === 'açık',
                  // Include more details from the case data
                  caseReason: formatCaseReason(caseData),
                  // If there's a numeric value in the case data, use it
                  caseValue: caseData.davaDeğeri || '',
                  // Include the opening date
                  acilisTarihi: caseData.acilisTarihi || (caseData.dosyaAcilisTarihi && caseData.dosyaAcilisTarihi.date ?
                    `${caseData.dosyaAcilisTarihi.date.day}.${caseData.dosyaAcilisTarihi.date.month}.${caseData.dosyaAcilisTarihi.date.year}` :
                    undefined)
                } : undefined}
              />

              {/* Detailed Case Information */}
              {caseDetailData ? (
                <>
                  {caseDetailData.caseTitle && (
                    <View style={styles.infoRow}>
                      <Ionicons name="text-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                      <View style={styles.infoContent}>
                        <ThemedText style={styles.infoLabel}>Dava Başlığı</ThemedText>
                        <ThemedText>{caseDetailData.caseTitle}</ThemedText>
                      </View>
                    </View>
                  )}

                  {caseDetailData.caseType && (
                    <View style={styles.infoRow}>
                      <Ionicons name="briefcase-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                      <View style={styles.infoContent}>
                        <ThemedText style={styles.infoLabel}>Dava Türü</ThemedText>
                        <ThemedText>{formatCaseType(caseDetailData.caseType)}</ThemedText>
                      </View>
                    </View>
                  )}

                  {caseDetailData.caseType === 'CEZA_DAVASI' && caseDetailData.crimeType && (
                    <View style={styles.infoRow}>
                      <Ionicons name="alert-circle-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                      <View style={styles.infoContent}>
                        <ThemedText style={styles.infoLabel}>Suç Türü</ThemedText>
                        <ThemedText>{formatCrimeType(caseDetailData.crimeType)}</ThemedText>
                      </View>
                    </View>
                  )}

                  {caseDetailData.caseValue && (
                    <View style={styles.infoRow}>
                      <Ionicons name="cash-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                      <View style={styles.infoContent}>
                        <ThemedText style={styles.infoLabel}>Dava Değeri</ThemedText>
                        <ThemedText>{caseDetailData.caseValue} TL</ThemedText>
                      </View>
                    </View>
                  )}

                  <View style={styles.infoRow}>
                    <Ionicons name="checkmark-circle-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Derdest Durumu</ThemedText>
                      <ThemedText>{caseDetailData.derdest ? 'Derdest' : 'Sonuçlanmış'}</ThemedText>
                    </View>
                  </View>

                  {caseDetailData.caseReason && (
                    <View style={styles.infoRow}>
                      <Ionicons name="information-circle-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                      <View style={styles.infoContent}>
                        <ThemedText style={styles.infoLabel}>Dava Sebebi</ThemedText>
                        <ThemedText>{caseDetailData.caseReason}</ThemedText>
                      </View>
                    </View>
                  )}

                  <View style={styles.infoRow}>
                    <Ionicons name="person-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Oluşturan</ThemedText>
                      <ThemedText>{caseDetailData.ownerName || 'Belirtilmemiş'}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="time-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Oluşturulma Tarihi</ThemedText>
                      <ThemedText>{formatDate(caseDetailData.createdAt) || 'Belirtilmemiş'}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="refresh-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Son Güncelleme</ThemedText>
                      <ThemedText>{formatDate(caseDetailData.updatedAt) || 'Belirtilmemiş'}</ThemedText>
                    </View>
                  </View>
                </>
              ) : (
                <View style={styles.emptyDetailContainer}>
                  <Ionicons name="information-circle-outline" size={40} color={isDark ? '#9ca3af' : '#64748b'} />
                  <ThemedText style={styles.emptyDetailText}>
                    Detaylı bilgiler henüz eklenmemiş. Dava ile ilgili detaylı bilgileri eklemek için "Detay Ekle" butonuna tıklayabilirsiniz.
                  </ThemedText>
                </View>
              )}
            </BlurView>
          )}

          {/* Taraflar Tab */}
          {activeTab === 'taraflar' && (
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
              <View style={styles.sectionHeader}>
                <ThemedText type="subtitle">Dava Tarafları</ThemedText>
              </View>

              {/* Convert caseParties to the format used in case cards */}
              {(() => {
                // Check if we have parties data
                if (!caseParties || caseParties.length === 0) {
                  return (
                    <View style={styles.emptyDetailContainer}>
                      <ThemedText style={styles.emptyDetailText}>Taraf bilgisi bulunamadı</ThemedText>
                    </View>
                  );
                }

                // Convert existing parties to the card format if needed
                const formattedParties = caseParties?.map((party: CaseParty) => {
                  if (party.isim || party.soyad) {
                    // Convert from details format to card format
                    return {
                      adi: `${party.isim || ''} ${party.soyad || ''}`.trim(),
                      rol: party.sifat || 'Belirtilmemiş',
                      kisiKurum: 'Kişi',
                      vekil: party.vekil || ''
                    };
                  }
                  return party; // Already in the right format
                });

                return (
                  <View style={styles.partiesList}>
                    {/* Davacı/Müşteki/Katılan taraflar */}
                    {formattedParties?.filter((party: any) =>
                      party && party.rol && (
                        party.rol.toLowerCase().includes('davacı') ||
                        party.rol.toLowerCase().includes('müşteki') ||
                        party.rol.toLowerCase().includes('katılan') ||
                        party.rol.toLowerCase().includes('mağdur')
                      )
                    ).map((party: any, index: number) => {
                      const tarafAdi = party.adi || 'Belirtilmemiş';
                      const tarafRol = party.rol || 'Belirtilmemiş';
                      const tarafKisiKurum = party.kisiKurum || '';
                      const tarafVekil = party.vekil ?
                        (typeof party.vekil === 'string' ?
                          party.vekil.replace(/[\[\]]/g, '') :
                          Array.isArray(party.vekil) ?
                            party.vekil.join(', ') :
                            '') :
                        '';

                      return (
                        <View key={`davaci-${index}`} style={styles.partyItemContainer}>
                          <ThemedText style={[styles.partyName, { color: '#10B981' }]}>
                            • {tarafAdi}
                          </ThemedText>

                          <View style={styles.partyDetailsContainer}>
                            <ThemedText style={styles.partyRole}>{tarafRol}</ThemedText>

                            {tarafKisiKurum ? (
                              <ThemedText style={styles.partyType}>
                                {tarafKisiKurum === 'Kişi' ? '👤' : '🏢'} {tarafKisiKurum}
                              </ThemedText>
                            ) : null}
                          </View>

                          {tarafVekil ? (
                            <ThemedText style={styles.partyLawyer}>
                              ⚖️ Vekil: {tarafVekil}
                            </ThemedText>
                          ) : null}
                        </View>
                      );
                    })}

                    {/* Davalı/Şüpheli/Sanık taraflar */}
                    {formattedParties?.filter((party: any) =>
                      party && party.rol && (
                        party.rol.toLowerCase().includes('davalı') ||
                        party.rol.toLowerCase().includes('şüpheli') ||
                        party.rol.toLowerCase().includes('sanık') ||
                        party.rol.toLowerCase().includes('aleyhine')
                      )
                    ).map((party: any, index: number) => {
                      const tarafAdi = party.adi || 'Belirtilmemiş';
                      const tarafRol = party.rol || 'Belirtilmemiş';
                      const tarafKisiKurum = party.kisiKurum || '';
                      const tarafVekil = party.vekil ?
                        (typeof party.vekil === 'string' ?
                          party.vekil.replace(/[\[\]]/g, '') :
                          Array.isArray(party.vekil) ?
                            party.vekil.join(', ') :
                            '') :
                        '';

                      return (
                        <View key={`davali-${index}`} style={styles.partyItemContainer}>
                          <ThemedText style={[styles.partyName, { color: '#EF4444' }]}>
                            • {tarafAdi}
                          </ThemedText>

                          <View style={styles.partyDetailsContainer}>
                            <ThemedText style={styles.partyRole}>{tarafRol}</ThemedText>

                            {tarafKisiKurum ? (
                              <ThemedText style={styles.partyType}>
                                {tarafKisiKurum === 'Kişi' ? '👤' : '🏢'} {tarafKisiKurum}
                              </ThemedText>
                            ) : null}
                          </View>

                          {tarafVekil ? (
                            <ThemedText style={styles.partyLawyer}>
                              ⚖️ Vekil: {tarafVekil}
                            </ThemedText>
                          ) : null}
                        </View>
                      );
                    })}

                    {/* Diğer taraflar */}
                    {formattedParties?.filter((party: any) =>
                      party && party.rol && !(
                        party.rol.toLowerCase().includes('davacı') ||
                        party.rol.toLowerCase().includes('müşteki') ||
                        party.rol.toLowerCase().includes('katılan') ||
                        party.rol.toLowerCase().includes('mağdur') ||
                        party.rol.toLowerCase().includes('davalı') ||
                        party.rol.toLowerCase().includes('şüpheli') ||
                        party.rol.toLowerCase().includes('sanık') ||
                        party.rol.toLowerCase().includes('aleyhine')
                      )
                    ).map((party: any, index: number) => {
                      const tarafAdi = party.adi || 'Belirtilmemiş';
                      const tarafRol = party.rol || 'Belirtilmemiş';
                      const tarafKisiKurum = party.kisiKurum || '';
                      const tarafVekil = party.vekil ?
                        (typeof party.vekil === 'string' ?
                          party.vekil.replace(/[\[\]]/g, '') :
                          Array.isArray(party.vekil) ?
                            party.vekil.join(', ') :
                            '') :
                        '';

                      return (
                        <View key={`diger-${index}`} style={styles.partyItemContainer}>
                          <ThemedText style={styles.partyName}>
                            • {tarafAdi}
                          </ThemedText>

                          <View style={styles.partyDetailsContainer}>
                            <ThemedText style={styles.partyRole}>{tarafRol}</ThemedText>

                            {tarafKisiKurum ? (
                              <ThemedText style={styles.partyType}>
                                {tarafKisiKurum === 'Kişi' ? '👤' : '🏢'} {tarafKisiKurum}
                              </ThemedText>
                            ) : null}
                          </View>

                          {tarafVekil ? (
                            <ThemedText style={styles.partyLawyer}>
                              ⚖️ Vekil: {tarafVekil}
                            </ThemedText>
                          ) : null}
                        </View>
                      );
                    })}
                  </View>
                );
              })()}
            </BlurView>
          )}

          {/* Safahat Tab */}
          {activeTab === 'safahat' && (
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
              <View style={styles.sectionHeader}>
                <ThemedText type="subtitle">Dava Safahatı</ThemedText>
              </View>

              {/* Safahat Info */}
              {safahatInfo && safahatInfo.error ? (
                // Display error message when API returns an error
                <View style={styles.errorContainer}>
                  <Ionicons name="alert-circle-outline" size={24} color="#FF6B6B" />
                  <ThemedText style={styles.errorText}>
                    {safahatInfo.error}
                  </ThemedText>
                </View>
              ) : safahatInfo && safahatInfo.safahatlar && safahatInfo.safahatlar.length > 0 ? (
                // Display safahat entries when available
                <View>
                  {/* Show cached data notification if using expired cache */}
                  {safahatInfo.isExpiredCache && (
                    <View style={styles.cachedDataNotice}>
                      <Ionicons name="time-outline" size={16} color="#3182CE" />
                      <ThemedText style={styles.cachedDataText}>
                        Önbelleğe alınmış veri gösteriliyor. Yeni veri 120 dakikada bir alınabilir.
                      </ThemedText>
                    </View>
                  )}

                  <FlatList
                    data={safahatInfo.safahatlar}
                    keyExtractor={(item) => item.id || `safahat-${Math.random()}`}
                    renderItem={({ item }) => (
                      <View style={styles.safahatItem}>
                        <View style={styles.safahatHeader}>
                          <View style={styles.safahatDateContainer}>
                            <Ionicons name="calendar-outline" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
                            <ThemedText style={styles.safahatDate}>
                              {item.safahatTarihiSTR || 'Tarih Yok'}
                            </ThemedText>
                          </View>
                          <ThemedText style={styles.safahatBirim}>
                            {item.islemYapanBirim || ''}
                          </ThemedText>
                        </View>
                        <View style={styles.safahatContent}>
                          <ThemedText type="defaultSemiBold" style={styles.safahatType}>
                            {item.safahatTuruAciklama || 'İşlem Türü Belirtilmemiş'}
                          </ThemedText>
                          <ThemedText style={styles.safahatDescription}>
                            {item.aciklama || 'Açıklama bulunmuyor'}
                          </ThemedText>
                        </View>
                      </View>
                    )}
                    scrollEnabled={true}
                    style={styles.safahatList}
                  />
                </View>
              ) : (
                <ThemedText style={styles.emptyText}>Safahat bilgisi bulunamadı</ThemedText>
              )}

              {/* Legacy Case History - Hidden for now */}
              {false && caseHistory && caseHistory.length > 0 && (
                <FlatList
                  data={caseHistory}
                  keyExtractor={(_item, index) => `history-${index}`}
                  renderItem={({ item }) => (
                    <View style={styles.historyItem}>
                      <View style={styles.historyDateContainer}>
                        <ThemedText style={styles.historyDate}>
                          {item.tarih || 'Tarih Yok'}
                        </ThemedText>
                      </View>
                      <View style={styles.historyContent}>
                        <ThemedText type="defaultSemiBold">{item.islemTuru || 'İşlem Türü Belirtilmemiş'}</ThemedText>
                        <ThemedText style={styles.historyDescription}>
                          {item.aciklama || 'Açıklama bulunmuyor'}
                        </ThemedText>
                      </View>
                    </View>
                  )}
                  scrollEnabled={false}
                />
              )}
            </BlurView>
          )}

          {/* Notlar Tab */}
          {activeTab === 'notlar' && (
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
              <View style={styles.sectionHeader}>
                <ThemedText type="subtitle">Dava Notları</ThemedText>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => setShowAddNoteModal(true)}
                >
                  <Ionicons name="add-circle" size={20} color="#FFFFFF" />
                  <ThemedText style={styles.addButtonText}>Not Ekle</ThemedText>
                </TouchableOpacity>
              </View>

              {caseNotes && caseNotes.length > 0 ? (
                <FlatList
                  data={caseNotes}
                  keyExtractor={(_item, index) => `note-${index}`}
                  renderItem={({ item }) => (
                    <View style={styles.noteItem}>
                      <View style={styles.noteHeader}>
                        <ThemedText style={styles.noteTitle}>
                          {(() => {
                            // Extract title from content if it contains a newline
                            if (item.content && item.content.includes('\n\n')) {
                              const parts = item.content.split('\n\n');
                              return parts[0] || 'Not Başlığı';
                            }
                            return 'Not Başlığı';
                          })()}
                        </ThemedText>
                        <View style={styles.noteActions}>
                          <TouchableOpacity
                            style={styles.noteDeleteButton}
                            onPress={() => {
                              if (item && item.id) {
                                handleDeleteNote(item.id);
                              } else {
                                console.error('Cannot delete note: item or item.id is undefined');
                                alert('Not silinirken bir hata oluştu: Not ID bulunamadı');
                              }
                            }}
                            disabled={deletingNoteId === item.id}
                          >
                            {deletingNoteId === item.id ? (
                              <ActivityIndicator size="small" color="#EF4444" />
                            ) : (
                              <Ionicons name="trash-outline" size={18} color="#EF4444" />
                            )}
                          </TouchableOpacity>
                          <ThemedText style={styles.noteDate}>{item.createdAt || 'Tarih Yok'}</ThemedText>
                        </View>
                      </View>
                      <ThemedText style={styles.noteContent}>
                        {(() => {
                          // Extract content without title
                          if (item.content && item.content.includes('\n\n')) {
                            const parts = item.content.split('\n\n');
                            return parts.slice(1).join('\n\n') || 'Not içeriği bulunmuyor';
                          }
                          return item.content || 'Not içeriği bulunmuyor';
                        })()}
                      </ThemedText>
                    </View>
                  )}
                  scrollEnabled={false}
                />
              ) : (
                <ThemedText style={styles.emptyText}>Not bulunamadı</ThemedText>
              )}
            </BlurView>
          )}

          {/* Tahsilat Tab */}
          {activeTab === 'tahsilat' && (
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
              <View style={styles.sectionHeader}>
                <ThemedText type="subtitle">Tahsilat ve Reddiyat</ThemedText>
              </View>

              {tahsilatReddiyatData ? (
                <View>
                  {/* Özet Bilgiler */}
                  <View style={styles.tahsilatSummaryContainer}>
                    <View style={styles.tahsilatSummaryItem}>
                      <Ionicons name="cash-outline" size={24} color={isDark ? '#9ca3af' : '#64748b'} />
                      <ThemedText style={styles.tahsilatSummaryLabel}>Toplam Tahsilat</ThemedText>
                      <ThemedText style={styles.tahsilatSummaryValue}>{tahsilatReddiyatData.toplamTahsilat || '0'} TL</ThemedText>
                    </View>

                    <View style={styles.tahsilatSummaryItem}>
                      <Ionicons name="return-down-back-outline" size={24} color={isDark ? '#9ca3af' : '#64748b'} />
                      <ThemedText style={styles.tahsilatSummaryLabel}>Toplam Reddiyat</ThemedText>
                      <ThemedText style={styles.tahsilatSummaryValue}>{tahsilatReddiyatData.toplamreddiyat || tahsilatReddiyatData.toplamReddiyat || '0'} TL</ThemedText>
                    </View>

                    <View style={styles.tahsilatSummaryItem}>
                      <Ionicons name="wallet-outline" size={24} color={isDark ? '#9ca3af' : '#64748b'} />
                      <ThemedText style={styles.tahsilatSummaryLabel}>Kalan</ThemedText>
                      <ThemedText style={styles.tahsilatSummaryValue}>{tahsilatReddiyatData.toplamKalan || '0'} TL</ThemedText>
                    </View>

                    <View style={styles.tahsilatSummaryItem}>
                      <Ionicons name="receipt-outline" size={24} color={isDark ? '#9ca3af' : '#64748b'} />
                      <ThemedText style={styles.tahsilatSummaryLabel}>Toplam Harç</ThemedText>
                      <ThemedText style={styles.tahsilatSummaryValue}>{tahsilatReddiyatData.toplamTahsilHarci || '0'} TL</ThemedText>
                    </View>
                  </View>

                  {/* Tahsilat Listesi */}
                  {tahsilatReddiyatData.tahsilatList && tahsilatReddiyatData.tahsilatList.length > 0 && (
                    <View style={styles.tahsilatSection}>
                      <View style={styles.tahsilatSectionHeader}>
                        <Ionicons name="arrow-down-circle-outline" size={20} color={Colors.light.tint} />
                        <ThemedText style={styles.tahsilatSectionTitle}>Tahsilatlar</ThemedText>
                      </View>

                      {tahsilatReddiyatData.tahsilatList.map((item: any, index: number) => (
                        <View key={`tahsilat-${item.kayitId}-${index}`} style={styles.tahsilatItem}>
                          <View style={styles.tahsilatItemHeader}>
                            <View style={styles.tahsilatItemLeft}>
                              <ThemedText style={styles.tahsilatItemTitle}>{item.tahsilatTuru || 'Tahsilat'}</ThemedText>
                              <ThemedText style={styles.tahsilatItemDate}>
                                {item.tahsilatTarihi ? item.tahsilatTarihi.split(' ')[0] : 'Tarih Belirtilmemiş'}
                              </ThemedText>
                            </View>
                            <View style={styles.tahsilatItemRight}>
                              <ThemedText style={styles.tahsilatItemAmount}>{item.tahsilatTutari || '0'} TL</ThemedText>
                              <ThemedText style={styles.tahsilatItemMakbuz}>Makbuz: {item.makbuzNo || '-'}</ThemedText>
                            </View>
                          </View>
                          <View style={styles.tahsilatItemDetails}>
                            <ThemedText style={styles.tahsilatItemDetailText}>
                              <ThemedText style={styles.tahsilatItemDetailLabel}>Ödeyen: </ThemedText>
                              {item.odeyenKisi || 'Belirtilmemiş'}
                            </ThemedText>
                            <ThemedText style={styles.tahsilatItemDetailText}>
                              <ThemedText style={styles.tahsilatItemDetailLabel}>Birim: </ThemedText>
                              {item.birimAdi || 'Belirtilmemiş'}
                            </ThemedText>
                            {item.kalanMiktar > 0 && (
                              <ThemedText style={styles.tahsilatItemDetailText}>
                                <ThemedText style={styles.tahsilatItemDetailLabel}>Kalan: </ThemedText>
                                {item.kalanMiktar} TL
                              </ThemedText>
                            )}
                          </View>
                        </View>
                      ))}
                    </View>
                  )}

                  {/* Reddiyat Listesi */}
                  {tahsilatReddiyatData.reddiyatList && tahsilatReddiyatData.reddiyatList.length > 0 && (
                    <View style={styles.tahsilatSection}>
                      <View style={styles.tahsilatSectionHeader}>
                        <Ionicons name="arrow-up-circle-outline" size={20} color="#EF4444" />
                        <ThemedText style={[styles.tahsilatSectionTitle, {color: '#EF4444'}]}>Reddiyatlar</ThemedText>
                      </View>

                      {tahsilatReddiyatData.reddiyatList.map((item: any, index: number) => (
                        <View key={`reddiyat-${item.kayitId}-${index}`} style={[styles.tahsilatItem, styles.reddiyatItem]}>
                          <View style={styles.tahsilatItemHeader}>
                            <View style={styles.tahsilatItemLeft}>
                              <ThemedText style={styles.tahsilatItemTitle}>{item.reddiyatNedeni || 'Reddiyat'}</ThemedText>
                              <ThemedText style={styles.tahsilatItemDate}>
                                {item.reddiyatTarihi ? item.reddiyatTarihi.split(' ')[0] : 'Tarih Belirtilmemiş'}
                              </ThemedText>
                            </View>
                            <View style={styles.tahsilatItemRight}>
                              <ThemedText style={[styles.tahsilatItemAmount, {color: '#EF4444'}]}>{item.miktar || '0'} TL</ThemedText>
                              <ThemedText style={styles.tahsilatItemMakbuz}>Makbuz: {item.makbuzNo || '-'}</ThemedText>
                            </View>
                          </View>
                          <View style={styles.tahsilatItemDetails}>
                            <ThemedText style={styles.tahsilatItemDetailText}>
                              <ThemedText style={styles.tahsilatItemDetailLabel}>Ödeyen: </ThemedText>
                              {item.odeyenKisi || 'Belirtilmemiş'}
                            </ThemedText>
                            <ThemedText style={styles.tahsilatItemDetailText}>
                              <ThemedText style={styles.tahsilatItemDetailLabel}>Birim: </ThemedText>
                              {item.birimAdi || 'Belirtilmemiş'}
                            </ThemedText>
                            {item.odenecekMiktar > 0 && (
                              <ThemedText style={styles.tahsilatItemDetailText}>
                                <ThemedText style={styles.tahsilatItemDetailLabel}>Ödenecek: </ThemedText>
                                {item.odenecekMiktar} TL
                              </ThemedText>
                            )}
                          </View>
                        </View>
                      ))}
                    </View>
                  )}

                  {/* Harç Listesi */}
                  {tahsilatReddiyatData.harcList && tahsilatReddiyatData.harcList.length > 0 && (
                    <View style={styles.tahsilatSection}>
                      <View style={styles.tahsilatSectionHeader}>
                        <Ionicons name="document-text-outline" size={20} color="#6366F1" />
                        <ThemedText style={[styles.tahsilatSectionTitle, {color: '#6366F1'}]}>Harçlar</ThemedText>
                      </View>

                      {tahsilatReddiyatData.harcList.map((item: any, index: number) => (
                        <View key={`harc-${index}`} style={[styles.tahsilatItem, styles.harcItem]}>
                          <View style={styles.tahsilatItemHeader}>
                            <View style={styles.tahsilatItemLeft}>
                              <ThemedText style={styles.tahsilatItemTitle}>{item.tahsilatTuru || 'Harç'}</ThemedText>
                              <ThemedText style={styles.tahsilatItemDate}>
                                {item.tahsilatTarihi ? item.tahsilatTarihi.split(' ')[0] : 'Tarih Belirtilmemiş'}
                              </ThemedText>
                            </View>
                            <View style={styles.tahsilatItemRight}>
                              <ThemedText style={[styles.tahsilatItemAmount, {color: '#6366F1'}]}>{item.miktar || '0'} TL</ThemedText>
                              <ThemedText style={styles.tahsilatItemMakbuz}>Makbuz: {item.makbuzNo || '-'}</ThemedText>
                            </View>
                          </View>
                          <View style={styles.tahsilatItemDetails}>
                            <ThemedText style={styles.tahsilatItemDetailText}>
                              <ThemedText style={styles.tahsilatItemDetailLabel}>Ödeyen: </ThemedText>
                              {item.odeyenKisi || 'Belirtilmemiş'}
                            </ThemedText>
                            <ThemedText style={styles.tahsilatItemDetailText}>
                              <ThemedText style={styles.tahsilatItemDetailLabel}>Birim: </ThemedText>
                              {item.birimAdi || 'Belirtilmemiş'}
                            </ThemedText>
                          </View>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              ) : (
                <ThemedText style={styles.emptyText}>Tahsilat ve reddiyat bilgisi bulunamadı</ThemedText>
              )}
            </BlurView>
          )}
        </ScrollView>

      <NotificationCenter
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
        initialNotifications={notifications}
      />

      {/* Add Note Modal */}
      {showAddNoteModal && (
        <View style={[styles.modalOverlay, { backgroundColor: 'transparent' }]}>
          <View
            style={[
              styles.modalContainer,
              { backgroundColor: '#FFFFFF' }
            ]}
          >
            <View style={[
              styles.modalHeader,
              { backgroundColor: '#FFFFFF', borderBottomColor: '#EEEEEE' }
            ]}>
              <ThemedText type="subtitle" style={{ fontWeight: '600', color: '#333333' }}>Not Ekle</ThemedText>
              <TouchableOpacity
                onPress={() => {
                  setShowAddNoteModal(false);
                  setNoteContent('');
                  setNoteTitle('');
                }}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <ThemedText style={styles.modalLabel}>Not Başlığı</ThemedText>
              <TextInput
                style={[
                  styles.titleInput,
                  { backgroundColor: '#FFFFFF', color: '#333333', borderColor: '#DDDDDD' }
                ]}
                placeholder="Not başlığını giriniz..."
                placeholderTextColor="rgba(0, 0, 0, 0.4)"
                value={noteTitle}
                onChangeText={setNoteTitle}
              />

              <ThemedText style={styles.modalLabel}>Not İçeriği</ThemedText>
              <TextInput
                style={[
                  styles.noteInput,
                  { backgroundColor: '#FFFFFF', color: '#333333', borderColor: '#DDDDDD' }
                ]}
                placeholder="Not içeriğini giriniz..."
                placeholderTextColor="rgba(0, 0, 0, 0.4)"
                multiline
                numberOfLines={5}
                textAlignVertical="top"
                value={noteContent}
                onChangeText={setNoteContent}
              />

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[
                    styles.modalButton,
                    styles.cancelButton,
                    { backgroundColor: '#F0F0F0' }
                  ]}
                  onPress={() => {
                    setShowAddNoteModal(false);
                    setNoteContent('');
                    setNoteTitle('');
                  }}
                >
                  <ThemedText style={[styles.cancelButtonText, { color: '#333333' }]}>İptal</ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.modalButton,
                    styles.saveButton,
                    !noteContent.trim() && styles.disabledButton
                  ]}
                  onPress={handleAddNote}
                  disabled={!noteContent.trim() || addingNote}
                >
                  {addingNote ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <ThemedText style={styles.saveButtonText}>Kaydet</ThemedText>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      )}
    </BackgroundWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Modern centered header
  modernHeader: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    ...(Platform.OS !== 'web' && {
      paddingVertical: 6,
      paddingHorizontal: 12,
    }),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  headerIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerStatsContainer: {
    marginLeft: 0,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  headerStats: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  // Case info card
  caseInfoCard: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 3,
    marginHorizontal: 16,
  },
  // Case card styles
  caseTypeIndicator: {
    height: 3,
    width: '100%',
  },
  singleRowHeader: {
    padding: 8,
    paddingHorizontal: 12,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...(Platform.OS !== 'web' && {
      padding: 6,
      paddingHorizontal: 10,
    }),
  },
  headerLeftSection: {
    flex: 4,
    paddingRight: 4,
  },
  headerRightSection: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  inlineInfoContainer: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
    marginTop: 4,
    marginBottom: 4,
    justifyContent: 'space-between',
    ...(Platform.OS !== 'web' && {
      marginTop: 2,
      marginBottom: 2,
      flexDirection: 'column',
    }),
  },
  inlineInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    width: '20%',
    paddingRight: 4,
    ...(Platform.OS !== 'web' && {
      width: '100%',
      marginBottom: 4,
      paddingVertical: 2,
    }),
  },
  inlineInfoText: {
    fontSize: 13,
    color: '#9ca3af',
    marginLeft: 6,
    ...(Platform.OS !== 'web' && {
      marginLeft: 4,
      fontSize: 12,
    }),
  },
  // Basic info card styles for the Detaylar tab
  basicInfoCard: {
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    overflow: 'hidden',
  },
  basicInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  basicInfoTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.tint,
  },
  basicInfoTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  basicInfoTypeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  basicInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 12,
  },
  basicInfoItem: {
    width: '50%',
    paddingVertical: 6,
    paddingHorizontal: 4,
    ...(Platform.OS !== 'web' && {
      width: '100%',
    }),
  },
  basicInfoItemFull: {
    width: '100%',
  },
  basicInfoLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    color: '#64748b',
  },
  basicInfoValue: {
    fontSize: 15,
    fontWeight: '500',
  },
  caseInfoTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  caseInfoTypeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  caseInfoStatusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  headerContainer: {
    width: '100%',
    zIndex: 10,
  },
  headerBackground: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerBackgroundLight: {
    backgroundColor: '#4A78B0',
  },
  headerBackgroundDark: {
    backgroundColor: '#2C4A6B',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'web' ? 12 : 60,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitleText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  logo: {
    marginRight: 8,
  },
  menuButton: {
    marginRight: 16,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#EF4444',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 2,
  },
  notificationBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: '600',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  caseHeaderCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  caseHeaderContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  caseIconContainer: {
    marginRight: 16,
  },
  caseIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  caseHeaderInfo: {
    flex: 1,
  },
  caseHeaderTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    flexWrap: 'wrap',
  },
  caseNumber: {
    fontSize: 18,
    marginRight: 12,
  },
  caseName: {
    marginBottom: 8,
    textAlign: 'center',
  },
  caseHeaderDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  caseDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  caseDetailText: {
    fontSize: 14,
    color: '#9ca3af',
    marginLeft: 6,
  },
  caseTypeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginBottom: 8,
  },
  caseTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap',
    justifyContent: 'center',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  tabButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: 100,
    alignItems: 'center',
  },
  activeTabButton: {
    backgroundColor: Colors.light.tint,
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  contentCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  addButtonText: {
    marginLeft: 4,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.tint,
  },
  editButtonText: {
    marginLeft: 4,
    color: Colors.light.tint,
    fontWeight: '600',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  deleteButtonText: {
    marginLeft: 4,
    color: '#EF4444',
    fontWeight: '600',
  },
  sectionDivider: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: 16,
  },
  sectionSubtitle: {
    marginBottom: 16,
    fontSize: 16,
  },
  emptyDetailContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyDetailText: {
    textAlign: 'center',
    marginTop: 12,
    color: '#9ca3af',
    lineHeight: 20,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#9ca3af',
    marginBottom: 2,
  },
  partyItem: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  partyTypeIndicator: {
    width: 4,
    height: '100%',
    borderRadius: 2,
    marginRight: 12,
  },
  partyInfo: {
    flex: 1,
  },
  partySifat: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 2,
  },
  historyItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  historyDateContainer: {
    width: 80,
    marginRight: 12,
  },
  historyDate: {
    fontSize: 14,
    color: '#9ca3af',
  },
  historyContent: {
    flex: 1,
  },
  historyDescription: {
    marginTop: 4,
  },
  safahatList: {
    width: '100%',
  },
  safahatItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  safahatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  safahatDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  safahatDate: {
    fontSize: 14,
    marginLeft: 6,
    color: '#64748b',
    fontWeight: '500',
  },
  safahatBirim: {
    fontSize: 12,
    color: '#9ca3af',
    fontStyle: 'italic',
  },
  safahatContent: {
    marginTop: 4,
  },
  safahatType: {
    fontSize: 16,
    marginBottom: 6,
    color: Colors.light.tint,
  },
  safahatDescription: {
    fontSize: 15,
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 235, 235, 0.8)',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 107, 107, 0.3)',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#E53E3E',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 5,
    fontWeight: '500',
  },
  errorCode: {
    fontSize: 14,
    color: '#718096',
    textAlign: 'center',
    marginTop: 5,
  },
  // Tahsilat styles
  tahsilatSummaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  tahsilatSummaryItem: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  tahsilatSummaryLabel: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 8,
    marginBottom: 4,
    fontWeight: '500',
  },
  tahsilatSummaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.tint,
  },
  tahsilatSection: {
    marginBottom: 20,
  },
  tahsilatSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  tahsilatSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    color: Colors.light.tint,
  },
  tahsilatItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  reddiyatItem: {
    borderLeftWidth: 3,
    borderLeftColor: '#EF4444',
  },
  harcItem: {
    borderLeftWidth: 3,
    borderLeftColor: '#6366F1',
  },
  tahsilatItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  tahsilatItemLeft: {
    flex: 2,
  },
  tahsilatItemRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  tahsilatItemTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 4,
  },
  tahsilatItemDate: {
    fontSize: 13,
    color: '#64748b',
  },
  tahsilatItemAmount: {
    fontSize: 16,
    fontWeight: '700',
    color: '#10B981',
    marginBottom: 4,
  },
  tahsilatItemMakbuz: {
    fontSize: 12,
    color: '#64748b',
  },
  tahsilatItemDetails: {
    marginTop: 4,
  },
  tahsilatItemDetailText: {
    fontSize: 14,
    marginBottom: 4,
    lineHeight: 20,
  },
  tahsilatItemDetailLabel: {
    fontWeight: '600',
    color: '#64748b',
  },
  noteItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  noteTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.tint,
    marginBottom: 4,
    flex: 1,
  },
  noteActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteDeleteButton: {
    padding: 5,
    marginRight: 10,
  },
  noteDate: {
    fontSize: 12,
    color: '#9ca3af',
  },
  noteContent: {
    lineHeight: 20,
    marginTop: 4,
  },
  emptyText: {
    textAlign: 'center',
    color: '#9ca3af',
    marginVertical: 20,
  },
  loadingCard: {
    borderRadius: 16,
    padding: 30,
    marginHorizontal: 16,
    marginTop: 40,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
    maxWidth: 600,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorCard: {
    borderRadius: 16,
    padding: 30,
    marginHorizontal: 16,
    marginTop: 40,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
    maxWidth: 600,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainerFull: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTextFull: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 16,
    textAlign: 'center',
    color: '#EF4444',
    maxWidth: '80%',
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  // New styles for the card-style parties display
  partiesList: {
    marginTop: 8,
  },
  partyItemContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
  },
  partyName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  partyDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  partyRole: {
    fontSize: 14,
    color: '#9ca3af',
    marginRight: 8,
  },
  partyType: {
    fontSize: 14,
    color: '#9ca3af',
  },
  partyLawyer: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 8,
  },
  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    width: '90%',
    maxWidth: 500,
    borderRadius: 8,
    overflow: 'hidden',
    padding: 20,
    backgroundColor: '#FFFFFF',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 5,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    marginHorizontal: -10,
    paddingHorizontal: 10,
    backgroundColor: '#FFFFFF',
  },
  closeButton: {
    padding: 5,
  },
  modalContent: {
    width: '100%',
  },
  modalLabel: {
    marginBottom: 12,
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  titleInput: {
    borderWidth: 1,
    borderColor: 'rgba(150, 150, 150, 0.3)',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: 'rgba(150, 150, 150, 0.3)',
    borderRadius: 8,
    padding: 12,
    minHeight: 120,
    fontSize: 16,
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 15,
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#F0F0F0',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333333',
  },
  saveButton: {
    backgroundColor: Colors.light.tint,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  disabledButton: {
    opacity: 0.5,
  },
  // Cached data notification styles
  cachedDataNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(49, 130, 206, 0.1)',
    padding: 8,
    borderRadius: 6,
    marginBottom: 12,
  },
  cachedDataText: {
    fontSize: 12,
    color: '#3182CE',
    marginLeft: 6,
  },

});
