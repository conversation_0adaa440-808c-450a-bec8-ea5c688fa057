import apiClient from './api';

// Müvekkil servisi
const clientService = {
  // Tüm müvekkilleri getir - CasePartiesContext'ten alınıyor, API isteği yapılmıyor
  getAllClients: async () => {
    try {
      // NOT: Bu fonksiyon artık API'ye istek atmak yerine CasePartiesContext'ten veri kullanıyor
      // Boş dizi döndürüyoruz çünkü veriler context'ten alınıyor
      console.log('getAllClients called - using CasePartiesContext instead of API');
      return [];
    } catch (error) {
      console.error('Get all clients error:', error);
      return [];
    }
  },

  // Müvekkil detayını getir
  getClientById: async (clientId) => {
    try {
      const response = await apiClient.get(`/api/clients/${clientId}`);
      return response.data;
    } catch (error) {
      console.error(`Get client ${clientId} error:`, error);
      throw error;
    }
  },

  // Yeni müvekkil oluştur
  createClient: async (clientData) => {
    try {
      const response = await apiClient.post('/api/clients', clientData);
      return response.data;
    } catch (error) {
      console.error('Create client error:', error);
      throw error;
    }
  },

  // Müvekkil güncelle
  updateClient: async (clientId, clientData) => {
    try {
      const response = await apiClient.put(`/api/clients/${clientId}`, clientData);
      return response.data;
    } catch (error) {
      console.error(`Update client ${clientId} error:`, error);
      throw error;
    }
  },

  // Müvekkil sil
  deleteClient: async (clientId) => {
    try {
      const response = await apiClient.delete(`/api/clients/${clientId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete client ${clientId} error:`, error);
      throw error;
    }
  },

  // Müvekkile not ekle
  addClientNote: async (clientId, noteData) => {
    try {
      const response = await apiClient.post(`/api/clients/${clientId}/notes`, noteData);
      return response.data;
    } catch (error) {
      console.error(`Add note to client ${clientId} error:`, error);
      throw error;
    }
  },

  // Müvekkil notu güncelle
  updateClientNote: async (clientId, noteId, noteData) => {
    try {
      const response = await apiClient.put(`/api/clients/${clientId}/notes/${noteId}`, noteData);
      return response.data;
    } catch (error) {
      console.error(`Update client ${clientId} note ${noteId} error:`, error);
      throw error;
    }
  },

  // Müvekkil notu sil
  deleteClientNote: async (clientId, noteId) => {
    try {
      const response = await apiClient.delete(`/api/clients/${clientId}/notes/${noteId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete client ${clientId} note ${noteId} error:`, error);
      throw error;
    }
  },

  // Müvekkilin davalarını getir
  getClientCases: async (clientId) => {
    try {
      const response = await apiClient.get(`/api/clients/${clientId}/cases`);
      return response.data;
    } catch (error) {
      console.error(`Get client ${clientId} cases error:`, error);
      throw error;
    }
  }
};

export default clientService;
