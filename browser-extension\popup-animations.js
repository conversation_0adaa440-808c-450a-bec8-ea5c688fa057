// Animasyon
document.addEventListener('DOMContentLoaded', function() {
  // Sol taraf animasyonu
  const logoContainer = document.querySelector('.logo-icon-container');
  const appTitle = document.querySelector('.app-title');
  const motto = document.querySelector('.motto');
  const divider = document.querySelector('.divider');
  const descriptionText = document.querySelector('.description-text');

  // Sağ taraf animasyonu
  const form = document.querySelector('.form');

  // Animasyon fonksiyonu
  function animateElements() {
    // Logo animasyonu
    logoContainer.style.opacity = '0';
    logoContainer.style.transform = 'scale(0.8)';

    // Diğer elementlerin başlangıç durumu
    [appTitle, motto, divider, descriptionText].forEach(el => {
      el.style.opacity = '0';
    });

    // Form animasyonu
    form.style.opacity = '0';
    form.style.transform = 'translateY(30px)';

    // Animasyonları başlat
    setTimeout(() => {
      logoContainer.style.transition = 'opacity 0.8s ease, transform 0.5s ease';
      logoContainer.style.opacity = '1';
      logoContainer.style.transform = 'scale(1)';

      setTimeout(() => {
        appTitle.style.transition = 'opacity 0.8s ease';
        appTitle.style.opacity = '1';

        setTimeout(() => {
          motto.style.transition = 'opacity 0.8s ease';
          motto.style.opacity = '1';

          setTimeout(() => {
            divider.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            divider.style.opacity = '1';
            divider.style.transform = 'scaleX(1)';

            setTimeout(() => {
              descriptionText.style.transition = 'opacity 0.8s ease';
              descriptionText.style.opacity = '1';
            }, 200);
          }, 200);
        }, 200);
      }, 300);
    }, 300);

    // Form animasyonu
    setTimeout(() => {
      form.style.transition = 'opacity 0.8s ease, transform 0.5s ease';
      form.style.opacity = '1';
      form.style.transform = 'translateY(0)';
    }, 500);
  }

  // Animasyonu başlat
  animateElements();
});

// Şifre göster/gizle işlevselliği
document.addEventListener('DOMContentLoaded', function() {
  const togglePassword = document.getElementById('togglePassword');
  if (togglePassword) {
    togglePassword.addEventListener('click', function() {
      const passwordInput = document.getElementById('password');
      const eyeIcon = this.querySelector('i');

      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
      } else {
        passwordInput.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
      }
    });
  }
});
