import { StyleSheet, TouchableOpacity, View, ScrollView, Dimensions, ActivityIndicator, Alert, FlatList } from 'react-native';
import React, { useState, useEffect } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import taskService from '@/services/taskService';

// Öncelik renkleri
const PRIORITY_COLORS = {
  'DÜŞÜK': { bg: 'rgba(16, 185, 129, 0.1)', text: '#10B981' },
  'ORTA': { bg: 'rgba(245, 158, 11, 0.1)', text: '#F59E0B' },
  'YÜKSEK': { bg: 'rgba(239, 68, 68, 0.1)', text: '#EF4444' },
  'KRİTİK': { bg: 'rgba(124, 58, 237, 0.1)', text: '#7C3AED' },
};

// Fallback görev verileri (API bağlantısı başarısız olduğunda kullanılır)
const TASKS_DATA = [];

// Haftanın günleri
const DAYS_OF_WEEK = ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'];

export default function CalendarScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState([]);
  const [dayTasks, setDayTasks] = useState([]);

  // API state
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // API'den görevleri getir
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true);
        setError('');
        const data = await taskService.getAllTasks();
        setTasks(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching tasks:', err);
        setError('Görevler yüklenirken bir hata oluştu.');
        setLoading(false);
        // Geliştirme aşamasında örnek verileri kullan
        setTasks(TASKS_DATA);
      }
    };

    fetchTasks();
  }, []);

  // Takvim günlerini oluştur
  useEffect(() => {
    const days = [];
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Ayın ilk günü
    const firstDay = new Date(year, month, 1);
    // Ayın son günü
    const lastDay = new Date(year, month + 1, 0);

    // Önceki ayın son günleri
    const firstDayOfWeek = firstDay.getDay() || 7; // Pazartesi: 1, Pazar: 7
    const prevMonthLastDay = new Date(year, month, 0).getDate();

    for (let i = firstDayOfWeek - 1; i > 0; i--) {
      const date = new Date(year, month - 1, prevMonthLastDay - i + 1);
      days.push({
        date,
        day: date.getDate(),
        month: 'prev',
        hasTask: tasks.some(task => {
          const taskDate = new Date(task.dueDate);
          return taskDate.getDate() === date.getDate() &&
                 taskDate.getMonth() === date.getMonth() &&
                 taskDate.getFullYear() === date.getFullYear();
        }),
      });
    }

    // Mevcut ayın günleri
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      days.push({
        date,
        day: i,
        month: 'current',
        hasTask: tasks.some(task => {
          const taskDate = new Date(task.dueDate);
          return taskDate.getDate() === i &&
                 taskDate.getMonth() === month &&
                 taskDate.getFullYear() === year;
        }),
        isToday: new Date().getDate() === i &&
                 new Date().getMonth() === month &&
                 new Date().getFullYear() === year,
      });
    }

    // Sonraki ayın ilk günleri
    const remainingDays = 42 - days.length; // 6 satır x 7 gün
    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(year, month + 1, i);
      days.push({
        date,
        day: i,
        month: 'next',
        hasTask: tasks.some(task => {
          const taskDate = new Date(task.dueDate);
          return taskDate.getDate() === i &&
                 taskDate.getMonth() === month + 1 &&
                 taskDate.getFullYear() === year;
        }),
      });
    }

    setCalendarDays(days);
  }, [currentDate, tasks]);

  // Seçili günün görevlerini filtrele
  useEffect(() => {
    const filteredTasks = tasks.filter(task => {
      const taskDate = new Date(task.dueDate);
      return taskDate.getDate() === selectedDate.getDate() &&
             taskDate.getMonth() === selectedDate.getMonth() &&
             taskDate.getFullYear() === selectedDate.getFullYear();
    });

    // Saate göre sırala
    filteredTasks.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

    setDayTasks(filteredTasks);
  }, [selectedDate, tasks]);

  // Önceki aya git
  const goToPrevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  // Sonraki aya git
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  // Bugüne git
  const goToToday = () => {
    const today = new Date();
    setCurrentDate(new Date(today.getFullYear(), today.getMonth(), 1));
    setSelectedDate(today);
  };

  // Tarih formatını düzenle
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
  };

  const navigateToTaskDetail = (id) => {
    router.push(`/tasks/${id}`);
  };

  const navigateToAddTask = () => {
    router.push('/tasks/add');
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Takvim</ThemedText>
        <TouchableOpacity onPress={navigateToAddTask}>
          <Ionicons name="add-circle" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
      </ThemedView>

      <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.calendarHeader}>
        <View style={styles.monthSelector}>
          <TouchableOpacity onPress={goToPrevMonth}>
            <Ionicons name="chevron-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
          </TouchableOpacity>

          <ThemedText type="subtitle">
            {currentDate.toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' })}
          </ThemedText>

          <TouchableOpacity onPress={goToNextMonth}>
            <Ionicons name="chevron-forward" size={24} color={Colors[colorScheme ?? 'light'].tint} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.todayButton} onPress={goToToday}>
          <ThemedText style={styles.todayButtonText}>Bugün</ThemedText>
        </TouchableOpacity>
      </BlurView>

      {loading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>Görevler yükleniyor...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <ThemedText style={styles.errorText}>{error}</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setLoading(true);
              taskService.getAllTasks()
                .then(data => {
                  setTasks(data);
                  setError('');
                })
                .catch(err => {
                  console.error('Error retrying fetch tasks:', err);
                  setError('Görevler yüklenirken bir hata oluştu.');
                  // Geliştirme aşamasında örnek verileri kullan
                  setTasks(TASKS_DATA);
                })
                .finally(() => setLoading(false));
            }}
          >
            <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : (
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.calendarContainer}>
          {/* Haftanın günleri */}
          <View style={styles.weekDaysContainer}>
            {DAYS_OF_WEEK.map((day, index) => (
              <View key={index} style={styles.weekDay}>
                <ThemedText style={styles.weekDayText}>{day}</ThemedText>
              </View>
            ))}
          </View>

          {/* Takvim günleri */}
          <View style={styles.daysContainer}>
            {calendarDays.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.dayItem,
                item.month !== 'current' && styles.otherMonthDay,
                item.isToday && styles.todayItem,
                selectedDate.getDate() === item.date.getDate() &&
                selectedDate.getMonth() === item.date.getMonth() &&
                selectedDate.getFullYear() === item.date.getFullYear() && styles.selectedDay
              ]}
              onPress={() => setSelectedDate(item.date)}
            >
              <ThemedText style={[
                styles.dayText,
                item.month !== 'current' && styles.otherMonthDayText,
                item.isToday && styles.todayText,
                selectedDate.getDate() === item.date.getDate() &&
                selectedDate.getMonth() === item.date.getMonth() &&
                selectedDate.getFullYear() === item.date.getFullYear() && styles.selectedDayText
              ]}>
                {item.day}
              </ThemedText>
              {item.hasTask && <View style={styles.taskIndicator} />}
            </TouchableOpacity>
          ))}
        </View>
      </BlurView>
      )}

      <View style={styles.selectedDateHeader}>
        <ThemedText type="subtitle">
          {selectedDate.toLocaleDateString('tr-TR', { weekday: 'long', day: 'numeric', month: 'long' })}
        </ThemedText>
        <ThemedText style={styles.taskCount}>
          {dayTasks.length} {dayTasks.length === 1 ? 'görev' : 'görev'}
        </ThemedText>
      </View>

      <ScrollView contentContainerStyle={styles.taskListContainer}>
        {loading ? (
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} style={styles.taskListLoader} />
        ) : dayTasks.length > 0 ? (
          dayTasks.map(task => (
            <TouchableOpacity
              key={task.id}
              style={styles.taskItem}
              onPress={() => navigateToTaskDetail(task.id)}
            >
              <View style={styles.taskTimeContainer}>
                <ThemedText style={styles.taskTime}>{formatTime(task.dueDate)}</ThemedText>
                <View style={[
                  styles.taskTypeBadge,
                  { backgroundColor: PRIORITY_COLORS[task.priority].bg }
                ]}>
                  <ThemedText style={[
                    styles.taskTypeText,
                    { color: PRIORITY_COLORS[task.priority].text }
                  ]}>
                    {task.type}
                  </ThemedText>
                </View>
              </View>

              <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.taskContent}>
                <View style={styles.taskHeader}>
                  <ThemedText type="defaultSemiBold">{task.title}</ThemedText>
                  {task.repeat > 0 && (
                    <Ionicons name="repeat" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                  )}
                </View>

                <ThemedText style={styles.taskDescription} numberOfLines={2}>
                  {task.description}
                </ThemedText>

                {task.location && (
                  <View style={styles.locationContainer}>
                    <Ionicons name="location-outline" size={14} color={isDark ? '#9ca3af' : '#64748b'} />
                    <ThemedText style={styles.locationText}>{task.location}</ThemedText>
                  </View>
                )}

                {(task.relatedCase || task.relatedClient) && (
                  <View style={styles.taskRelations}>
                    {task.relatedCase && (
                      <View style={styles.relationBadge}>
                        <Ionicons name="folder-outline" size={12} color={isDark ? '#9ca3af' : '#64748b'} />
                        <ThemedText style={styles.relationText}>Dava #{task.relatedCase}</ThemedText>
                      </View>
                    )}

                    {task.relatedClient && (
                      <View style={styles.relationBadge}>
                        <Ionicons name="person-outline" size={12} color={isDark ? '#9ca3af' : '#64748b'} />
                        <ThemedText style={styles.relationText}>
                          {task.relatedClient === '1' ? 'John Smith' :
                           task.relatedClient === '2' ? 'Sarah Williams' :
                           task.relatedClient === '3' ? 'Michael Davis' :
                           task.relatedClient === '4' ? 'Thompson LLC' :
                           task.relatedClient === '5' ? 'Carlos Martinez' : 'Müvekkil'}
                        </ThemedText>
                      </View>
                    )}
                  </View>
                )}
              </BlurView>
            </TouchableOpacity>
          ))
        ) : (
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
            <ThemedText style={styles.emptyText}>Bu tarihte görev bulunmuyor</ThemedText>
            <TouchableOpacity
              style={styles.addTaskButton}
              onPress={navigateToAddTask}
            >
              <Ionicons name="add-circle" size={16} color="white" />
              <ThemedText style={styles.addTaskButtonText}>Görev Ekle</ThemedText>
            </TouchableOpacity>
          </BlurView>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  monthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  todayButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderRadius: 16,
  },
  todayButtonText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  calendarContainer: {
    marginHorizontal: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginBottom: 16,
  },
  weekDaysContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  weekDay: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  weekDayText: {
    fontWeight: '600',
    fontSize: 12,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayItem: {
    width: `${100 / 7}%`,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  dayText: {
    fontSize: 14,
  },
  otherMonthDay: {
    opacity: 0.5,
  },
  otherMonthDayText: {
    color: '#9ca3af',
  },
  todayItem: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  todayText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  selectedDay: {
    backgroundColor: Colors.light.tint,
  },
  selectedDayText: {
    color: 'white',
    fontWeight: '600',
  },
  taskIndicator: {
    position: 'absolute',
    bottom: 8,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.light.tint,
  },
  selectedDateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  taskCount: {
    fontSize: 14,
    opacity: 0.7,
  },
  taskListContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  taskItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  taskTimeContainer: {
    width: 80,
    marginRight: 12,
  },
  taskTime: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  taskTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  taskTypeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  taskContent: {
    flex: 1,
    borderRadius: 16,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 12,
    marginLeft: 4,
    color: '#64748b',
  },
  taskRelations: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  relationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  relationText: {
    fontSize: 10,
    marginLeft: 4,
  },
  emptyContainer: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  emptyText: {
    marginTop: 16,
    marginBottom: 24,
    opacity: 0.7,
  },
  addTaskButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  addTaskButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  taskListLoader: {
    marginTop: 20,
  },
});