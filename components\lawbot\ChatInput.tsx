import React, { useState, useRef } from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Platform, Keyboard } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface ChatInputProps {
  onSend: (message: string) => void;
  isLoading: boolean;
}

export default function ChatInput({ onSend, isLoading }: ChatInputProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [message, setMessage] = useState('');
  const inputRef = useRef<TextInput>(null);

  const handleSend = () => {
    if (message.trim() && !isLoading) {
      onSend(message.trim());
      setMessage('');
      // Dismiss keyboard on Android
      if (Platform.OS === 'android') {
        Keyboard.dismiss();
      }
    }
  };

  // On Android, use a regular View instead of BlurView for better performance
  const Container = Platform.OS === 'android' ? View : BlurView;

  return (
    <Container
      intensity={Platform.OS !== 'android' ? (isDark ? 40 : 60) : undefined}
      tint={Platform.OS !== 'android' ? (isDark ? 'dark' : 'light') : undefined}
      style={[styles.container, Platform.OS === 'android' && { backgroundColor: isDark ? '#333' : '#f0f0f0' }]}
    >
      <TextInput
        ref={inputRef}
        style={[
          styles.input,
          { color: isDark ? '#fff' : '#000' }
        ]}
        placeholder="Type your message..."
        placeholderTextColor={isDark ? '#aaa' : '#666'}
        value={message}
        onChangeText={setMessage}
        multiline
        maxLength={1000}
        editable={!isLoading}
        returnKeyType="send"
        onSubmitEditing={handleSend}
      />
      <TouchableOpacity
        style={[
          styles.sendButton,
          { backgroundColor: Colors[colorScheme ?? 'light'].tint },
          (!message.trim() || isLoading) && styles.disabledSendButton
        ]}
        onPress={handleSend}
        disabled={isLoading || !message.trim()}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" size="small" />
        ) : (
          <Ionicons name="send" size={20} color="#fff" />
        )}
      </TouchableOpacity>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'ios' ? 8 : 10,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(150, 150, 150, 0.2)',
    backgroundColor: Platform.OS === 'android' ? 'rgba(245, 245, 245, 0.9)' : undefined,
  },
  input: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    maxHeight: 120,
    backgroundColor: 'rgba(150, 150, 150, 0.1)',
    fontSize: 16,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    // Make the touch target larger on mobile
    padding: Platform.OS === 'web' ? 0 : 4,
  },
  disabledSendButton: {
    opacity: 0.5,
  },
});
