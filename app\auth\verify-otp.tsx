import React, { useState, useContext, useEffect } from 'react';
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View,
  Text,
  Dimensions,
  Animated,
  SafeAreaView,
  Alert,
  ImageBackground
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import authService from '@/services/authService';
import apiClient from '@/services/api';
import { AuthContext } from '@/app/_layout';
import Footer from '@/components/layout/Footer';

export default function VerifyOTPScreen() {
  const params = useLocalSearchParams();
  const [email, setEmail] = useState('');

  // Extract email from params, query string, or AsyncStorage
  useEffect(() => {
    const getEmailFromAllSources = async () => {
      // First try from route params
      const emailParam = params.email as string;
      console.log('Email param from URL params:', emailParam);

      if (emailParam) {
        setEmail(emailParam);
        return;
      }

      // Then try from query string if on web
      if (Platform.OS === 'web' && typeof window !== 'undefined') {
        try {
          const queryString = window.location.search;
          const urlParams = new URLSearchParams(queryString);
          const emailFromQuery = urlParams.get('email');

          console.log('Email from query string:', emailFromQuery);

          if (emailFromQuery) {
            setEmail(emailFromQuery);
            return;
          }
        } catch (error) {
          console.error('Error parsing query string:', error);
        }
      }

      // Finally try from AsyncStorage
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const storedEmail = await AsyncStorage.getItem('verificationEmail');
        console.log('Email from AsyncStorage:', storedEmail);

        if (storedEmail) {
          setEmail(storedEmail);
          return;
        }
      } catch (storageError) {
        console.error('Error retrieving email from AsyncStorage:', storageError);
      }

      console.log('Could not find email from any source');
    };

    getEmailFromAllSources();
  }, [params]);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { login: contextLogin } = useContext(AuthContext);

  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [countdown, setCountdown] = useState(180); // Changed from 60 to 180 seconds
  const [canResend, setCanResend] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(5);

  // State change effect
  useEffect(() => {
    // No logging needed
  }, [countdown, canResend]);

  // Redirect countdown timer
  useEffect(() => {
    if (showSuccessModal && redirectCountdown > 0) {
      const timer = setTimeout(() => {
        setRedirectCountdown(redirectCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (showSuccessModal && redirectCountdown === 0) {
      // Redirect to login page when countdown reaches zero
      router.replace('/auth/login');
    }
  }, [showSuccessModal, redirectCountdown]);

  const { width, height } = Dimensions.get('window');
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];

  // Log email parameter when component mounts and try to send OTP if needed
  useEffect(() => {
    console.log('OTP verification screen mounted with email state:', email);

    // Only proceed if we have an email
    if (!email || email.trim() === '') {
      console.error('No email available yet for OTP verification');
      return;
    }

    // Check if we need to send OTP (if user navigated here directly)
    const checkAndSendOtp = async () => {
      try {
        console.log('Attempting to send OTP to:', email);
        // Try to send OTP if it hasn't been sent yet
        // This is a fallback in case the user navigated directly to this screen
        await authService.sendOtp(email);
        console.log('Initial OTP sent from verification screen');
      } catch (err) {
        console.log('Error sending initial OTP or OTP already sent:', err);
        // We don't show an error here as the OTP might have already been sent
      }
    };

    // Small delay to ensure email state is stable
    const timer = setTimeout(() => {
      if (email && email.trim() !== '') {
        checkAndSendOtp();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [email]);

  // Animate the screen when it mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Countdown timer for OTP resend
  useEffect(() => {
    if (countdown > 0 && !canResend) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      // Just enable the resend button when countdown reaches zero
      setCanResend(true);
    }
  }, [countdown, canResend]);

  // Handle OTP input change
  const handleOtpChange = (text: string) => {
    // Only allow alphanumeric characters and limit to 8 characters
    // This allows both digits and letters since OTP might contain both
    const alphanumericOnly = text.replace(/[^a-zA-Z0-9]/g, '').slice(0, 8);
    setOtp(alphanumericOnly);
  };

  // Handle OTP verification
  const handleVerify = async () => {
    // Validate OTP - allow OTPs between 6 and 8 characters
    if (otp.length < 6) {
      setError('Lütfen doğrulama kodunu tam olarak girin');
      return;
    }

    if (!email) {
      setError('E-posta adresi bulunamadı');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Check if this is a password reset flow
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const isPasswordReset = await AsyncStorage.getItem('password_reset_flow') === 'true' ||
                              params?.isPasswordReset === 'true';

      if (isPasswordReset) {
        try {
          // Send validation request to forgot-password endpoint
          // Note: validateOtp expects parameters in the order (otpValue, otpEmail)
          const response = await authService.validateOtp(otp, email);

          if (response && response.validatedOtp) {
            setSuccess(true);

            // Store the validated OTP for the password update
            await AsyncStorage.setItem('validatedOtp', JSON.stringify(response.validatedOtp));

            // Navigate to password update screen
            router.push({
              pathname: '/auth/reset-password',
              params: { email }
            });
          } else {
            // Validation failed
            setError('Doğrulama kodu geçersiz veya süresi dolmuş');
            Alert.alert(
              'Doğrulama Kodu Hatalı',
              'Girdiğiniz kod hatalı. Lütfen kodunuzu kontrol edip tekrar deneyin.' +
              (canResend ? ' Veya yeni bir kod isteyebilirsiniz.' : ` ${countdown} saniye sonra yeni bir kod isteyebilirsiniz.`),
              [{ text: 'Tamam' }]
            );
          }
        } catch (err) {
          // Always show error for invalid OTP
          if (err.response && err.response.status === 401) {
            // Show error message for invalid OTP
            setError('Doğrulama kodu geçersiz veya süresi dolmuş');
            Alert.alert(
              'Doğrulama Kodu Hatalı',
              'Girdiğiniz kod hatalı. Lütfen kodunuzu kontrol edip tekrar deneyin.' +
              (canResend ? ' Veya yeni bir kod isteyebilirsiniz.' : ` ${countdown} saniye sonra yeni bir kod isteyebilirsiniz.`),
              [{ text: 'Tamam' }]
            );
          }

          // Default error handling
          setError('Doğrulama kodu geçersiz veya süresi dolmuş');
          Alert.alert(
            'Doğrulama Hatası',
            'Doğrulama kodu doğrulanamadı. Lütfen tekrar deneyin.' +
            (canResend ? ' Veya yeni bir kod isteyebilirsiniz.' : ` ${countdown} saniye sonra yeni bir kod isteyebilirsiniz.`),
            [{ text: 'Tamam' }]
          );
        }
      } else {
        // Regular email verification flow

        // Send verification request
        const response = await authService.verifyEmail(otp, email);

        // Check the verified field in the response
        if (response && response.verified === true) {
          setSuccess(true);

          // Try to store the verified email in AsyncStorage for convenience
          try {
            await AsyncStorage.setItem('verifiedEmail', email);

            // Also store a flag to show the popup on the login screen
            await AsyncStorage.setItem('show_verification_success', 'true');
          } catch (storageError) {
            // Failed to store verified email in AsyncStorage
          }

          // Show success modal and start redirect countdown
          setShowSuccessModal(true);
        } else {
          // Verification failed but we got a response (verified: false)

          // Prepare a user-friendly error message
          const errorMessage =
            response?.responseMessage ||
            'Girdiğiniz doğrulama kodu hatalı. Lütfen e-postanızı kontrol edip doğru kodu girdiğinizden emin olun.';

          // Set the error message in the UI
          setError(errorMessage);

          // Clear the OTP input to let the user try again
          setOtp('');

          // Show a friendly alert with clear instructions
          Alert.alert(
            'Doğrulama Kodu Hatalı',
            'Girdiğiniz kod hatalı. Lütfen kodunuzu kontrol edip tekrar deneyin.' +
            (canResend ? ' Veya yeni bir kod isteyebilirsiniz.' : ` ${countdown} saniye sonra yeni bir kod isteyebilirsiniz.`),
            [{ text: 'Tamam' }]
          );
        }
      }
    } catch (err: any) {
      // Handle errors
      if (err.response) {

        // We should no longer get 401 errors here since we're handling them in the service
        // But just in case, handle them the same way as other errors

        // Handle server errors with response messages
        if (err.response.data && err.response.data.responseMessage) {
          setError(err.response.data.responseMessage);

          // Clear the OTP input to let the user try again
          setOtp('');

          Alert.alert(
            'Doğrulama Hatası',
            err.response.data.responseMessage +
            (canResend ? ' Yeni bir kod isteyebilirsiniz.' : ` ${countdown} saniye sonra yeni bir kod isteyebilirsiniz.`),
            [{ text: 'Tamam' }]
          );
        } else {
          setError(`Sunucu hatası: ${err.response.status}`);

          // Clear the OTP input to let the user try again
          setOtp('');

          Alert.alert(
            'Sunucu Hatası',
            `İşlem sırasında bir sunucu hatası oluştu (${err.response.status}). Lütfen tekrar deneyin` +
            (canResend ? ' veya yeni bir kod isteyin.' : ` veya ${countdown} saniye sonra yeni bir kod isteyin.`),
            [{ text: 'Tamam' }]
          );
        }
      }
      // Handle network errors
      else if (err.request) {
        setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');

        // Clear the OTP input to let the user try again
        setOtp('');

        Alert.alert(
          'Bağlantı Hatası',
          'Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edip tekrar deneyin.',
          [{ text: 'Tamam' }]
        );
      }
      // Handle other errors
      else if (err.message) {
        setError(`Doğrulama hatası: ${err.message}`);

        // Clear the OTP input to let the user try again
        setOtp('');

        Alert.alert(
          'Doğrulama Hatası',
          `Doğrulama işlemi sırasında bir hata oluştu: ${err.message}. Lütfen tekrar deneyin` +
          (canResend ? ' veya yeni bir kod isteyin.' : ` veya ${countdown} saniye sonra yeni bir kod isteyin.`),
          [{ text: 'Tamam' }]
        );
      }
      // Handle unknown errors
      else {
        setError('Doğrulama sırasında bir hata oluştu. Lütfen tekrar deneyin.');

        // Clear the OTP input to let the user try again
        setOtp('');

        Alert.alert(
          'Beklenmeyen Hata',
          'Doğrulama işlemi sırasında beklenmeyen bir hata oluştu. Lütfen tekrar deneyin' +
          (canResend ? ' veya yeni bir kod isteyin.' : ` veya ${countdown} saniye sonra yeni bir kod isteyin.`),
          [{ text: 'Tamam' }]
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP resend by sending a request to /auth/user/create or forgot-password endpoint
  const handleResendOtp = async () => {
    // Only proceed if resend is allowed
    if (!canResend) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Check if this is a password reset flow
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const isPasswordReset = await AsyncStorage.getItem('password_reset_flow') === 'true' ||
                              params?.isPasswordReset === 'true';

      if (isPasswordReset) {

        // For password reset flow, use the forgot-password endpoint
        try {
          const response = await authService.forgotPassword(email);
        } catch (forgotError) {
          throw forgotError; // Re-throw to be caught by the outer try-catch
        }
      } else {

        // Create a mock user data if we don't have it in storage
        const mockUserData = {
          name: "User",
          surname: "Test",
          email: email,
          password: "Password123!",
          identityNumber: "12345678901",
          birthDate: "1990-01-01",
          mobilePhone: "5551234567"
        };

        try {
          // Always try to send to /auth/user/create first as requested
          const response = await apiClient.post('/auth/user/create', mockUserData);
        } catch (createError) {

          // If that fails, try to get user data from AsyncStorage
          try {
            const userData = await AsyncStorage.getItem('signupUserData');

            if (userData) {
              // If we have the user data from signup, use it to resend OTP
              const parsedUserData = JSON.parse(userData);

              // Send the same request as signup to create endpoint
              const response = await apiClient.post('/auth/user/create', parsedUserData);
            } else {
              // If we don't have the user data, try the regular resend methods

              try {
                // First try the resend endpoint
                const response = await authService.resendOtp(email);
              } catch (resendError) {
                // If resend fails, try the regular send endpoint
                const response = await authService.sendOtp(email);
              }
            }
          } catch (storageError) {
            // Fall back to regular resend methods
            try {
              // First try the resend endpoint
              const response = await authService.resendOtp(email);
            } catch (resendError) {
              // If resend fails, try the regular send endpoint
              const response = await authService.sendOtp(email);
            }
          }
        }
      }

      // Reset countdown to 180 seconds
      setCountdown(180);
      setCanResend(false);

      // Show alert to inform user
      Alert.alert(
        'OTP Kodu Gönderildi',
        'Yeni doğrulama kodu e-posta adresinize gönderildi.'
      );
    } catch (err: any) {
      // Handle errors
      if (err.response) {

        if (err.response.data && err.response.data.responseMessage) {
          setError(err.response.data.responseMessage);
        } else {
          setError(`Sunucu hatası: ${err.response.status}`);
        }
      } else if (err.request) {
        setError('Sunucudan yanıt alınamadı. Lütfen internet bağlantınızı kontrol edin.');
      } else if (err.message) {
        setError(`Kod gönderme hatası: ${err.message}`);
      } else {
        setError('Yeni kod gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Success Modal */}
      {showSuccessModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalIconContainer}>
              <Ionicons name="checkmark-circle" size={60} color="#10B981" />
            </View>
            <Text style={styles.modalTitle}>Doğrulama Başarılı</Text>
            <Text style={styles.modalMessage}>
              E-posta adresiniz başarıyla doğrulandı. {redirectCountdown} saniye içinde giriş sayfasına yönlendirileceksiniz.
            </Text>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => router.replace('/auth/login')}
            >
              <Text style={styles.modalButtonText}>Hemen Giriş Yap</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={{flex: 1, width: '100%', display: 'flex', flexDirection: 'column'}}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <ImageBackground
            source={require('../../assets/images/law-background.jpg')}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          >
            <View style={styles.mainContainer}>
              <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
              >
                <Animated.View
                  style={[
                    styles.formWrapper,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                  <View style={styles.formHeader}>
                    <Text style={styles.formTitle}>E-posta Doğrulama</Text>
                    <View style={styles.formTitleUnderline} />
                  </View>

                  <View style={styles.formContainer}>
                    {/* Info Message */}
                    {email ? (
                      <View style={styles.infoContainer}>
                        <Ionicons name="mail" size={24} color="#4A78B0" />
                        <Text style={styles.infoText}>
                          {email} adresine bir doğrulama kodu gönderdik. Lütfen e-postanızı kontrol edin ve kodu aşağıya girin. Kod büyük-küçük harfler ve rakamlar içerebilir.
                        </Text>
                      </View>
                    ) : (
                      <View style={styles.inputGroup}>
                        <View style={styles.labelContainer}>
                          <Ionicons
                            name="mail-outline"
                            size={18}
                            color={isDark ? '#94a3b8' : '#64748b'}
                            style={styles.inputIcon}
                          />
                          <ThemedText style={styles.label}>E-posta Adresiniz</ThemedText>
                        </View>
                        <View style={styles.inputWrapper}>
                          <TextInput
                            style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                            placeholder="E-posta adresinizi girin"
                            placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                            value={email}
                            onChangeText={setEmail}
                            keyboardType="email-address"
                            autoCapitalize="none"
                            returnKeyType="done"
                          />
                          <TouchableOpacity
                            style={styles.sendButton}
                            onPress={() => {
                              if (email && email.includes('@')) {
                                authService.sendOtp(email)
                                  .then(() => {
                                    // Reset countdown to 180 seconds
                                    setCountdown(180);
                                    setCanResend(false);
                                    Alert.alert('Başarılı', 'Doğrulama kodu e-posta adresinize gönderildi.');
                                  })
                                  .catch(err => {
                                    Alert.alert('Hata', 'Doğrulama kodu gönderilirken bir hata oluştu.');
                                  });
                              } else {
                                Alert.alert('Hata', 'Lütfen geçerli bir e-posta adresi girin.');
                              }
                            }}
                          >
                            <Text style={styles.sendButtonText}>Kod Gönder</Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    )}

                    {/* Error Message */}
                    {error ? (
                      <Animated.View style={styles.errorContainer}>
                        <Ionicons name="alert-circle" size={20} color="#EF4444" />
                        <Text style={styles.errorText}>{error}</Text>
                      </Animated.View>
                    ) : null}

                    {/* OTP Input */}
                    <View style={styles.inputGroup}>
                      <View style={styles.labelContainer}>
                        <Ionicons
                          name="key-outline"
                          size={18}
                          color={isDark ? '#94a3b8' : '#64748b'}
                          style={styles.inputIcon}
                        />
                        <ThemedText style={styles.label}>Doğrulama Kodu</ThemedText>
                      </View>
                      <View style={styles.inputWrapper}>
                        <TextInput
                          style={[styles.input, isDark ? styles.inputDark : styles.inputLight]}
                          placeholder="Doğrulama kodunu girin"
                          placeholderTextColor={isDark ? '#94a3b8' : '#94a3b8'}
                          value={otp}
                          onChangeText={handleOtpChange}
                          keyboardType="default"
                          autoCapitalize="characters"
                          maxLength={8}
                          returnKeyType="done"
                        />
                      </View>
                    </View>

                    {/* Countdown info text */}
                    {!canResend && countdown > 0 && (
                      <Text style={styles.resendInfoText}>
                        Yeni kod isteyebilmek için kalan süre: {countdown} saniye
                      </Text>
                    )}

                    {/* Combined Verify/Resend Button */}
                    <TouchableOpacity
                      style={[styles.button, isLoading && styles.buttonDisabled]}
                      onPress={canResend ? handleResendOtp : handleVerify}
                      disabled={isLoading || success}
                    >


                      {isLoading ? (
                        <View style={styles.loadingContainer}>
                          <Text style={styles.buttonText}>
                            {canResend ? 'Gönderiliyor' : 'Doğrulanıyor'}
                          </Text>
                          <View style={styles.loadingDots}>
                            <View style={styles.loadingDot} />
                            <View style={[styles.loadingDot, styles.loadingDotMiddle]} />
                            <View style={styles.loadingDot} />
                          </View>
                        </View>
                      ) : success ? (
                        <Text style={styles.buttonText}>Doğrulandı ✓</Text>
                      ) : canResend ? (
                        <View style={styles.buttonContentRow}>
                          <Ionicons
                            name="refresh-outline"
                            size={20}
                            color="#FFFFFF"
                            style={{ marginRight: 8 }}
                          />
                          <Text style={styles.buttonText}>Tekrar Gönder</Text>
                        </View>
                      ) : (
                        <Text style={styles.buttonText}>Doğrula</Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </Animated.View>
              </ScrollView>
            </View>
          </ImageBackground>
        </KeyboardAvoidingView>
        {width > 768 && (
          <View style={styles.footerContainer}>
            <Footer />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  footerContainer: {
    width: '100%',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingBottom: Platform.OS === 'web' ? 0 : 50,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  mainContainer: {
    flexDirection: 'column',
    position: 'relative',
    overflow: 'hidden',
    width: '90%',
    maxWidth: 500,
    borderRadius: 20,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
    marginVertical: Platform.OS === 'web' ? 0 : 20,
    backgroundColor: '#FFFFFF',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 30 : 20,
    paddingVertical: Platform.OS === 'web' ? 50 : 30,
    minHeight: Platform.OS === 'web' ? '100%' : 'auto',
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4A78B0',
    marginBottom: 8,
  },
  formTitleUnderline: {
    width: 40,
    height: 4,
    backgroundColor: '#4A78B0',
    borderRadius: 2,
  },
  formContainer: {
    width: '100%',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(74, 120, 176, 0.1)',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  infoText: {
    color: '#1E293B',
    marginLeft: 12,
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 20,
  },
  errorText: {
    color: '#EF4444',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    marginRight: 8,
    color: '#1E3A8A',
  },
  label: {
    fontWeight: '600',
    fontSize: 16,
    color: '#1E3A8A',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#CBD5E1',
    backgroundColor: '#FFFFFF',
    zIndex: 10,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'transparent',
    color: '#1E293B',
    zIndex: 20,
    letterSpacing: 2,
    fontWeight: '600',
  },
  inputLight: {
    color: '#1E293B',
  },
  inputDark: {
    color: '#1E293B',
  },
  resendInfoText: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 16,
    textAlign: 'center',
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 8,
  },
  resendButtonEnabled: {
    backgroundColor: '#4A78B0',
    borderWidth: 0,
  },
  resendButtonDisabled: {
    backgroundColor: '#F1F5F9',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  resendButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  resendButtonTextEnabled: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  resendButtonTextDisabled: {
    color: '#94A3B8',
  },
  button: {
    backgroundColor: '#4A78B0',
    height: 50,
    borderRadius: 25,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 4px 8px rgba(74, 120, 176, 0.3)' }
      : {
          shadowColor: '#4A78B0',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }
    ),
    elevation: 5,
    paddingHorizontal: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    marginLeft: 8,
    alignItems: 'center',
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
  loadingDotMiddle: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  buttonContentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButton: {
    backgroundColor: '#4A78B0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    position: 'absolute',
    right: 8,
    zIndex: 20,
  },
  sendButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.1,
          shadowRadius: 20,
        }
    ),
    elevation: 10,
  },
  modalIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E293B',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    color: '#64748B',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  modalButton: {
    backgroundColor: '#4A78B0',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  modalButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },

});
