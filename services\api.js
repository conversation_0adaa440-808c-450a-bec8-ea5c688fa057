import axios from 'react-native-axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '../utils/logger';
import tokenManager from '../utils/tokenManager';

// API temel URL'si
const API_URL = 'http://193.35.154.97:4244';

// Log API URL for debugging
console.log('Using API URL:', API_URL);

// Axios instance oluşturma
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// İstek gönderilmeden önce token ekleyen interceptor
apiClient.interceptors.request.use(
  async (config) => {
    // Check if token exists and add it to the request headers
    const token = await tokenManager.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Log DELETE requests
    if (config.method.toLowerCase() === 'delete') {
      logger.api(`DELETE Request: ${config.baseURL}${config.url}`);
    }

    // API isteğini log'a yazdır
    logger.api(`API İsteği: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);
    if (config.data) {
      logger.api('API İstek Verileri:', JSON.stringify(config.data, null, 2));
    }
    if (config.params) {
      logger.api('API İstek Parametreleri:', JSON.stringify(config.params, null, 2));
    }

    return config;
  },
  (error) => {
    logger.error('API İstek Hatası:', error);
    return Promise.reject(error);
  }
);

// 401 hatası durumunda otomatik logout için interceptor

// Yanıt alındığında hata kontrolü yapan interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Log DELETE responses
    if (response.config.method.toLowerCase() === 'delete') {
      logger.api(`DELETE Response (${response.status}): ${response.config.url}`);
    }

    // Başarılı yanıtı log'a yazdır
    logger.api(`API Yanıtı (${response.status}): ${response.config.method.toUpperCase()} ${response.config.url}`);
    if (response.data && typeof response.data === 'object') {
      // Veri çok büyükse kısalt
      const dataStr = JSON.stringify(response.data);
      if (dataStr.length > 1000) {
        logger.api('API Yanıt Verileri (kısaltılmış):', dataStr.substring(0, 1000) + '...');
      } else {
        logger.api('API Yanıt Verileri:', response.data);
      }
    }
    return response;
  },
  async (error) => {
    // Log DELETE errors
    if (error.config && error.config.method && error.config.method.toLowerCase() === 'delete') {
      logger.error(`DELETE Error (${error.response?.status || 'unknown'}): ${error.config.url}`);
    }

    // Hata yanıtını log'a yazdır
    logger.error('API Hatası:', error.message);
    if (error.response) {
      logger.error(`API Hata Yanıtı (${error.response.status}):`, error.response.data);
    }
    if (error.config) {
      logger.error('Hatalı İstek:', `${error.config.method.toUpperCase()} ${error.config.url}`);
    }

    // 401 Unauthorized hatası durumunda token'ı temizle ve login sayfasına yönlendir
    // Ancak OTP doğrulama ve bazı diğer özel endpoint'ler için bu işlemi yapma
    if (error.response && error.response.status === 401) {
      // Check if this is an OTP verification request or other excluded endpoints
      const isOtpVerification = error.config && (
        error.config.url.includes('/auth/user/verify-email') ||
        error.config.url.includes('/auth/user/send-otp') ||
        error.config.url.includes('/auth/user/resend-otp') ||
        error.config.url.includes('/auth/user/forgot-password/validate-otp') ||
        error.config.url.includes('/auth/user/forgot-password/send-otp')
      );

      // If this is an OTP verification request, don't redirect to login
      if (isOtpVerification) {
        logger.warn('OTP doğrulama hatası alındı, otomatik yönlendirme yapılmayacak.');
        return Promise.reject(error);
      }

      logger.warn('Oturum süresi doldu veya geçersiz token. Oturumdan çıkılıyor...');

      // Check if we're already on the login page to prevent redirect loops
      const isLoginPage = typeof window !== 'undefined' &&
                         (window.location.pathname.includes('/auth/login') ||
                          window.location.pathname === '/auth');

      // Also check if we're on the OTP verification or password reset pages
      const isOtpPage = typeof window !== 'undefined' && (
                       window.location.pathname.includes('/auth/verify-otp') ||
                       window.location.pathname.includes('/auth/reset-password') ||
                       window.location.pathname.includes('/auth/forgot-password')
                       );

      // Don't redirect if we're on the login page or OTP verification page
      if (!isLoginPage && !isOtpPage) {
        // Token'ı temizle
        await tokenManager.clearTokens();

        // Mobil cihazlarda global bir event yayınla
        if (typeof window !== 'undefined') {
          // Web için global event
          const logoutEvent = new Event('auth_logout');
          window.dispatchEvent(logoutEvent);

          // Redirect to login page directly for web
          if (!isLoginPage && !isOtpPage) {
            window.location.href = '/auth/login';
          }
        } else {
          // React Native için global state'i güncelle
          // Bu kısım AuthContext tarafından dinlenecek
          await AsyncStorage.setItem('auth_logout', 'true');
        }
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
