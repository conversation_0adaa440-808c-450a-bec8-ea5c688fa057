import { Tabs } from 'expo-router';
import React, { useContext, useState, useEffect } from 'react';
import { Platform, Dimensions } from 'react-native';
import { usePathname } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '@/utils/logger';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthContext } from '../_layout';
import AppLayout from '@/components/layout/AppLayout';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  // Removed logout from context as it's not needed here
  const [isWideScreen, setIsWideScreen] = useState(false);
  const pathname = usePathname();
  const [lastPath, setLastPath] = useState('');

  // Check if current screen uses FlatList to prevent ScrollView nesting
  const shouldDisableScrollView =
    pathname === '/' ||
    pathname === '/index' ||
    pathname === '/cases' ||
    pathname === '/clients' ||
    pathname === '/tasks' ||
    pathname === '/trials' ||
    pathname === '/lawbot' ||
    pathname.startsWith('/cases/') ||
    pathname.startsWith('/clients/') ||
    pathname.startsWith('/trials/');

  // Track navigation between tabs
  useEffect(() => {
    const trackNavigation = async () => {
      try {
        // Skip if pathname hasn't changed
        if (pathname === lastPath) return;

        console.log('🔄 Navigation detected:', { from: lastPath, to: pathname });

        // Get the last visited path
        let previousPath = '';
        if (Platform.OS === 'web') {
          previousPath = localStorage.getItem('lastVisitedPath') || '';
          console.log('📂 Previous path from localStorage:', previousPath);

          // Store current path
          localStorage.setItem('lastVisitedPath', pathname);
          console.log('💾 Stored new path:', pathname);

          // Set reset flags when navigating away from any tab to another tab
          if (previousPath && previousPath !== pathname) {
            console.log('🚀 Setting reset flags for navigation:', { from: previousPath, to: pathname });

            // Cases tab reset - check for exact path match
            if ((previousPath === '/cases' || previousPath === '/') && (pathname !== '/cases' && pathname !== '/')) {
              console.log('🏷️ Setting casesNeedsReset flag');
              logger.info('Navigating away from Dosyalar tab, setting reset flag');
              localStorage.setItem('casesNeedsReset', 'true');
            }

            // Clients tab reset
            if (previousPath === '/clients' && pathname !== '/clients') {
              console.log('🏷️ Setting kisilerNeedsReset flag');
              logger.info('Navigating away from Kişiler tab, setting reset flag');
              localStorage.setItem('kisilerNeedsReset', 'true');
            }

            // Tasks tab reset
            if (previousPath === '/tasks' && pathname !== '/tasks') {
              console.log('🏷️ Setting tasksNeedsReset flag');
              logger.info('Navigating away from Görevler tab, setting reset flag');
              localStorage.setItem('tasksNeedsReset', 'true');
            }

            // Trials tab reset
            if (previousPath === '/trials' && pathname !== '/trials') {
              console.log('🏷️ Setting trialsNeedsReset flag');
              logger.info('Navigating away from Duruşmalar tab, setting reset flag');
              localStorage.setItem('trialsNeedsReset', 'true');
            }
          }
        } else {
          // For mobile
          previousPath = await AsyncStorage.getItem('lastVisitedPath') || '';

          // Store current path
          await AsyncStorage.setItem('lastVisitedPath', pathname);

          // Set reset flags when navigating away from any tab to another tab
          if (previousPath && previousPath !== pathname) {
            // Cases tab reset - check for exact path match
            if ((previousPath === '/cases' || previousPath === '/') && (pathname !== '/cases' && pathname !== '/')) {
              logger.info('Mobile: Navigating away from Dosyalar tab, setting reset flag');
              await AsyncStorage.setItem('casesNeedsReset', 'true');
            }

            // Clients tab reset
            if (previousPath === '/clients' && pathname !== '/clients') {
              logger.info('Mobile: Navigating away from Kişiler tab, setting reset flag');
              await AsyncStorage.setItem('kisilerNeedsReset', 'true');
            }

            // Tasks tab reset
            if (previousPath === '/tasks' && pathname !== '/tasks') {
              logger.info('Mobile: Navigating away from Görevler tab, setting reset flag');
              await AsyncStorage.setItem('tasksNeedsReset', 'true');
            }

            // Trials tab reset
            if (previousPath === '/trials' && pathname !== '/trials') {
              logger.info('Mobile: Navigating away from Duruşmalar tab, setting reset flag');
              await AsyncStorage.setItem('trialsNeedsReset', 'true');
            }
          }
        }

        // Update last path state
        setLastPath(pathname);
      } catch (error) {
        logger.error('Error tracking navigation:', error);
      }
    };

    trackNavigation();
  }, [pathname]);

  // Ekran genişliğini kontrol et
  useEffect(() => {
    const checkScreenWidth = () => {
      const { width } = Dimensions.get('window');
      setIsWideScreen(Platform.OS === 'web' && width > 768);
    };

    checkScreenWidth();

    // Web için resize event listener ekle
    if (Platform.OS === 'web') {
      window.addEventListener('resize', checkScreenWidth);
      return () => window.removeEventListener('resize', checkScreenWidth);
    }
  }, []);

  // Mobil görünüm için tab bar, web görünümü için sidebar kullanılacak
  const tabBarOptions = {
    tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
    headerShown: false,
    tabBarButton: HapticTab,
    tabBarBackground: TabBarBackground,
    tabBarStyle: Platform.select({
      ios: {
        // Use a transparent background on iOS to show the blur effect
        position: 'absolute',
      },
      default: {},
    }),
    // Web ve geniş ekranlarda tab bar'ı gizle
    tabBarStyle: isWideScreen ? { display: 'none' } : undefined,
  };

  return (
    <AppLayout
      hideFooter={!isWideScreen}
      disableScrollView={shouldDisableScrollView}
    >
      <Tabs
        screenOptions={tabBarOptions}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Ana Sayfa',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="cases"
        options={{
          title: 'Dosyalar',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="folder.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="clients"
        options={{
          title: 'Kişiler',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.2.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="tasks"
        options={{
          title: 'Görevler',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="calendar" color={color} />,
        }}
      />
      <Tabs.Screen
        name="trials"
        options={{
          title: 'Duruşmalar',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="building.columns.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="lawbot"
        options={{
          title: 'Lawbot',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="bubble.left.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profil',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.crop.circle.fill" color={color} />,
        }}
      />
    </Tabs>
    </AppLayout>
  );
}
