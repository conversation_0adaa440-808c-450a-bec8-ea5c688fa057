import { StyleSheet, TouchableOpacity, View, ScrollView, ActivityIndicator, Alert } from 'react-native';
import React, { useState, useEffect } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import reminderService from '@/services/reminderService';

// Öncelik renkleri
const PRIORITY_COLORS = {
  'LOW': { bg: 'rgba(34, 197, 94, 0.1)', text: '#22C55E' },
  'MEDIUM': { bg: 'rgba(249, 115, 22, 0.1)', text: '#F97316' },
  'HIGH': { bg: 'rgba(239, 68, 68, 0.1)', text: '#EF4444' },
  'CRITICAL': { bg: 'rgba(124, 58, 237, 0.1)', text: '#7C3AED' },
};

// Öncelik çevirileri
const PRIORITY_TRANSLATIONS = {
  'LOW': 'DÜŞÜK',
  'MEDIUM': 'ORTA',
  'HIGH': 'YÜKSEK',
  'CRITICAL': 'KRİTİK',
};

export default function ReminderDetailScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { id } = useLocalSearchParams();

  // State
  const [reminder, setReminder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Hatırlatıcı bilgilerini API'den getir
  useEffect(() => {
    const fetchReminder = async () => {
      try {
        setLoading(true);
        setError('');

        // API'den hatırlatıcı detayını getir
        const data = await reminderService.getReminderById(id);
        setReminder(data);
      } catch (err) {
        console.error(`Error fetching reminder ${id}:`, err);
        setError('Hatırlatıcı bilgileri yüklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    };

    fetchReminder();
  }, [id]);

  // Hatırlatıcıyı sil
  const handleDelete = () => {
    Alert.alert(
      'Hatırlatıcıyı Sil',
      'Bu hatırlatıcıyı silmek istediğinize emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel'
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderService.deleteReminder(id);
              router.back();
            } catch (err) {
              console.error('Error deleting reminder:', err);
              Alert.alert('Hata', 'Hatırlatıcı silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Hatırlatıcıyı düzenle
  const handleEdit = () => {
    router.push(`/reminders/edit/${id}`);
  };

  // Tarih formatla
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Tekrarlama metni
  const getRepeatText = (hours) => {
    if (!hours || hours <= 0) return 'Tekrarlanmıyor';
    if (hours === 24) return 'Günlük';
    if (hours === 168) return 'Haftalık';
    if (hours === 720) return 'Aylık';
    return `${hours} saat`;
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Hatırlatıcı Detayı</ThemedText>
        <View style={{ width: 24 }} />
      </ThemedView>

      {loading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>Hatırlatıcı yükleniyor...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <ThemedText style={styles.errorText}>{error}</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={async () => {
              try {
                setLoading(true);
                setError('');
                const data = await reminderService.getReminderById(id);
                setReminder(data);
                setLoading(false);
              } catch (err) {
                console.error(`Error retrying fetch reminder ${id}:`, err);
                setError('Hatırlatıcı bilgileri yüklenirken bir hata oluştu.');
                setLoading(false);
              }
            }}
          >
            <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : !reminder ? (
        <ThemedView style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <ThemedText style={styles.errorText}>Hatırlatıcı bulunamadı.</ThemedText>
        </ThemedView>
      ) : (
        <ScrollView style={styles.scrollView}>
          {/* Başlık ve Öncelik */}
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.detailCard}>
            <View style={styles.titleContainer}>
              <ThemedText type="title">{reminder.title}</ThemedText>
              <View style={[
                styles.priorityBadge,
                { backgroundColor: PRIORITY_COLORS[reminder.priority]?.bg || PRIORITY_COLORS.MEDIUM.bg }
              ]}>
                <ThemedText style={[
                  styles.priorityText,
                  { color: PRIORITY_COLORS[reminder.priority]?.text || PRIORITY_COLORS.MEDIUM.text }
                ]}>
                  {PRIORITY_TRANSLATIONS[reminder.priority] || 'ORTA'}
                </ThemedText>
              </View>
            </View>
          </BlurView>

          {/* Açıklama */}
          {reminder.description && (
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.detailCard}>
              <ThemedText type="subtitle" style={styles.sectionTitle}>Açıklama</ThemedText>
              <ThemedText style={styles.description}>{reminder.description}</ThemedText>
            </BlurView>
          )}

          {/* Tarih ve Tekrarlama Bilgileri */}
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.detailCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>Hatırlatıcı Bilgileri</ThemedText>
            
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="calendar-outline" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.infoLabelText}>Tarih ve Saat</ThemedText>
              </View>
              <ThemedText style={styles.infoValue}>{formatDate(reminder.dueDate)}</ThemedText>
            </View>
            
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="repeat-outline" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.infoLabelText}>Tekrarlama</ThemedText>
              </View>
              <ThemedText style={styles.infoValue}>{getRepeatText(reminder.repeatIntervalInHours)}</ThemedText>
            </View>
            
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="time-outline" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.infoLabelText}>Oluşturulma Tarihi</ThemedText>
              </View>
              <ThemedText style={styles.infoValue}>{formatDate(reminder.createdAt)}</ThemedText>
            </View>
            
            {reminder.updatedAt && reminder.updatedAt !== reminder.createdAt && (
              <View style={styles.infoRow}>
                <View style={styles.infoLabel}>
                  <Ionicons name="refresh-outline" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                  <ThemedText style={styles.infoLabelText}>Son Güncelleme</ThemedText>
                </View>
                <ThemedText style={styles.infoValue}>{formatDate(reminder.updatedAt)}</ThemedText>
              </View>
            )}
          </BlurView>

          {/* Aksiyon Butonları */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
              <Ionicons name="create-outline" size={20} color="white" />
              <ThemedText style={styles.buttonText}>Düzenle</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
              <Ionicons name="trash-outline" size={20} color="white" />
              <ThemedText style={styles.buttonText}>Sil</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  detailCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  sectionTitle: {
    marginBottom: 12,
  },
  description: {
    lineHeight: 22,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  infoLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoLabelText: {
    marginLeft: 8,
  },
  infoValue: {
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
  },
  editButton: {
    flex: 1,
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  deleteButton: {
    flex: 1,
    backgroundColor: '#EF4444',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
