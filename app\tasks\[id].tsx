import { StyleSheet, ScrollView, TouchableOpacity, View, Alert, ActivityIndicator, Platform, Text, TextInput, KeyboardAvoidingView, FlatList } from 'react-native';
import React, { useState, useEffect } from 'react';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import taskService from '@/services/taskService';
import AddTaskModal from '@/components/tasks/AddTaskModal';
import { formatDate, formatRelativeDate } from '@/utils/dateUtils';
import { showSuccessMessage, showErrorMessage, showInfoMessage, showWarningMessage, showConfirmDialog } from '@/utils/flashMessageUtils';

// Öncelik renkleri
type PriorityLevel = 'DÜŞÜK' | 'ORTA' | 'YÜKSEK' | 'KRİTİK';

interface PriorityColor {
  bg: string;
  text: string;
}

// Use index signature to allow any string key
const PRIORITY_COLORS: Record<string, PriorityColor> = {
  'DÜŞÜK': { bg: 'rgba(16, 185, 129, 0.1)', text: '#10B981' },
  'ORTA': { bg: 'rgba(245, 158, 11, 0.1)', text: '#F59E0B' },
  'YÜKSEK': { bg: 'rgba(239, 68, 68, 0.1)', text: '#EF4444' },
  'KRİTİK': { bg: 'rgba(124, 58, 237, 0.1)', text: '#7C3AED' },
  // Add fallback for any other priority
  'default': { bg: 'rgba(156, 163, 175, 0.1)', text: '#6B7280' },
};

// Örnek görev verileri
const TASKS_DATA = [
  {
    id: '1',
    title: 'Smith Davası için Dilekçe Hazırla',
    description: 'Smith vs. Johnson davası için itiraz dilekçesi hazırlanacak.',
    dueDate: '2023-06-14T14:00:00',
    priority: 'YÜKSEK',
    type: 'GÖREV',
    completed: false,
    repeat: 0, // Tekrarlanmıyor
    relatedCase: '1',
    relatedClient: '1',
  },
  {
    id: '2',
    title: 'Williams Anlaşmasını İncele',
    description: 'Williams boşanma davası için anlaşma metnini incele ve müvekkile bilgi ver.',
    dueDate: '2023-06-16T10:00:00',
    priority: 'ORTA',
    type: 'GÖREV',
    completed: false,
    repeat: 0,
    relatedCase: '2',
    relatedClient: '2',
  },
  {
    id: '3',
    title: 'Martinez Duruşması',
    description: 'Martinez ceza davası duruşması.',
    dueDate: '2023-06-18T09:00:00',
    priority: 'KRİTİK',
    type: 'DURUŞMA',
    completed: false,
    repeat: 0,
    relatedCase: '5',
    relatedClient: '5',
    location: 'Adliye Binası, Duruşma Salonu 2C',
  },
  {
    id: '4',
    title: 'Thompson ile Müvekkil Görüşmesi',
    description: 'Thompson LLC şirket yetkilisi ile iflas davası hakkında görüşme.',
    dueDate: '2023-06-15T15:30:00',
    priority: 'ORTA',
    type: 'TOPLANTI',
    completed: false,
    repeat: 0,
    relatedCase: '4',
    relatedClient: '4',
    location: 'Ofis',
  },
  {
    id: '5',
    title: 'Haftalık Dava Dosyalarını Gözden Geçir',
    description: 'Tüm aktif dava dosyalarını gözden geçir ve güncellemeleri not et.',
    dueDate: '2023-06-17T11:00:00',
    priority: 'DÜŞÜK',
    type: 'GÖREV',
    completed: false,
    repeat: 168, // Haftalık (saat cinsinden)
    relatedCase: null,
    relatedClient: null,
  },
  {
    id: '6',
    title: 'Davis Miras Davası Belgeleri Teslim Tarihi',
    description: 'Davis miras davası için gerekli belgelerin son teslim tarihi.',
    dueDate: '2023-06-20T17:00:00',
    priority: 'YÜKSEK',
    type: 'HATIRLATMA',
    completed: false,
    repeat: 0,
    relatedCase: '3',
    relatedClient: '3',
  },
];

// Örnek müvekkil ve dava verileri
const CLIENTS_DATA = [
  { id: '1', name: 'John Smith' },
  { id: '2', name: 'Sarah Williams' },
  { id: '3', name: 'Michael Davis' },
  { id: '4', name: 'Thompson LLC' },
  { id: '5', name: 'Carlos Martinez' },
];

const CASES_DATA = [
  { id: '1', title: 'Smith - Johnson Davası' },
  { id: '2', title: 'Williams Boşanma Davası' },
  { id: '3', title: 'Davis Miras Davası' },
  { id: '4', title: 'Thompson LLC İflas Davası' },
  { id: '5', title: 'Martinez Ceza Davası' },
];

export default function TaskDetailScreen() {
  const params = useLocalSearchParams();
  const id = params.id;
  console.log('Task ID from URL params:', id, typeof id);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Define Task type to fix TypeScript errors
  interface Task {
    id: number | string;
    title: string;
    description?: string;
    priority: string; // Changed from PriorityLevel to string for compatibility
    status?: string;  // Made optional for compatibility with mock data
    completed: boolean;
    dueDate: string;
    startDate?: string;
    type: string;
    location?: string;
    repeat?: number;
    relatedCase?: string | number | null;
    relatedClient?: string | number | null;
    caseData?: any;
    clientData?: any;
    // Additional fields from API
    createdAt?: string | number;
    updatedAt?: string | number;
    completedAt?: string | number;
    assignedTo?: string;
    createdBy?: string;
  }

  interface Note {
    id: number | string;
    content: string;
    createdAt: string;
  }

  // State
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCompleted, setIsCompleted] = useState(false);

  // Notes tab state
  const [activeTab, setActiveTab] = useState('bilgiler'); // 'bilgiler' or 'notlar'
  const [notes, setNotes] = useState<Note[]>([]);
  const [loadingNotes, setLoadingNotes] = useState(false);
  const [addingNote, setAddingNote] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<number | null>(null);

  // Note modal state
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);
  const [noteTitle, setNoteTitle] = useState('');
  const [noteContent, setNoteContent] = useState('');

  // Edit task modal state
  const [showEditTaskModal, setShowEditTaskModal] = useState(false);

  // Görev bilgilerini API'den getir
  // Görev notlarını yenile (artık task detaylarından alınıyor)
  const refreshTaskWithNotes = async () => {
    if (!id) return;

    try {
      setLoadingNotes(true);
      // Görev detaylarını yeniden getir (notları da içerecek)
      const data = await taskService.getTaskById(id);

      // Notları güncelle
      if (data && data.notes) {
        setNotes(data.notes || []);
      } else {
        setNotes([]);
      }
    } catch (err) {
      console.error(`Error refreshing task with notes ${id}:`, err);
      // Hata durumunda boş dizi kullan
      setNotes([]);
    } finally {
      setLoadingNotes(false);
    }
  };



  // Not ekle veya düzenle
  const handleAddNote = async () => {
    if (!noteContent.trim() || addingNote) return;

    try {
      setAddingNote(true);

      // Show info message
      showInfoMessage(
        'İşlem Yapılıyor',
        editingNoteId ? 'Not güncelleniyor...' : 'Not ekleniyor...'
      );

      // Başlık ve içeriği birleştir
      let fullContent = noteContent.trim();
      if (noteTitle.trim()) {
        fullContent = `${noteTitle.trim()}\n\n${noteContent.trim()}`;
      }

      // Sadece content içeren bir obje gönder
      const noteData = { content: fullContent };

      if (editingNoteId) {
        // Not düzenleme - PUT isteği
        const token = await AsyncStorage.getItem('auth_token');
        const response = await fetch(`http://193.35.154.97:4244/api/tasks/notes/${editingNoteId}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(noteData)
        });

        if (response.ok) {
          console.log(`Note ${editingNoteId} updated successfully`);
          // Başarılı mesajı göster
          showSuccessMessage(
            'Başarılı',
            'Not başarıyla güncellendi.'
          );
        } else {
          console.error(`Failed to update note: ${response.status}`);
          // Hata mesajı göster
          showErrorMessage(
            'Hata',
            'Not güncellenirken bir hata oluştu. Lütfen tekrar deneyin.'
          );
        }
      } else {
        // Yeni not ekleme
        try {
          await taskService.createNoteForTask(id, noteData);
          console.log('Not başarıyla eklendi');
          // Başarılı mesajı göster
          showSuccessMessage(
            'Başarılı',
            'Not başarıyla eklendi.'
          );
        } catch (error) {
          console.error('Error adding note:', error);
          // Hata mesajı göster
          showErrorMessage(
            'Hata',
            'Not eklenirken bir hata oluştu. Lütfen tekrar deneyin.'
          );
        }
      }

      // Modal'ı kapat ve input'ları temizle
      setShowAddNoteModal(false);
      setNoteTitle('');
      setNoteContent('');
      setEditingNoteId(null);

      // Görev detaylarını yeniden getir (güncel notları almak için)
      await refreshTaskWithNotes();
    } catch (err) {
      console.error(`Error ${editingNoteId ? 'updating' : 'adding'} note:`, err);
      // Show error message
      showErrorMessage(
        'Hata',
        editingNoteId ? 'Not güncellenirken bir hata oluştu.' : 'Not eklenirken bir hata oluştu.'
      );
    } finally {
      setAddingNote(false);
    }
  };

  useEffect(() => {
    const fetchTask = async () => {
      try {
        setLoading(true);
        setError('');

        // API'den görev detayını getir
        const data = await taskService.getTaskById(id);

        // API'den gelen verileri UI formatına dönüştür
        const formattedTask = {
          id: data.id,
          title: data.title,
          description: data.description || '',
          priority: taskService.mapPriorityFromApi(data.priority),
          status: data.status,
          completed: data.status === 'COMPLETED',
          dueDate: data.dueDate,
          startDate: data.startDate,
          type: data.type === 'TASK' ? 'GÖREV' :
                data.type === 'HEARING' ? 'DURUŞMA' :
                data.type === 'MEETING' ? 'TOPLANTI' : 'HATIRLATMA',
          location: data.location,
          repeat: data.repeat || 0,
          relatedCase: data.caseNumber,
          relatedClient: data.clientId,
          caseData: data.caseData || null,
          clientData: data.clientData || null
        };

        // Eğer dava ve müvekkil bilgileri yoksa, bunları getirmeyi dene
        if (data.caseNumber && !data.caseData) {
          try {
            const caseService = require('@/services/caseService').default;
            const caseData = await caseService.getCaseById(data.caseNumber);
            if (caseData) {
              formattedTask.caseData = caseData;
            }
          } catch (caseErr) {
            console.error(`Error fetching case data for task ${id}:`, caseErr);
          }
        }

        if (data.clientId && !data.clientData) {
          try {
            const clientService = require('@/services/clientService').default;
            const clientData = await clientService.getClientById(data.clientId);
            if (clientData) {
              formattedTask.clientData = clientData;
            }
          } catch (clientErr) {
            console.error(`Error fetching client data for task ${id}:`, clientErr);
          }
        }

        setTask(formattedTask);
        setIsCompleted(data.status === 'COMPLETED');

        // Notları da ayarla (API yanıtından)
        if (data.notes) {
          setNotes(data.notes);
        }
      } catch (err) {
        console.error(`Error fetching task ${id}:`, err);
        setError('Görev bilgileri yüklenirken bir hata oluştu.');

        // Geliştirme aşamasında örnek verileri kullan
        const fallbackTask = TASKS_DATA.find(t => t.id === id);
        if (fallbackTask) {
          setTask(fallbackTask);
          setIsCompleted(fallbackTask.completed);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchTask();
  }, [id]);

  // Notlar sekmesi aktif olduğunda notları yenile
  useEffect(() => {
    if (activeTab === 'notlar' && task) {
      // Notlar zaten task detaylarından geldiği için
      // sadece yeni not eklendiğinde yenileme yapılacak
      // Burada bir şey yapmaya gerek yok
    }
  }, [activeTab, task]);

  // Yükleme durumu
  if (loading) {
    return (
      <ThemedView style={styles.container}>
        {/* Header - Same as Dava Detayı */}
        <View style={styles.headerContainer}>
          <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => router.back()}
                >
                  <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <View style={styles.titleContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                  <Text style={styles.titleText}>AVAS</Text>
                  <Text style={styles.subtitleText}> | Görev Detayı</Text>
                </View>
              </View>
              <View style={styles.rightSection} />
            </View>
          </View>
        </View>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>Görev bilgileri yükleniyor...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  // Hata durumu
  if (error || !task) {
    return (
      <ThemedView style={styles.container}>
        {/* Header - Same as Dava Detayı */}
        <View style={styles.headerContainer}>
          <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => router.back()}
                >
                  <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <View style={styles.titleContainer}>
                  <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                  <Text style={styles.titleText}>AVAS</Text>
                  <Text style={styles.subtitleText}> | Görev Bulunamadı</Text>
                </View>
              </View>
              <View style={styles.rightSection} />
            </View>
          </View>
        </View>
        <ThemedView style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} style={{marginBottom: 16}} />
          <ThemedText style={{fontSize: 16, marginBottom: 12}}>{error || 'Görev bilgileri bulunamadı.'}</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setLoading(true);
              taskService.getTaskById(id)
                .then(data => {
                  setTask(data);
                  setIsCompleted(data.status === 'COMPLETED');
                  setError('');
                })
                .catch(err => {
                  console.error(`Error retrying fetch task ${id}:`, err);
                  setError('Görev bilgileri yüklenirken bir hata oluştu.');

                  // Geliştirme aşamasında örnek verileri kullan
                  const fallbackTask = TASKS_DATA.find(t => t.id === id);
                  if (fallbackTask) {
                    setTask(fallbackTask);
                    setIsCompleted(fallbackTask.completed);
                  }
                })
                .finally(() => setLoading(false));
            }}
          >
            <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    );
  }

  // Format the task dates using our utility function
  const formattedDueDate = formatDate(task.dueDate, true);
  const formattedStartDate = task.startDate ? formatDate(task.startDate, true) : null;

  const handleEdit = () => {
    // Düzenleme sayfasına yönlendir
    router.push(`/tasks/edit/${task.id}`);
  };

  const getTaskTypeIcon = () => {
    switch (task.type) {
      case 'GÖREV':
        return 'checkmark-circle';
      case 'DURUŞMA':
        return 'briefcase';
      case 'TOPLANTI':
        return 'people';
      case 'HATIRLATMA':
        return 'alarm';
      default:
        return 'checkmark-circle';
    }
  };

  const getRepeatText = () => {
    switch (task.repeat) {
      case 24:
        return 'Günlük';
      case 168:
        return 'Haftalık';
      case 720:
        return 'Aylık';
      default:
        return 'Tekrarlanmıyor';
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header - Same as Dava Detayı */}
      <View style={styles.headerContainer}>
        <View style={[styles.headerBackground, isDark ? styles.headerBackgroundDark : styles.headerBackgroundLight]}>
          <View style={styles.header}>
            <View style={styles.leftSection}>
              <TouchableOpacity
                style={styles.menuButton}
                onPress={() => router.back()}
              >
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <View style={styles.titleContainer}>
                <MaterialCommunityIcons name="scale-balance" size={24} color="#FFFFFF" style={styles.logo} />
                <Text style={styles.titleText}>AVAS</Text>
                <Text style={styles.subtitleText}> | Görev Detayı</Text>
              </View>
            </View>

            <View style={styles.rightSection}>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={handleEdit}
              >
                <Ionicons name="create-outline" size={22} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Task Icon and Title */}
        <View style={styles.pageIconContainer}>
          <Ionicons name="checkmark-circle-outline" size={24} color="#FFFFFF" />
        </View>
        <ThemedText style={styles.pageTitle}>Görev Detayı</ThemedText>

        {/* Tabs - Similar to Dava Detayı */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'bilgiler' && styles.activeTabButton]}
            onPress={() => setActiveTab('bilgiler')}
          >
            <ThemedText style={[
              styles.tabButtonText,
              activeTab === 'bilgiler' && styles.activeTabButtonText
            ]}>
              Bilgiler
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'notlar' && styles.activeTabButton]}
            onPress={() => setActiveTab('notlar')}
          >
            <ThemedText style={[
              styles.tabButtonText,
              activeTab === 'notlar' && styles.activeTabButtonText
            ]}>
              Notlar
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Bilgiler Tab Content */}
        {activeTab === 'bilgiler' && (
          <>
            {/* Task Header Card - Similar to Dava Detayı */}
            <View style={styles.taskInfoCard}>
              <View style={styles.taskHeaderRow}>
                <ThemedText style={styles.taskHeaderTitle}>
                  {task.title}
                </ThemedText>
                <View style={[
                  styles.priorityBadge,
                  { backgroundColor: (PRIORITY_COLORS[task.priority] || PRIORITY_COLORS['default']).bg }
                ]}>
                  <ThemedText style={[
                    styles.priorityText,
                    { color: (PRIORITY_COLORS[task.priority] || PRIORITY_COLORS['default']).text }
                  ]}>
                    {task.priority}
                  </ThemedText>
                </View>
              </View>

              {/* Task Info Rows */}
              <View style={styles.infoGrid}>
                <View style={styles.infoColumn}>
                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Görev No:</ThemedText>
                    <ThemedText style={styles.infoValue}>{task.id}</ThemedText>
                  </View>

                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Durum:</ThemedText>
                    <View style={styles.statusContainer}>
                      <View style={[styles.statusIndicator, {backgroundColor: isCompleted ? '#4CAF50' : '#F59E0B'}]} />
                      <ThemedText style={styles.infoValue}>{isCompleted ? 'Tamamlandı' : 'Açık'}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Görev Tipi:</ThemedText>
                    <View style={styles.taskTypeRow}>
                      <Ionicons
                        name={getTaskTypeIcon()}
                        size={16}
                        color={task.type === 'GÖREV' ? '#10B981' :
                               task.type === 'DURUŞMA' ? '#3B82F6' :
                               task.type === 'TOPLANTI' ? '#F59E0B' : '#8B5CF6'}
                        style={{marginRight: 4}}
                      />
                      <ThemedText style={styles.infoValue}>{task.type}</ThemedText>
                    </View>
                  </View>
                </View>

                <View style={styles.infoColumn}>
                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Bitiş Tarihi:</ThemedText>
                    <ThemedText style={styles.infoValue}>
                      {formattedDueDate}
                    </ThemedText>
                  </View>

                  {task.startDate && (
                    <View style={styles.infoItem}>
                      <ThemedText style={styles.infoLabel}>Başlangıç Tarihi:</ThemedText>
                      <ThemedText style={styles.infoValue}>
                        {formattedStartDate}
                      </ThemedText>
                    </View>
                  )}

                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Tekrarlama:</ThemedText>
                    <ThemedText style={styles.infoValue}>{getRepeatText()}</ThemedText>
                  </View>
                </View>
              </View>

              <View style={styles.infoSeparator} />

              <View style={styles.infoRow}>
                <View style={styles.infoItem}>
                  <ThemedText style={styles.infoLabel}>Öncelik:</ThemedText>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <View style={[
                      {
                        width: 8,
                        height: 8,
                        borderRadius: 4,
                        marginRight: 6
                      },
                      { backgroundColor: (PRIORITY_COLORS[task.priority] || PRIORITY_COLORS['default']).bg }
                    ]} />
                    <ThemedText style={styles.infoValue}>{task.priority}</ThemedText>
                  </View>
                </View>

                {task.status && (
                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Durum Kodu:</ThemedText>
                    <ThemedText style={styles.infoValue}>{task.status}</ThemedText>
                  </View>
                )}
              </View>

              {task.location && (
                <>
                  <View style={styles.infoSeparator} />
                  <View style={styles.infoItem}>
                    <ThemedText style={styles.infoLabel}>Konum:</ThemedText>
                    <ThemedText style={styles.infoValue}>{task.location}</ThemedText>
                  </View>
                </>
              )}

              {/* Related Items */}
              {(task.relatedCase || task.relatedClient) && (
                <>
                  <View style={styles.infoSeparator} />
                  <ThemedText style={styles.sectionSubtitle}>İlgili Kayıtlar</ThemedText>

                  {task.relatedCase && (
                    <View style={styles.relatedItemContainer}>
                      <View style={styles.relatedItemIconContainer}>
                        <Ionicons name="folder-outline" size={18} color={Colors[colorScheme ?? 'light'].tint} />
                      </View>
                      <TouchableOpacity
                        style={styles.relatedItemButton}
                        onPress={() => router.push(`/cases/${task.relatedCase}`)}
                      >
                        <ThemedText style={styles.relatedItemText}>
                          {task.caseData ?
                            (task.caseData.dosyaNo || task.caseData.title || `Dava #${task.relatedCase}`) :
                            CASES_DATA.find(c => c.id === task.relatedCase)?.title || `Dava #${task.relatedCase}`}
                        </ThemedText>
                        <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                      </TouchableOpacity>
                    </View>
                  )}

                  {task.relatedClient && (
                    <View style={styles.relatedItemContainer}>
                      <View style={styles.relatedItemIconContainer}>
                        <Ionicons name="person-outline" size={18} color={Colors[colorScheme ?? 'light'].tint} />
                      </View>
                      <TouchableOpacity
                        style={styles.relatedItemButton}
                        onPress={() => router.push(`/clients/${task.relatedClient}`)}
                      >
                        <ThemedText style={styles.relatedItemText}>
                          {task.clientData ?
                            (task.clientData.name || task.clientData.fullName || `Kişi #${task.relatedClient}`) :
                            CLIENTS_DATA.find(c => c.id === task.relatedClient)?.name || `Kişi #${task.relatedClient}`}
                        </ThemedText>
                        <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                      </TouchableOpacity>
                    </View>
                  )}
                </>
              )}
            </View>

            {/* Detailed Information Section */}
            <View style={styles.detailedInfoSection}>
              <ThemedText style={styles.sectionTitle}>Detaylı Bilgiler</ThemedText>

              {task.description ? (
                <View style={styles.descriptionContainer}>
                  <ThemedText style={styles.description}>{task.description}</ThemedText>
                </View>
              ) : (
                <View style={styles.emptyDescriptionContainer}>
                  <ThemedText style={styles.emptyDescriptionText}>Açıklama bulunmuyor.</ThemedText>
                </View>
              )}

              {/* Additional Task Information */}
              <View style={styles.infoSeparator} />

              <View style={{marginTop: 16, padding: 8}}>
                <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12}}>
                  <ThemedText style={{fontSize: 16, fontWeight: 'bold'}}>Teknik Bilgiler</ThemedText>

                  {/* Test Popup Button */}
                  <TouchableOpacity
                    style={{
                      backgroundColor: Colors[colorScheme ?? 'light'].tint,
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 4,
                    }}
                    onPress={() => {
                      // Show a test flash message with buttons
                      showSuccessMessage(
                        'Congratulations',
                        'You\'ve just displayed this awesome Pop Up View',
                        [
                          {
                            text: 'First Button',
                            onPress: () => console.log('First button pressed')
                          },
                          {
                            text: 'Second Button',
                            onPress: () => console.log('Second button pressed')
                          },
                          {
                            text: 'Done',
                            onPress: () => console.log('Done button pressed')
                          }
                        ]
                      );
                    }}
                  >
                    <ThemedText style={{color: '#FFFFFF', fontSize: 12}}>Test Flash Mesaj</ThemedText>
                  </TouchableOpacity>
                </View>

                <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8}}>
                  <ThemedText style={{fontWeight: '500', width: '40%'}}>Oluşturulma Tarihi:</ThemedText>
                  <ThemedText style={{flex: 1}}>
                    {task.createdAt ? formatDate(task.createdAt, true) : 'Belirtilmemiş'}
                  </ThemedText>
                </View>

                <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8}}>
                  <ThemedText style={{fontWeight: '500', width: '40%'}}>Son Güncelleme:</ThemedText>
                  <ThemedText style={{flex: 1}}>
                    {task.updatedAt ? formatDate(task.updatedAt, true) : 'Belirtilmemiş'}
                  </ThemedText>
                </View>

                {task.assignedTo && (
                  <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8}}>
                    <ThemedText style={{fontWeight: '500', width: '40%'}}>Atanan Kişi:</ThemedText>
                    <ThemedText style={{flex: 1}}>{task.assignedTo}</ThemedText>
                  </View>
                )}

                {task.createdBy && (
                  <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8}}>
                    <ThemedText style={{fontWeight: '500', width: '40%'}}>Oluşturan:</ThemedText>
                    <ThemedText style={{flex: 1}}>{task.createdBy}</ThemedText>
                  </View>
                )}

                {task.completedAt && (
                  <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8}}>
                    <ThemedText style={{fontWeight: '500', width: '40%'}}>Tamamlanma Tarihi:</ThemedText>
                    <ThemedText style={{flex: 1}}>
                      {formatDate(task.completedAt, true)}
                    </ThemedText>
                  </View>
                )}
              </View>
            </View>
          </>
        )}

        {/* Notlar Tab Content */}
        {activeTab === 'notlar' && (
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
            <View style={styles.sectionHeader}>
              <ThemedText type="subtitle">Görev Notları</ThemedText>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() => setShowAddNoteModal(true)}
              >
                <Ionicons name="add-circle" size={20} color="#FFFFFF" />
                <ThemedText style={styles.addButtonText}>Not Ekle</ThemedText>
              </TouchableOpacity>
            </View>

            {/* Notlar Listesi */}
            {loadingNotes ? (
              <View style={styles.notesLoadingContainer}>
                <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.notesLoadingText}>Notlar yükleniyor...</ThemedText>
              </View>
            ) : notes.length > 0 ? (
              <FlatList
                data={notes}
                keyExtractor={(item, index) => `note-${item.id || index}`}
                renderItem={({ item }) => (
                  <View style={styles.noteItem}>
                    <View style={styles.noteHeader}>
                      <ThemedText style={styles.noteTitle}>
                        {(() => {
                          // Extract title from content if it contains a newline
                          if (item.content && item.content.includes('\n\n')) {
                            const parts = item.content.split('\n\n');
                            return parts[0] || 'Not Başlığı';
                          }
                          return 'Not';
                        })()}
                      </ThemedText>
                      <View style={styles.noteActions}>
                        {/* Edit Note Button */}
                        <TouchableOpacity
                          style={styles.noteActionButton}
                          onPress={() => {
                            // Set the note content and title for editing
                            if (item.content && item.content.includes('\n\n')) {
                              const parts = item.content.split('\n\n');
                              setNoteTitle(parts[0] || '');
                              setNoteContent(parts.slice(1).join('\n\n') || item.content);
                            } else {
                              setNoteTitle('');
                              setNoteContent(item.content || '');
                            }

                            // Set the current note ID for editing
                            setEditingNoteId(typeof item.id === 'string' ? parseInt(item.id, 10) : item.id);

                            // Show the edit modal
                            setShowAddNoteModal(true);
                          }}
                        >
                          <Ionicons name="create-outline" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                        </TouchableOpacity>

                        {/* Delete Note Button */}
                        <TouchableOpacity
                          style={[styles.noteActionButton, {borderColor: '#EF4444'}]}
                          onPress={() => {
                            // Show confirmation dialog for note deletion
                            showConfirmDialog(
                              'Not Sil',
                              'Bu notu silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
                              () => {
                                // User confirmed, proceed with deletion
                                if (item.id) {
                                  // Get the token
                                  AsyncStorage.getItem('auth_token')
                                    .then(token => {
                                      // Make the DELETE request
                                      return fetch(`http://193.35.154.97:4244/api/tasks/notes/${item.id}`, {
                                        method: 'DELETE',
                                        headers: {
                                          'Authorization': `Bearer ${token}`,
                                          'Content-Type': 'application/json'
                                        }
                                      });
                                    })
                                    .then(response => {
                                      if (response.ok) {
                                        console.log(`Note ${item.id} deleted successfully`);
                                        // Refresh the notes list
                                        refreshTaskWithNotes();
                                        // Başarılı mesajı göster
                                        showSuccessMessage(
                                          'Başarılı',
                                          'Not başarıyla silindi.'
                                        );
                                      } else {
                                        console.error(`Failed to delete note: ${response.status}`);
                                        // Hata mesajı göster
                                        showErrorMessage(
                                          'Hata',
                                          'Not silinirken bir hata oluştu. Lütfen tekrar deneyin.'
                                        );
                                      }
                                    })
                                    .catch(error => {
                                      console.error('Error deleting note:', error);
                                    });
                                }
                              },
                              () => {
                                // User cancelled, do nothing
                                console.log('Note deletion cancelled');
                              },
                              {
                                iconName: 'trash',
                                iconColor: '#F44336',
                                confirmButtonText: 'Evet, Sil',
                                cancelButtonText: 'İptal',
                                cancelButtonColor: '#A9A9A9'
                              }
                            );
                          }}
                        >
                          <Ionicons name="trash-outline" size={16} color="#EF4444" />
                        </TouchableOpacity>

                        <ThemedText style={styles.noteDate}>
                          {formatDate(item.createdAt, false)}
                        </ThemedText>
                      </View>
                    </View>
                    <ThemedText style={styles.noteContent}>
                      {(() => {
                        // Extract content without title
                        if (item.content && item.content.includes('\n\n')) {
                          const parts = item.content.split('\n\n');
                          return parts.slice(1).join('\n\n') || item.content;
                        }
                        return item.content;
                      })()}
                    </ThemedText>
                  </View>
                )}
                scrollEnabled={false}
              />
            ) : (
              <ThemedText style={styles.emptyText}>Not bulunamadı</ThemedText>
            )}
          </BlurView>
        )}

        {/* Action Buttons - Minimal Style */}
        {activeTab === 'bilgiler' && (
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={styles.minimalEditButton}
              onPress={() => setShowEditTaskModal(true)}
            >
              <Ionicons name="create-outline" size={20} color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.minimalButtonText}>Düzenle</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.minimalStatusButton,
                { borderColor: isCompleted ? Colors[colorScheme ?? 'light'].tint : '#4CAF50' }
              ]}
              onPress={async () => {
                try {
                  // Show info message
                  showInfoMessage(
                    'İşlem Yapılıyor',
                    'Görev durumu güncelleniyor...'
                  );

                  // Durumu tersine çevir
                  const newStatus = isCompleted ? 'OPEN' : 'COMPLETED';

                  // API'ye gönderilecek güncelleme verisi
                  const updateData = {
                    ...task,
                    status: newStatus,
                    priority: taskService.mapPriorityToApi(task.priority),
                    type: task.type === 'GÖREV' ? 'TASK' :
                          task.type === 'DURUŞMA' ? 'HEARING' :
                          task.type === 'TOPLANTI' ? 'MEETING' : 'REMINDER'
                  };

                  // API'ye güncelleme isteği gönder
                  await taskService.updateTask(id, updateData);

                  // Başarılı olursa yerel state'i güncelle
                  setIsCompleted(!isCompleted);
                  console.log('Görev durumu değiştirildi:', task.id, !isCompleted);

                  // Başarılı mesajı göster
                  showSuccessMessage(
                    'Başarılı',
                    isCompleted ? 'Görev başarıyla tekrar açıldı.' : 'Görev başarıyla tamamlandı.'
                  );
                } catch (err) {
                  console.error(`Error updating task ${id}:`, err);
                  // Show error message
                  showErrorMessage(
                    'Hata',
                    'Görev durumu güncellenirken bir hata oluştu. Lütfen tekrar deneyin.'
                  );
                }
              }}
            >
              <Ionicons
                name={isCompleted ? "refresh-outline" : "checkmark-circle-outline"}
                size={20}
                color={isCompleted ? Colors[colorScheme ?? 'light'].tint : "#4CAF50"}
              />
              <ThemedText style={[
                styles.minimalButtonText,
                { color: isCompleted ? Colors[colorScheme ?? 'light'].tint : "#4CAF50" }
              ]}>
                {isCompleted ? "Tekrar Aç" : "Tamamla"}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.minimalDeleteButton}
              onPress={() => {
                if (id) {
                  // Show confirmation dialog
                  showConfirmDialog(
                    'Görevi Sil',
                    'Bu görevi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
                    async () => {
                      // User confirmed, proceed with deletion
                      try {
                        const token = await AsyncStorage.getItem('auth_token');
                        const response = await fetch(`http://193.35.154.97:4244/api/tasks/${id}`, {
                          method: 'DELETE',
                          headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                          }
                        });

                        if (response.ok) {
                          // Başarılı mesajı göster
                          showSuccessMessage(
                            'Başarılı',
                            'Görev başarıyla silindi.',
                            [
                              {
                                text: 'Tamam',
                                onPress: () => router.back()
                              }
                            ]
                          );
                        } else {
                          console.error(`Error deleting task: ${response.status}`);
                          // Hata mesajı göster
                          showErrorMessage(
                            'Hata',
                            'Görev silinirken bir hata oluştu. Lütfen tekrar deneyin.'
                          );
                        }
                      } catch (error) {
                        console.error('Delete error:', error);
                      }
                    },
                    () => {
                      // User cancelled, do nothing
                      console.log('Task deletion cancelled');
                    },
                    {
                      iconName: 'trash',
                      iconColor: '#F44336',
                      confirmButtonText: 'Evet, Sil',
                      cancelButtonText: 'İptal',
                      cancelButtonColor: '#A9A9A9'
                    }
                  );
                }
              }}
            >
              <Ionicons name="trash-outline" size={20} color="#EF4444" />
              <ThemedText style={[styles.minimalButtonText, {color: '#EF4444'}]}>Sil</ThemedText>
            </TouchableOpacity>
          </View>
        )}



        {/* Add Note Modal */}
        {showAddNoteModal && (
          <View style={[styles.modalOverlay, { backgroundColor: 'transparent' }]}>
            <View
              style={[
                styles.modalContainer,
                { backgroundColor: '#FFFFFF' }
              ]}
            >
              <View style={[
                styles.modalHeader,
                { backgroundColor: '#FFFFFF', borderBottomColor: '#EEEEEE' }
              ]}>
                <ThemedText type="subtitle" style={{ fontWeight: '600', color: '#333333' }}>
                  {editingNoteId ? 'Not Düzenle' : 'Not Ekle'}
                </ThemedText>
                <TouchableOpacity
                  onPress={() => {
                    setShowAddNoteModal(false);
                    setNoteContent('');
                    setNoteTitle('');
                    setEditingNoteId(null);
                  }}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color="#333333" />
                </TouchableOpacity>
              </View>

              <View style={styles.modalContent}>
                <ThemedText style={styles.modalLabel}>Not Başlığı</ThemedText>
                <TextInput
                  style={[
                    styles.titleInput,
                    { backgroundColor: '#FFFFFF', color: '#333333', borderColor: '#DDDDDD' }
                  ]}
                  placeholder="Not başlığını giriniz..."
                  placeholderTextColor="rgba(0, 0, 0, 0.4)"
                  value={noteTitle}
                  onChangeText={setNoteTitle}
                />

                <ThemedText style={styles.modalLabel}>Not İçeriği</ThemedText>
                <TextInput
                  style={[
                    styles.noteInput,
                    { backgroundColor: '#FFFFFF', color: '#333333', borderColor: '#DDDDDD' }
                  ]}
                  placeholder="Not içeriğini giriniz..."
                  placeholderTextColor="rgba(0, 0, 0, 0.4)"
                  multiline
                  numberOfLines={5}
                  textAlignVertical="top"
                  value={noteContent}
                  onChangeText={setNoteContent}
                />

                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={[
                      styles.modalButton,
                      styles.cancelButton,
                      { backgroundColor: '#F0F0F0' }
                    ]}
                    onPress={() => {
                      setShowAddNoteModal(false);
                      setNoteContent('');
                      setNoteTitle('');
                      setEditingNoteId(null);
                    }}
                  >
                    <ThemedText style={[styles.cancelButtonText, { color: '#333333' }]}>İptal</ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.modalButton,
                      styles.saveButton,
                      !noteContent.trim() && styles.disabledButton
                    ]}
                    onPress={handleAddNote}
                    disabled={!noteContent.trim() || addingNote}
                  >
                    {addingNote ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <ThemedText style={styles.saveButtonText}>Kaydet</ThemedText>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Edit Task Modal */}
        {task && (
          <AddTaskModal
            visible={showEditTaskModal}
            onClose={() => setShowEditTaskModal(false)}
            onSuccess={async () => {
              // Görev detaylarını yeniden getir
              try {
                const updatedTask = await taskService.getTaskById(id);
                setTask(updatedTask);
                setIsCompleted(updatedTask.status === 'COMPLETED');
              } catch (err) {
                console.error(`Error refreshing task ${id}:`, err);
              }
            }}
             task={{...task, relatedCase: task.relatedCase?.toString()}}
  isEditing={true}
          />
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerContainer: {
    width: '100%',
    zIndex: 10,
  },
  headerBackground: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerBackgroundLight: {
    backgroundColor: '#4A78B0',
  },
  headerBackgroundDark: {
    backgroundColor: '#2C4A6B',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'web' ? 12 : 60,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitleText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  logo: {
    marginRight: 8,
  },
  menuButton: {
    marginRight: 16,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  pageIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.tint,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    alignSelf: 'center',
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.tint,
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  activeTabButtonText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  taskInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 12,
  },
  taskHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  infoGrid: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoColumn: {
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoItem: {
    marginBottom: 12,
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  taskTypeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoSeparator: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 12,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  relatedItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  relatedItemIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  relatedItemButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  relatedItemText: {
    color: Colors.light.tint,
    fontSize: 14,
  },
  detailedInfoSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  descriptionContainer: {
    paddingVertical: 8,
  },
  description: {
    lineHeight: 22,
    fontSize: 14,
  },
  emptyDescriptionContainer: {
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  emptyDescriptionText: {
    color: '#64748b',
    fontSize: 14,
    textAlign: 'center',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 16,
    gap: 12,
  },
  // Minimal style buttons (similar to dosya detayları page)
  minimalEditButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.tint,
  },
  minimalDeleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  minimalStatusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 8,
  },
  minimalButtonText: {
    marginLeft: 4,
    fontWeight: '600',
    fontSize: 14,
    color: Colors.light.tint,
  },
  editButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: Colors.light.tint,
    borderRadius: 8,
    gap: 8,
    backgroundColor: '#fff',
  },
  editButtonText: {
    fontWeight: '600',
    color: Colors.light.tint,
    fontSize: 14,
  },
  completeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#4CAF50',
    borderRadius: 8,
    gap: 8,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  reopenButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: Colors.light.tint,
    borderRadius: 8,
    gap: 8,
    backgroundColor: '#fff',
  },
  completeButtonText: {
    fontWeight: '600',
    color: '#4CAF50',
    fontSize: 14,
  },
  reopenButtonText: {
    fontWeight: '600',
    color: Colors.light.tint,
    fontSize: 14,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#EF4444',
    borderRadius: 8,
    gap: 8,
    marginBottom: 16,
    backgroundColor: '#fff',
  },
  deleteButtonText: {
    color: '#EF4444',
    fontWeight: '600',
    fontSize: 14,
  },

  // New delete button styles
  newDeleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 8,
    marginBottom: 16,
    backgroundColor: '#EF4444',
  },
  newDeleteButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },

  // Content Card
  contentCard: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#FFFFFF',
    marginLeft: 6,
    fontWeight: '600',
    fontSize: 14,
  },
  // Notes Tab Styles
  notesLoadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notesLoadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#64748b',
  },
  noteItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  noteTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.tint,
    marginBottom: 4,
    flex: 1,
  },
  noteActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteActionButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: Colors.light.tint,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  noteDeleteButton: {
    padding: 5,
    marginRight: 10,
  },
  noteDate: {
    fontSize: 12,
    color: '#9ca3af',
  },
  noteContent: {
    lineHeight: 20,
    marginTop: 4,
  },
  emptyText: {
    textAlign: 'center',
    color: '#9ca3af',
    marginVertical: 20,
  },
  // Modal Styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    width: '90%',
    maxWidth: 500,
    borderRadius: 8,
    overflow: 'hidden',
    padding: 20,
    backgroundColor: '#FFFFFF',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 5,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    marginHorizontal: -10,
    paddingHorizontal: 10,
    backgroundColor: '#FFFFFF',
  },
  closeButton: {
    padding: 5,
  },
  modalContent: {
    width: '100%',
  },
  modalLabel: {
    marginBottom: 12,
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  titleInput: {
    borderWidth: 1,
    borderColor: 'rgba(150, 150, 150, 0.3)',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: 'rgba(150, 150, 150, 0.3)',
    borderRadius: 8,
    padding: 12,
    minHeight: 120,
    fontSize: 16,
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 15,
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#F0F0F0',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333333',
  },
  saveButton: {
    backgroundColor: Colors.light.tint,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  disabledButton: {
    opacity: 0.5,
  },
});
