import React, { useState } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, TextInput, View } from 'react-native';
import { router } from 'expo-router';
import { Picker } from '@react-native-picker/picker';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function AddCaseScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [caseTitle, setCaseTitle] = useState('');
  const [caseNumber, setCaseNumber] = useState('');
  const [caseType, setCaseType] = useState('Civil');
  const [crimeType, setCrimeType] = useState('');
  const [caseValue, setCaseValue] = useState('');
  const [client, setClient] = useState('');
  const [notes, setNotes] = useState('');
  
  const handleSave = () => {
    // In a real app, you would save the case data to your backend
    // For now, we'll just navigate back to the cases screen
    router.replace('/(tabs)/cases');
  };
  
  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol size={24} name="chevron.left" color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Add New Case</ThemedText>
      </ThemedView>
      
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.scrollContent}>
        <ThemedView style={styles.formSection}>
          <ThemedText style={styles.sectionTitle}>Case Information</ThemedText>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Case Title *</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={caseTitle}
              onChangeText={setCaseTitle}
              placeholder="Enter case title"
              placeholderTextColor="#999"
            />
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Case Number *</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={caseNumber}
              onChangeText={setCaseNumber}
              placeholder="Enter case number"
              placeholderTextColor="#999"
            />
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Case Type *</ThemedText>
            <View style={[styles.pickerContainer, isDark && styles.pickerContainerDark]}>
              <Picker
                selectedValue={caseType}
                onValueChange={(itemValue) => setCaseType(itemValue)}
                style={[styles.picker, isDark && styles.pickerDark]}
                dropdownIconColor={isDark ? '#fff' : '#000'}
              >
                <Picker.Item label="Civil" value="Civil" />
                <Picker.Item label="Criminal" value="Criminal" />
                <Picker.Item label="Family" value="Family" />
                <Picker.Item label="Probate" value="Probate" />
                <Picker.Item label="Bankruptcy" value="Bankruptcy" />
                <Picker.Item label="Other" value="Other" />
              </Picker>
            </View>
          </ThemedView>
          
          {caseType === 'Criminal' && (
            <ThemedView style={styles.inputGroup}>
              <ThemedText style={styles.label}>Crime Type</ThemedText>
              <TextInput
                style={[styles.input, isDark && styles.inputDark]}
                value={crimeType}
                onChangeText={setCrimeType}
                placeholder="Enter crime type"
                placeholderTextColor="#999"
              />
            </ThemedView>
          )}
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Case Value</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={caseValue}
              onChangeText={setCaseValue}
              placeholder="Enter case value (if applicable)"
              placeholderTextColor="#999"
              keyboardType="numeric"
            />
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Client *</ThemedText>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={client}
              onChangeText={setClient}
              placeholder="Enter client name"
              placeholderTextColor="#999"
            />
          </ThemedView>
        </ThemedView>
        
        <ThemedView style={styles.formSection}>
          <ThemedText style={styles.sectionTitle}>Notes</ThemedText>
          <TextInput
            style={[styles.notesInput, isDark && styles.notesInputDark]}
            multiline
            value={notes}
            onChangeText={setNotes}
            placeholder="Add case notes here..."
            placeholderTextColor="#999"
            textAlignVertical="top"
          />
        </ThemedView>
        
        <ThemedView style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <ThemedText style={styles.saveButtonText}>Save Case</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
    color: '#333',
  },
  inputDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
    color: '#fff',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  pickerContainerDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
  },
  picker: {
    height: 50,
    color: '#333',
  },
  pickerDark: {
    color: '#fff',
  },
  notesInput: {
    height: 150,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    backgroundColor: '#fff',
    color: '#333',
  },
  notesInputDark: {
    borderColor: '#4B5563',
    backgroundColor: '#374151',
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderColor: Colors.light.tint,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    height: 50,
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
