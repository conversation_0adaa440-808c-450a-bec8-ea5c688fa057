import React from 'react';
import {
  View,
  Modal,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Trial, TrialParty } from '@/services/trialService';

interface TrialDetailModalProps {
  trial: Trial | null;
  visible: boolean;
  onClose: () => void;
}

export default function TrialDetailModal({ trial, visible, onClose }: TrialDetailModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Tarih formatını düzenle
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);

      // Türkçe ay isimleri
      const aylar = [
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>lık'
      ];

      const gun = date.getDate();
      const ay = aylar[date.getMonth()];
      const yil = date.getFullYear();
      const saat = date.getHours().toString().padStart(2, '0');
      const dakika = date.getMinutes().toString().padStart(2, '0');

      return `${gun} ${ay} ${yil} ${saat}:${dakika}`;
    } catch (error) {
      return dateString;
    }
  };

  // Taraf sıfatına göre tag stili
  const getPartyTagStyle = (sifat: string) => {
    switch (sifat.toUpperCase()) {
      case 'DAVACI':
        return styles.davacıTag;
      case 'DAVALI':
        return styles.davalıTag;
      case 'ÇOCUK':
        return styles.cocukTag;
      case 'VEKİL':
      case 'AVUKAT':
        return styles.vekilTag;
      default:
        return styles.defaultTag;
    }
  };

  // Trial null ise veya visible false ise null dön
  if (!trial || !visible) return null;

  // Duruşma detay bölümlerini oluştur
  const sections = [
    {
      id: 'dosya',
      title: 'Dosya Bilgileri',
      content: [
        { label: 'Dosya No:', value: trial.dosyaNo },
        { label: 'Dosya Türü:', value: trial.dosyaTurKodAciklama },
        { label: 'Dosya ID:', value: trial.dosyaId }
      ]
    },
    {
      id: 'mahkeme',
      title: 'Mahkeme Bilgileri',
      content: [
        { label: 'Mahkeme:', value: trial.yerelBirimAd },
        { label: 'Birim ID:', value: trial.birimId },
        { label: 'Birim Org Kodu:', value: trial.birimOrgKodu }
      ]
    },
    {
      id: 'durusma',
      title: 'Duruşma Bilgileri',
      content: [
        { label: 'Tarih ve Saat:', value: formatDate(trial.tarihSaat) },
        { label: 'İşlem Türü:', value: trial.islemTuruAciklama },
        { label: 'İşlem Sonucu:', value: trial.islemSonucuAciklama },
        { label: 'Talep Durumu:', value: trial.talepDurumu },
        { label: 'E-Duruşma:', value: trial.isEDurusmaSaatTalepValid ? 'Mümkün' : 'Mümkün Değil' }
      ]
    },
    {
      id: 'diger',
      title: 'Diğer Bilgiler',
      content: [
        { label: 'Kayıt ID:', value: trial.kayitId },
        { label: 'Hakim/Heyet:', value: trial.hakimHeyet },
        { label: 'Katılım Butonu:', value: trial.katilButonAktifMi ? 'Aktif' : 'Pasif' }
      ]
    }
  ];

  // Taraf bilgilerini hazırla
  const davaci = trial.dosyaTaraflari?.filter(p => p.sifat === 'DAVACI') || [];
  const davali = trial.dosyaTaraflari?.filter(p => p.sifat === 'DAVALI') || [];
  const diger = trial.dosyaTaraflari?.filter(p => p.sifat !== 'DAVACI' && p.sifat !== 'DAVALI') || [];

  // Taraf özetlerini oluştur
  const partySummaries = [
    { id: 'davaci', title: 'Davacılar:', parties: davaci },
    { id: 'davali', title: 'Davalılar:', parties: davali },
    { id: 'diger', title: 'Diğer Taraflar:', count: diger.length }
  ];

  // Bölüm render fonksiyonu
  const renderSection = ({ item }) => (
    <View style={styles.section}>
      <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>{item.title}</ThemedText>
      {/* Replace FlatList with manual rendering to avoid nesting issues */}
      <View>
        {item.content.map((infoItem, index) => (
          <View key={`${infoItem.label}-${index}`} style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>{infoItem.label}</ThemedText>
            <ThemedText style={styles.infoValue}>{infoItem.value}</ThemedText>
          </View>
        ))}
      </View>
    </View>
  );

  // We've replaced the renderPartySummary function with inline rendering in the ListFooterComponent

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <BlurView intensity={isDark ? 70 : 90} tint="light" style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <ThemedText type="subtitle" style={styles.modalTitle}>Duruşma Detayları</ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={Colors[colorScheme ?? 'light'].text} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={sections}
            renderItem={renderSection}
            keyExtractor={item => item.id}
            style={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={
              <View style={styles.modalContent}>
                {/* Boş header */}
              </View>
            }
            ListFooterComponent={
              <View style={styles.section}>
                <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>Taraf Bilgileri</ThemedText>
                {trial.dosyaTaraflari && trial.dosyaTaraflari.length > 0 ? (
                  <View style={styles.partiesContainer}>
                    {/* Replace nested FlatList with manual rendering */}
                    {partySummaries.map(item => (
                      <View key={item.id} style={styles.partySection}>
                        <ThemedText style={styles.partySectionTitle}>{item.title}</ThemedText>
                        {item.parties ? (
                          item.parties.length > 0 ? (
                            <View>
                              {item.parties.map((party, index) => {
                                // Taraf sıfatına göre stil belirle
                                let tagStyle = styles.defaultTag;
                                if (party.sifat === 'DAVACI') tagStyle = styles.davacıTag;
                                else if (party.sifat === 'DAVALI') tagStyle = styles.davalıTag;
                                else if (party.sifat === 'ÇOCUK') tagStyle = styles.cocukTag;
                                else if (party.sifat === 'VEKİL' || party.sifat === 'AVUKAT') tagStyle = styles.vekilTag;

                                return (
                                  <View key={`${party.id || party.isim}-${index}`} style={styles.partyItemContainer}>
                                    <View style={[styles.partyTag, tagStyle]}>
                                      <ThemedText style={styles.partyTagText}>{party.sifat}</ThemedText>
                                    </View>
                                    <ThemedText style={styles.partyItem}>
                                      {party.isim} {party.soyad}
                                    </ThemedText>
                                  </View>
                                );
                              })}
                            </View>
                          ) : (
                            <ThemedText style={styles.emptyText}>Yok</ThemedText>
                          )
                        ) : (
                          <View>
                            {diger.map((party, index) => {
                              // Taraf sıfatına göre stil belirle
                              let tagStyle = styles.defaultTag;
                              if (party.sifat === 'ÇOCUK') tagStyle = styles.cocukTag;
                              else if (party.sifat === 'VEKİL' || party.sifat === 'AVUKAT') tagStyle = styles.vekilTag;

                              return (
                                <View key={`diger-${party.id || party.isim}-${index}`} style={styles.partyItemContainer}>
                                  <View style={[styles.partyTag, tagStyle]}>
                                    <ThemedText style={styles.partyTagText}>{party.sifat}</ThemedText>
                                  </View>
                                  <ThemedText style={styles.partyItem}>
                                    {party.isim} {party.soyad}
                                  </ThemedText>
                                </View>
                              );
                            })}
                          </View>
                        )}
                      </View>
                    ))}
                  </View>
                ) : (
                  <ThemedText style={styles.emptyText}>Taraf bilgisi bulunamadı</ThemedText>
                )}
              </View>
            }
          />
        </BlurView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxHeight: '90%',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  scrollContent: {
    flex: 1,
  },
  modalContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    paddingBottom: 8,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    width: 120,
    fontWeight: '600',
    opacity: 0.8,
  },
  infoValue: {
    flex: 1,
  },
  partiesContainer: {
    marginTop: 8,
  },
  partySection: {
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  partySectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
  },
  partyItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  partyTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  partyTagText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  partyItem: {
    fontSize: 14,
    flex: 1,
  },
  partyCount: {
    fontSize: 14,
    fontStyle: 'italic',
    paddingLeft: 8,
  },
  emptyText: {
    fontStyle: 'italic',
    opacity: 0.7,
  },
  davacıTag: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
  },
  davalıTag: {
    backgroundColor: 'rgba(239, 68, 68, 0.2)',
  },
  cocukTag: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
  },
  vekilTag: {
    backgroundColor: 'rgba(139, 92, 246, 0.2)',
  },
  defaultTag: {
    backgroundColor: 'rgba(107, 114, 128, 0.2)',
  },
});
