import React, { useState, useEffect } from 'react';
import { View, Modal, TouchableOpacity, StyleSheet, TextInput, Switch, Platform, ScrollView } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import caseService from '@/services/caseService';
import { CustomDropdown } from './CustomDropdown';

// Import JS version of scrollbar styles for CSS injection
import './scrollbar-styles.js';

interface AddCaseDetailModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (details: {
    caseType: string;
    crimeType: string;
    derdest: boolean;
    caseValue: string;
    caseReason: string;
    caseTitle: string;
  }) => void;
  loading?: boolean;
  existingData?: {
    id?: string;
    caseType?: string;
    crimeType?: string;
    derdest?: boolean;
    caseValue?: number | string;
    caseReason?: string;
    caseTitle?: string;
    acilisTarihi?: string; // Added opening date field
  };
}

// Type definitions for case and crime types
interface TypeOption {
  code: string;
  displayName: string;
}

interface TypesResponse {
  caseTypes: TypeOption[];
  crimeTypes: TypeOption[];
}

export default function AddCaseDetailModal({
  visible,
  onClose,
  onSave,
  loading = false,
  existingData
}: AddCaseDetailModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Form state
  const [caseType, setCaseType] = useState('');
  const [crimeType, setCrimeType] = useState('');
  const [derdest, setDerdest] = useState(true);
  const [caseValue, setCaseValue] = useState('');
  const [caseReason, setCaseReason] = useState('');
  const [caseTitle, setCaseTitle] = useState('');

  // Dropdown state
  const [showCaseTypeDropdown, setShowCaseTypeDropdown] = useState(false);
  const [showCrimeTypeDropdown, setShowCrimeTypeDropdown] = useState(false);

  // Selected labels for display
  const [caseTypeLabel, setCaseTypeLabel] = useState('');
  const [crimeTypeLabel, setCrimeTypeLabel] = useState('');

  // API data state
  const [caseTypes, setCaseTypes] = useState<TypeOption[]>([]);
  const [crimeTypes, setCrimeTypes] = useState<TypeOption[]>([]);
  const [loadingTypes, setLoadingTypes] = useState(false);
  const [typesError, setTypesError] = useState('');

  // Track if we're in edit mode
  const [isEditMode, setIsEditMode] = useState(false);

  // Fetch case and crime types from API
  useEffect(() => {
    const fetchTypes = async () => {
      try {
        setLoadingTypes(true);
        setTypesError('');

        const typesData = await caseService.getCaseAndCrimeTypes();
        console.log('Fetched types data:', typesData);

        if (typesData && typesData.caseTypes) {
          setCaseTypes(typesData.caseTypes);
        }

        if (typesData && typesData.crimeTypes) {
          setCrimeTypes(typesData.crimeTypes);
        }
      } catch (error) {
        console.error('Error fetching case and crime types:', error);
        setTypesError('Dava ve suç türleri yüklenirken bir hata oluştu.');
      } finally {
        setLoadingTypes(false);
      }
    };

    if (visible) {
      fetchTypes();
    }
  }, [visible]);

  // Populate form with existing data when editing
  useEffect(() => {
    if (visible && existingData) {
      console.log('Populating form with existing data:', existingData);
      setIsEditMode(true);

      // Set case type and find its label
      if (existingData.caseType) {
        setCaseType(existingData.caseType);
        // Find the matching case type label
        if (caseTypes && caseTypes.length > 0) {
          const matchingCaseType = caseTypes.find(type => type.code === existingData.caseType);
          if (matchingCaseType) {
            setCaseTypeLabel(matchingCaseType.displayName);
          }
        }
      }

      // Set crime type and find its label
      if (existingData.crimeType) {
        setCrimeType(existingData.crimeType);
        // Find the matching crime type label
        if (crimeTypes && crimeTypes.length > 0) {
          const matchingCrimeType = crimeTypes.find(type => type.code === existingData.crimeType);
          if (matchingCrimeType) {
            setCrimeTypeLabel(matchingCrimeType.displayName);
          }
        }
      }

      // Set other fields
      if (existingData.derdest !== undefined) {
        setDerdest(existingData.derdest);
      }

      if (existingData.caseValue !== undefined) {
        setCaseValue(existingData.caseValue.toString());
      }

      // Check if caseReason contains opening date information
      if (existingData.caseReason) {
        // Extract opening date from caseReason if it's in the format "Açılış Tarihi: XX.XX.XXXX\nOther info..."
        const openingDateMatch = existingData.caseReason.match(/Açılış Tarihi: ([^\n]+)/);

        // If we found an opening date in the caseReason, use it
        if (openingDateMatch && openingDateMatch[1] && openingDateMatch[1] !== 'Belirtilmemiş') {
          console.log('Found opening date in caseReason:', openingDateMatch[1]);

          // Remove the opening date line from caseReason to avoid duplication
          const cleanedReason = existingData.caseReason
            .replace(/Açılış Tarihi: [^\n]+\n?/, '')
            .trim();

          setCaseReason(cleanedReason);
        } else {
          // If no opening date found, use the caseReason as is
          setCaseReason(existingData.caseReason);
        }
      }

      if (existingData.caseTitle) {
        setCaseTitle(existingData.caseTitle);
      }
    } else {
      // Reset form when not editing
      setIsEditMode(false);
    }
  }, [visible, existingData, caseTypes, crimeTypes]);

  const handleSave = () => {
    // Validate form
    if (!caseType) {
      alert('Lütfen dava türünü seçiniz');
      return;
    }

    if (caseType === 'CEZA_DAVASI' && !crimeType) {
      alert('Lütfen suç türünü seçiniz');
      return;
    }

    if (!caseTitle) {
      alert('Lütfen dava başlığını giriniz');
      return;
    }

    // Validate case value if provided
    if (caseValue && caseValue.trim() !== '') {
      const numValue = parseFloat(caseValue);
      if (isNaN(numValue)) {
        alert('Dava değeri geçerli bir sayı olmalıdır');
        return;
      }
    }

    // Prepare the data to save
    const detailsToSave = {
      caseType,
      // Send empty string instead of null for crimeType when not CEZA_DAVASI
      crimeType: caseType === 'CEZA_DAVASI' ? crimeType : "",
      derdest,
      caseValue,
      caseReason: caseReason || "",
      caseTitle
    };

    console.log('Saving case details:', detailsToSave);
    onSave(detailsToSave);
  };

  const handleClose = () => {
    // Reset form
    setCaseType('');
    setCrimeType('');
    setDerdest(true);
    setCaseValue('');
    setCaseReason('');
    setCaseTitle('');
    setCaseTypeLabel('');
    setCrimeTypeLabel('');
    setShowCaseTypeDropdown(false);
    setShowCrimeTypeDropdown(false);
    setIsEditMode(false);
    onClose();
  };

  const selectCaseType = (code: string, displayName: string) => {
    setCaseType(code);
    setCaseTypeLabel(displayName);
    setShowCaseTypeDropdown(false);

    // Reset crime type if not a criminal case
    if (code !== 'CEZA_DAVASI') {
      setCrimeType('');
      setCrimeTypeLabel('');
    }
  };

  const selectCrimeType = (code: string, displayName: string) => {
    setCrimeType(code);
    setCrimeTypeLabel(displayName);
    setShowCrimeTypeDropdown(false);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={[styles.modalOverlay, { backgroundColor: 'transparent' }]}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <ThemedText type="subtitle" style={{ color: '#333333', fontWeight: '600' }}>
              {isEditMode ? 'Dava Detayını Düzenle' : 'Dava Detayı Ekle'}
            </ThemedText>
            <TouchableOpacity onPress={handleClose}>
              <Ionicons name="close" size={24} color="#333333" />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.formScrollView}
            className="no-scrollbar"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          >
            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>Dava Başlığı *</ThemedText>
              <TextInput
                style={[
                  styles.input,
                  isDark && styles.inputDark
                ]}
                value={caseTitle}
                onChangeText={setCaseTitle}
                placeholder="Örn: Ahmet Yılmaz vs. XYZ Şirketi"
                placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
              />
            </View>

            <View style={[styles.formGroup, { zIndex: 1000 }]}>
              <ThemedText style={styles.label}>Dava Türü *</ThemedText>
              <CustomDropdown
                options={caseTypes}
                selectedValue={caseType}
                selectedLabel={caseTypeLabel}
                placeholder="Dava türü seçiniz"
                onSelect={selectCaseType}
                loading={loadingTypes}
                disabled={loadingTypes}
                zIndex={1000}
              />

              {typesError ? (
                <ThemedText style={styles.errorText}>{typesError}</ThemedText>
              ) : null}
            </View>

            {caseType === 'CEZA_DAVASI' && (
              <View style={[styles.formGroup, { zIndex: 999 }]}>
                <ThemedText style={styles.label}>Suç Türü *</ThemedText>
                <CustomDropdown
                  options={crimeTypes}
                  selectedValue={crimeType}
                  selectedLabel={crimeTypeLabel}
                  placeholder="Suç türü seçiniz"
                  onSelect={selectCrimeType}
                  loading={loadingTypes}
                  disabled={loadingTypes}
                  zIndex={999}
                />
              </View>
            )}

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>Dava Değeri (TL)</ThemedText>
              <TextInput
                style={[
                  styles.input,
                  isDark && styles.inputDark
                ]}
                value={caseValue}
                onChangeText={setCaseValue}
                placeholder="Örn: 50000"
                placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>Dava Sebebi</ThemedText>
              <TextInput
                style={[
                  styles.input,
                  styles.textArea,
                  isDark && styles.inputDark
                ]}
                value={caseReason}
                onChangeText={setCaseReason}
                placeholder="Dava açılma sebebini giriniz"
                placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.switchContainer}>
              <View>
                <ThemedText style={styles.label}>Derdest</ThemedText>
                <ThemedText style={styles.helperText}>
                  Dava halen devam ediyor mu?
                </ThemedText>
              </View>
              <Switch
                value={derdest}
                onValueChange={setDerdest}
                trackColor={{ false: '#767577', true: Colors[colorScheme ?? 'light'].tint }}
                thumbColor={Platform.OS === 'ios' ? undefined : '#f4f3f4'}
              />
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={handleClose}
              disabled={loading}
            >
              <ThemedText style={styles.buttonText}>İptal</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
              disabled={loading}
            >
              <ThemedText style={styles.saveButtonText}>
                {loading ? 'Kaydediliyor...' : isEditMode ? 'Güncelle' : 'Kaydet'}
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 500,
    borderRadius: 8,
    padding: 20,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    maxHeight: '90%',
    backgroundColor: '#FFFFFF',
    position: 'relative',
    zIndex: 1,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
  },
  formScrollView: {
    maxHeight: 500,
    overflow: 'hidden',
  },
  formGroup: {
    marginBottom: 24,
    position: 'relative',
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  helperText: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 2,
  },
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#1f2937',
  },
  textArea: {
    height: 100,
    paddingTop: 12,
    paddingBottom: 12,
    textAlignVertical: 'top',
  },
  inputDark: {
    borderColor: '#374151',
    backgroundColor: '#1f2937',
    color: '#f9fafb',
  },
  dropdownButton: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownButtonDark: {
    borderColor: '#374151',
    backgroundColor: '#1f2937',
  },
  placeholderText: {
    color: '#9ca3af',
  },
  dropdown: {
    position: 'absolute',
    top: 48,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    zIndex: 9999,
    elevation: 9999,
    maxHeight: 200,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    overflow: 'hidden',
  },
  dropdownDark: {
    backgroundColor: '#1f2937',
    borderColor: '#374151',
  },
  dropdownItem: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F0F0F0',
    marginRight: 12,
  },
  saveButton: {
    backgroundColor: Colors.light.tint,
  },
  buttonText: {
    color: '#333333',
    fontSize: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    marginLeft: 8,
    color: '#9ca3af',
  },
  errorText: {
    color: '#ef4444',
    marginTop: 4,
    fontSize: 12,
  },
});
