import OpenAI from 'openai';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: "********************************************************************************************************************************************************************", // Use environment variable for API key
  dangerouslyAllowBrowser: true, // Allow usage in browser/React Native
});

// Message type definition
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt: Date;
}

// Assistant type definition
export interface Assistant {
  id: string;
  name: string;
  description?: string;
}

// LawbotService for handling OpenAI Assistants API interactions
const lawbotService = {
  // Create a new thread or retrieve existing one
  getThread: async (): Promise<string> => {
    try {
      // Check if we have a thread ID stored
      let threadId = await AsyncStorage.getItem('lawbot_thread_id');

      // If no thread exists, create a new one
      if (!threadId) {
        const thread = await openai.beta.threads.create();
        threadId = thread.id;
        await AsyncStorage.setItem('lawbot_thread_id', threadId);
      }

      return threadId;
    } catch (error) {
      console.error('Error getting thread:', error);
      throw error;
    }
  },

  // Create a new thread (used when switching assistants)
  createNewThread: async (): Promise<string> => {
    try {
      const thread = await openai.beta.threads.create();
      const threadId = thread.id;
      await AsyncStorage.setItem('lawbot_thread_id', threadId);
      return threadId;
    } catch (error) {
      console.error('Error creating new thread:', error);
      throw error;
    }
  },

  // Send a message to the assistant and get the response
  sendMessage: async (assistantId: string, content: string): Promise<Message[]> => {
    try {
      // Get or create a thread
      const threadId = await lawbotService.getThread();

      // Add the user message to the thread
      await openai.beta.threads.messages.create(threadId, {
        role: 'user',
        content,
      });

      // Run the assistant on the thread
      const run = await openai.beta.threads.runs.createAndPoll(threadId, {
        assistant_id: assistantId,
      });

      // If the run completed successfully, get the messages
      if (run.status === 'completed') {
        const messages = await openai.beta.threads.messages.list(threadId);

        // Convert OpenAI messages to our Message format
        return messages.data.map(msg => ({
          id: msg.id,
          role: msg.role as 'user' | 'assistant',
          content: msg.content[0].type === 'text' ? msg.content[0].text.value : '',
          createdAt: new Date(msg.created_at * 1000),
        })); // Return in chronological order (oldest first)
      } else {
        throw new Error(`Run failed with status: ${run.status}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  // Get all messages from the current thread
  getMessages: async (threadId: string): Promise<Message[]> => {
    try {
      const messages = await openai.beta.threads.messages.list(threadId);

      return messages.data.map(msg => ({
        id: msg.id,
        role: msg.role as 'user' | 'assistant',
        content: msg.content[0].type === 'text' ? msg.content[0].text.value : '',
        createdAt: new Date(msg.created_at * 1000),
      })); // Return in chronological order (oldest first)
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  },
};

export default lawbotService;
