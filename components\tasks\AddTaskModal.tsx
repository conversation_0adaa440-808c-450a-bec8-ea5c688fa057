import { StyleSheet, TouchableOpacity, View, TextInput, Platform, ActivityIndicator, Modal, SafeAreaView, FlatList, ScrollView } from 'react-native';
import React, { useState, useEffect, useMemo } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import taskService from '@/services/taskService';
import caseService from '@/services/caseService';
import { useCaseParties } from '@/contexts/CasePartiesContext';
import { formatDate as formatDateUtil, formatRelativeDate } from '@/utils/dateUtils';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '@/utils/flashMessageUtils';

// Define types for our data
interface CaseItem {
  id: string;
  title: string;
  dosyaNo: string;
  birimAdi?: string;
  dosyaTur?: string;
}

interface ClientItem {
  id: string;
  name: string;
  role?: string;
  type?: string;
}

interface PartyItem {
  id: string;
  name: string;
  role?: string;
  type?: string;
}

interface Task {
  id?: string | number;
  title: string;
  description?: string;
  priority?: string;
  status?: string;
  dueDate?: string;
  startDate?: string;
  type?: string;
  location?: string;
  relatedCase?: string;
  caseNumber?: string;
  [key: string]: any; // Allow any additional properties
}

interface AddTaskModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  task?: Task; // Optional task for editing
  isEditing?: boolean; // Flag to indicate if we're editing
}

const AddTaskModal: React.FC<AddTaskModalProps> = ({
  visible,
  onClose,
  onSuccess,
  task,
  isEditing = false
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [taskType, setTaskType] = useState('GÖREV');
  const [priority, setPriority] = useState('ORTA');
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(new Date(new Date().setDate(new Date().getDate() + 1)));
  const [isStartDatePickerVisible, setStartDatePickerVisible] = useState(false);
  const [isDueDatePickerVisible, setDueDatePickerVisible] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');
  const [location, setLocation] = useState('');
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [selectedCase, setSelectedCase] = useState<string | null>(null);

  // Dava taraflarını almak için context'i kullan
  const { allParties, loading: partiesLoading, fetchPartiesIfNeeded } = useCaseParties();

  // Seçilen davanın tarafları
  const [caseParties, setCaseParties] = useState<PartyItem[]>([]);
  const [showClientPicker, setShowClientPicker] = useState(false);
  const [showCasePicker, setShowCasePicker] = useState(false);

  // API state
  const [loading, setLoading] = useState(false);
  const [loadingCases, setLoadingCases] = useState(true);
  const [loadingClients, setLoadingClients] = useState(true);
  const [cases, setCases] = useState<CaseItem[]>([]);
  const [clients, setClients] = useState<ClientItem[]>([]);

  // Dava listesi sayfalama ve filtreleme için state'ler
  const [currentPage, setCurrentPage] = useState(1);
  const [caseFilter, setCaseFilter] = useState('');
  const casesPerPage = 5;

  // Filtrelenmiş davaları hesapla
  const filteredCases = useMemo(() => {
    if (!caseFilter) return cases;

    const searchLower = caseFilter.toLowerCase();
    return cases.filter(caseItem =>
      (caseItem.title?.toLowerCase() || '').includes(searchLower) ||
      (caseItem.dosyaNo?.toLowerCase() || '').includes(searchLower) ||
      (caseItem.birimAdi?.toLowerCase() || '').includes(searchLower) ||
      (caseItem.dosyaTur?.toLowerCase() || '').includes(searchLower)
    );
  }, [cases, caseFilter]);

  // Sayfalanmış davaları hesapla
  const paginatedCases = useMemo(() => {
    const startIndex = (currentPage - 1) * casesPerPage;
    return filteredCases.slice(startIndex, startIndex + casesPerPage);
  }, [filteredCases, currentPage, casesPerPage]);

  // Toplam sayfa sayısı
  const totalPages = Math.ceil(filteredCases.length / casesPerPage);

  // Sayfalama işlemleri
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Tarafları yükle
  useEffect(() => {
    fetchPartiesIfNeeded();
  }, []);

  // Seçilen dava değiştiğinde tarafları güncelle
  useEffect(() => {
    if (selectedCase) {
      // Seçilen davanın bilgilerini bul
      const selectedCaseData = cases.find(c => c.id === selectedCase);
      if (selectedCaseData && selectedCaseData.dosyaNo) {
        // Davanın taraflarını bul
        let parties = allParties[selectedCaseData.dosyaNo] || [];

        // Tarafları kişi formatına dönüştür
        const formattedParties = parties.map(party => {
          // Benzersiz bir ID oluştur
          const partyId = party.tarafId || party.id || `party-${Math.random().toString(36).substring(2, 9)}`;

          return {
            id: partyId,
            name: party.adi || party.tarafAdi || party.ad || `${party.tarafAd || ''} ${party.tarafSoyad || ''}`.trim() || 'Belirtilmemiş',
            role: party.rol || party.tarafTur || party.tarafTipi || 'Belirtilmemiş',
            type: party.kisiKurum || party.tarafTip || 'Belirtilmemiş'
          };
        });

        // Tarafları ayarla
        setCaseParties(formattedParties);

        // Eğer henüz bir kişi seçilmemişse ve taraflar varsa, ilk tarafı seç
        if (!selectedClient && formattedParties.length > 0) {
          setSelectedClient(formattedParties[0].id);
        }
      } else {
        setCaseParties([]);
      }
    } else {
      // Dava seçilmemişse tarafları temizle
      setCaseParties([]);
    }
  }, [selectedCase, allParties, cases]);

  // Load task data when editing
  useEffect(() => {
    if (visible && isEditing && task) {
      // Set form fields with task data
      setTitle(task.title || '');
      setDescription(task.description || '');

      // Set task type
      if (task.type) {
        const mappedType =
          task.type === 'TASK' ? 'GÖREV' :
          task.type === 'HEARING' ? 'DURUŞMA' :
          task.type === 'MEETING' ? 'TOPLANTI' : 'HATIRLATMA';
        setTaskType(mappedType);
      }

      // Set priority
      if (task.priority) {
        const mappedPriority =
          task.priority === 'LOW' ? 'DÜŞÜK' :
          task.priority === 'MEDIUM' ? 'ORTA' :
          task.priority === 'HIGH' ? 'YÜKSEK' : 'KRİTİK';
        setPriority(mappedPriority);
      }

      // Set dates - handle Unix timestamps
      if (task.startDate) {
        console.log('Setting start date from task:', task.startDate);
        // Check if it's a Unix timestamp (number) or ISO string
        if (typeof task.startDate === 'number') {
          // Convert Unix timestamp to Date object
          // If seconds (10 digits or less), convert to milliseconds
          const timestamp = task.startDate < 10000000000
            ? task.startDate * 1000
            : task.startDate;
          setStartDate(new Date(timestamp));
          console.log('Converted start date from Unix timestamp:', new Date(timestamp));
        } else {
          setStartDate(new Date(task.startDate));
          console.log('Converted start date from string:', new Date(task.startDate));
        }
      }

      if (task.dueDate) {
        console.log('Setting due date from task:', task.dueDate);
        // Check if it's a Unix timestamp (number) or ISO string
        if (typeof task.dueDate === 'number') {
          // Convert Unix timestamp to Date object
          // If seconds (10 digits or less), convert to milliseconds
          const timestamp = task.dueDate < 10000000000
            ? task.dueDate * 1000
            : task.dueDate;
          setDueDate(new Date(timestamp));
          console.log('Converted due date from Unix timestamp:', new Date(timestamp));
        } else {
          setDueDate(new Date(task.dueDate));
          console.log('Converted due date from string:', new Date(task.dueDate));
        }
      }

      // Set location
      if (task.location) {
        setLocation(task.location);
      }

      // Set case number (will be selected after cases are loaded)
      if (task.caseNumber) {
        // We'll select the case in the cases useEffect
        console.log('Task has case number:', task.caseNumber);
      }
    }
  }, [visible, isEditing, task]);

  // API'den dava listesini getir
  useEffect(() => {
    const fetchCases = async () => {
      try {
        setLoadingCases(true);
        const data = await caseService.getUserCases(true);

        // API'den gelen veriyi işle
        if (data && Array.isArray(data) && data.length > 0) {
          // API'nin döndüğü formatı kontrol et ve uygun şekilde işle
          const casesData = data[0] || [];

          // Dava listesini oluştur
          const formattedCases = casesData.map((caseItem: any): CaseItem => ({
            id: caseItem.dosyaId || caseItem.id,
            title: `${caseItem.dosyaNo || ''} ${caseItem.birimAdi || ''}`.trim() || 'Belirtilmemiş',
            dosyaNo: caseItem.dosyaNo,
            birimAdi: caseItem.birimAdi,
            dosyaTur: caseItem.dosyaTur
          }));

          // Dosya numarasına göre sırala
          formattedCases.sort((a: CaseItem, b: CaseItem) => {
            if (!a.dosyaNo) return 1;
            if (!b.dosyaNo) return -1;
            return b.dosyaNo.localeCompare(a.dosyaNo);
          });

          setCases(formattedCases);

          // If editing and task has a case number, select the corresponding case
          if (isEditing && task?.caseNumber && formattedCases.length > 0) {
            const caseToSelect = formattedCases.find(c => c.dosyaNo === task.caseNumber);
            if (caseToSelect) {
              setSelectedCase(caseToSelect.id);
              console.log('Selected case:', caseToSelect.id);
            }
          }
        } else {
          setCases([]);
        }

        setLoadingCases(false);
      } catch (err) {
        console.error('Error fetching cases:', err);
        setLoadingCases(false);
        setCases([]);
      }
    };

    if (visible) {
      fetchCases();
    }
  }, [visible, isEditing, task]);

  // Form validation
  const [errors, setErrors] = useState({
    title: '',
  });

  const validateForm = () => {
    // Show info message for form validation
    showInfoMessage(
      'Form Doğrulanıyor',
      'Form alanları kontrol ediliyor...'
    );

    let isValid = true;
    const newErrors = {
      title: '',
    };

    if (!title.trim()) {
      newErrors.title = 'Başlık alanı zorunludur';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setTaskType('GÖREV');
    setPriority('ORTA');
    setStartDate(new Date());
    setDueDate(new Date(new Date().setDate(new Date().getDate() + 1)));
    setLocation('');
    setSelectedClient(null);
    setSelectedCase(null);
    setErrors({ title: '' });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSave = async () => {
    if (validateForm()) {
      try {
        setLoading(true);

        // Seçilen dava bilgilerini al
        const selectedCaseData = selectedCase ? cases.find(c => c.id === selectedCase) : null;

        // Tarihleri doğru formatta hazırla
        console.log('Preparing dates for API submission:');
        console.log('Original startDate:', startDate);
        console.log('Original dueDate:', dueDate);

        // Tarihleri ISO formatına dönüştür ve zaman dilimini ayarla
        // Türkiye için UTC+3 (GMT+3)
        const startDateObj = new Date(startDate);
        const dueDateObj = new Date(dueDate);

        // ISO formatına dönüştür
        const startDateISO = startDateObj.toISOString();
        const dueDateISO = dueDateObj.toISOString();

        console.log('Formatted startDate:', startDateISO);
        console.log('Formatted dueDate:', dueDateISO);

        // API'ye gönderilecek görev verisi
        const taskData: {
          title: string;
          description: string;
          priority: string;
          status: string;
          startDate: string;
          dueDate: string;
          caseNumber: string | null;
          location?: string;
          type?: string;
        } = {
          title,
          description,
          priority: taskService.mapPriorityToApi(priority),
          status: isEditing ? (task?.status || 'OPEN') : 'OPEN',
          startDate: startDateISO,
          dueDate: dueDateISO,
          caseNumber: selectedCaseData?.dosyaNo || null, // Dava numarası
          type: taskType === 'GÖREV' ? 'TASK' :
                taskType === 'DURUŞMA' ? 'HEARING' :
                taskType === 'TOPLANTI' ? 'MEETING' : 'REMINDER'
        };

        // Sadece location alanını ekle
        if (location) {
          taskData.location = location;
        }

        let response;

        // API'ye gönder
        if (isEditing && task?.id) {
          // Update existing task
          console.log('Görev güncelleniyor:', JSON.stringify(taskData, null, 2));
          response = await taskService.updateTask(task.id, taskData);

          // Görev hatırlatıcısını güncelle
          try {
            const taskReminderService = require('@/services/taskReminderService').default;
            await taskReminderService.updateTaskReminder({
              ...response,
              id: task.id,
              title: response.title,
              description: response.description,
              dueDate: response.dueDate,
              type: taskType,
              priority: priority
            });
          } catch (reminderErr) {
            console.error('Hatırlatıcı güncelleme hatası:', reminderErr);
          }
        } else {
          // Create new task
          console.log('Yeni görev oluşturuluyor:', JSON.stringify(taskData, null, 2));
          response = await taskService.createTask(taskData);

          // Görev hatırlatıcısını planla
          try {
            const taskReminderService = require('@/services/taskReminderService').default;
            await taskReminderService.scheduleTaskReminder({
              id: response.id,
              title: response.title,
              description: response.description,
              dueDate: response.dueDate,
              type: taskType,
              priority: priority
            });
          } catch (reminderErr) {
            console.error('Hatırlatıcı planlama hatası:', reminderErr);
          }
        }

        // Görevler sayfasına dönmeden önce bir flag ayarla
        try {
          await AsyncStorage.setItem('refresh_tasks', 'true');
        } catch (storageErr) {
          console.error('AsyncStorage error:', storageErr);
        }

        // Başarılı mesajı göster
        showSuccessMessage(
          'Başarılı',
          isEditing ? 'Görev başarıyla güncellendi.' : 'Görev başarıyla eklendi.'
        );

        resetForm();
        onSuccess();
      } catch (err: any) {
        console.error(isEditing ? 'Görev güncelleme hatası:' : 'Görev oluşturma hatası:', err);

        // Hata mesajını göster
        showErrorMessage(
          isEditing ? 'Görev Güncellenemedi' : 'Görev Eklenemedi',
          isEditing
            ? 'Görev güncellenirken bir hata oluştu. Lütfen tekrar deneyin.'
            : 'Görev eklenirken bir hata oluştu. Lütfen tekrar deneyin.'
        );
      } finally {
        setLoading(false);
      }
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    // Use the utility function but without time
    return formatDateUtil(date.getTime(), false);
  };

  // Format time for display
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format full date and time for display
  const formatFullDateTime = (date: Date) => {
    // Use the utility function with time
    return formatDateUtil(date.getTime(), true);
  };



  // Show start date picker
  const showStartDatePicker = () => {
    console.log('Opening start date picker');
    setStartDatePickerVisible(true);
  };

  // Show due date picker
  const showDueDatePicker = () => {
    console.log('Opening due date picker');
    setDueDatePickerVisible(true);
  };

  // Handle date confirmation for start date
  const handleStartDateConfirm = (date: Date) => {
    console.log('handleStartDateConfirm called with date:', date);

    // Ensure the date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid date provided to handleStartDateConfirm');
      return;
    }

    // Create a new Date object to avoid reference issues
    const newDate = new Date(date);

    // Log the ISO string for debugging
    console.log('Start date ISO string:', newDate.toISOString());

    // Update the state
    setStartDate(newDate);
    setStartDatePickerVisible(false);
    console.log('Start date updated to:', newDate);
  };

  // Handle date confirmation for due date
  const handleDueDateConfirm = (date: Date) => {
    console.log('handleDueDateConfirm called with date:', date);

    // Ensure the date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid date provided to handleDueDateConfirm');
      return;
    }

    // Create a new Date object to avoid reference issues
    const newDate = new Date(date);

    // Log the ISO string for debugging
    console.log('Due date ISO string:', newDate.toISOString());

    // Update the state
    setDueDate(newDate);
    setDueDatePickerVisible(false);
    console.log('Due date updated to:', newDate);
  };

  // Handle date picker cancel
  const hideStartDatePicker = () => {
    setStartDatePickerVisible(false);
  };

  // Handle date picker cancel
  const hideDueDatePicker = () => {
    setDueDatePickerVisible(false);
  };



  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: 'white' }]}>
          <View style={styles.modalHeader}>
            <View style={styles.modalTitleContainer}>
              <Ionicons
                name={isEditing ? "create-outline" : "add-circle"}
                size={24}
                color={Colors[colorScheme ?? 'light'].tint}
                style={styles.modalTitleIcon}
              />
              <ThemedText style={styles.modalTitle}>
                {isEditing ? 'Görev Düzenle' : 'Yeni Görev Ekle'}
              </ThemedText>
            </View>
            <TouchableOpacity
              onPress={handleClose}
              style={styles.closeButton}
              activeOpacity={0.7}
            >
              <Ionicons name="close-circle" size={28} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
          </View>

          <View style={styles.scrollableContainer}>
            <View style={styles.formContainer}>
              <View style={styles.formContentContainer}>
            <View style={styles.formSection}>
              <View style={styles.formSectionHeader}>
                <Ionicons name="information-circle" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.formSectionTitle}>Temel Bilgiler</ThemedText>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="text" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>
                    Başlık
                    <ThemedText style={styles.requiredStar}> *</ThemedText>
                  </ThemedText>
                </View>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      isDark && styles.inputDark,
                      errors.title ? styles.inputError : null
                    ]}
                    placeholder="Görev başlığı giriniz"
                    placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                    value={title}
                    onChangeText={(text) => {
                      setTitle(text);
                      if (text.trim()) {
                        setErrors({...errors, title: ''});
                      }
                    }}
                  />
                  {title.length > 0 && (
                    <TouchableOpacity
                      style={styles.clearButton}
                      onPress={() => setTitle('')}
                    >
                      <Ionicons name="close-circle" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
                    </TouchableOpacity>
                  )}
                </View>
                {errors.title ? <ThemedText style={styles.errorText}>{errors.title}</ThemedText> : null}
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="document-text" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>Açıklama</ThemedText>
                </View>
                <TextInput
                  style={[
                    styles.input,
                    styles.textArea,
                    isDark && styles.inputDark
                  ]}
                  placeholder="Görev açıklaması giriniz"
                  placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                  value={description}
                  onChangeText={setDescription}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>

              <View style={styles.sectionDivider} />

              <View style={styles.subSectionHeader}>
                <Ionicons name="time" size={18} color={Colors[colorScheme ?? 'light'].tint} style={{marginRight: 6}} />
                <ThemedText style={styles.subSectionTitle}>Tarih ve Konum</ThemedText>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="calendar" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>Başlangıç Tarihi</ThemedText>
                </View>
                <TouchableOpacity
                  style={[styles.dateInput, isDark && styles.inputDark, {
                    backgroundColor: 'white',
                    borderWidth: 1,
                    borderColor: '#e5e7eb',
                    borderRadius: 10,
                  }]}
                  onPress={showStartDatePicker}
                  activeOpacity={0.7}
                >
                  <View style={styles.dateTimeContainer}>
                    <Ionicons name="calendar-outline" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                    <ThemedText style={styles.dateTimeText} numberOfLines={1} ellipsizeMode="tail">{formatDateUtil(startDate.getTime(), true)}</ThemedText>
                  </View>
                </TouchableOpacity>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="calendar" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>Bitiş Tarihi</ThemedText>
                </View>
                <TouchableOpacity
                  style={[styles.dateInput, isDark && styles.inputDark, {
                    backgroundColor: 'white',
                    borderWidth: 1,
                    borderColor: '#e5e7eb',
                    borderRadius: 10,
                  }]}
                  onPress={showDueDatePicker}
                  activeOpacity={0.7}
                >
                  <View style={styles.dateTimeContainer}>
                    <Ionicons name="calendar-outline" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                    <ThemedText style={styles.dateTimeText} numberOfLines={1} ellipsizeMode="tail">{formatDateUtil(dueDate.getTime(), true)}</ThemedText>
                  </View>
                </TouchableOpacity>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="location" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>Konum</ThemedText>
                </View>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[styles.input, isDark && styles.inputDark]}
                    placeholder="Konum bilgisi giriniz"
                    placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                    value={location}
                    onChangeText={setLocation}
                  />
                  {location.length > 0 && (
                    <TouchableOpacity
                      style={styles.clearButton}
                      onPress={() => setLocation('')}
                    >
                      <Ionicons name="close-circle" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
                    </TouchableOpacity>
                  )}
                </View>
              </View>

              <View style={styles.sectionDivider} />

              <View style={styles.subSectionHeader}>
                <Ionicons name="link" size={18} color={Colors[colorScheme ?? 'light'].tint} style={{marginRight: 6}} />
                <ThemedText style={styles.subSectionTitle}>İlgili Kayıtlar</ThemedText>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="briefcase" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>İlgili Dava</ThemedText>
                </View>
                <TouchableOpacity
                  style={[
                    styles.input,
                    styles.pickerInput,
                    selectedCase && styles.selectedPickerInput,
                    isDark && { backgroundColor: '#1F2937', color: '#fff' }
                  ].filter(Boolean)}
                  onPress={() => {
                    setShowCasePicker(true);
                    setShowClientPicker(false);
                  }}
                  activeOpacity={0.7}
                >
                  {selectedCase ? (
                    <View style={styles.selectedCaseContainer}>
                      <View style={[styles.caseTypeIndicator, { backgroundColor: getCaseTypeColor(cases.find(c => c.id === selectedCase)?.dosyaTur) }]} />
                      <ThemedText style={styles.selectedPickerText}>
                        {loadingCases ? 'Davalar yükleniyor...' : cases.find(c => c.id === selectedCase)?.title}
                      </ThemedText>
                    </View>
                  ) : (
                    <ThemedText style={styles.placeholderText}>
                      {loadingCases ? 'Davalar yükleniyor...' : 'Dava seçiniz (opsiyonel)'}
                    </ThemedText>
                  )}
                  <Ionicons
                    name="chevron-down-circle"
                    size={20}
                    color={selectedCase ? Colors[colorScheme ?? 'light'].tint : isDark ? '#9ca3af' : '#6B7280'}
                  />
                </TouchableOpacity>
              </View>
            </View>



            <View style={styles.formSection}>
              <View style={styles.formSectionHeader}>
                <Ionicons name="options" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.formSectionTitle}>Görev Türü ve Öncelik</ThemedText>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="list" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>Görev Tipi</ThemedText>
                </View>
                <View style={styles.taskTypeContainer}>
                  <TouchableOpacity
                    style={[
                      styles.taskTypeButton,
                      taskType === 'GÖREV' && styles.taskTypeButtonActive,
                      { borderColor: '#3B82F6' }
                    ]}
                    onPress={() => setTaskType('GÖREV')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: taskType === 'GÖREV' ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name="checkmark-circle"
                        size={24}
                        color={taskType === 'GÖREV' ? '#3B82F6' : isDark ? '#9ca3af' : '#64748b'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.taskTypeText,
                      taskType === 'GÖREV' && { color: '#3B82F6', fontWeight: '600' }
                    ]}>
                      Görev
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.taskTypeButton,
                      taskType === 'DURUŞMA' && styles.taskTypeButtonActive,
                      { borderColor: '#EF4444' }
                    ]}
                    onPress={() => setTaskType('DURUŞMA')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: taskType === 'DURUŞMA' ? 'rgba(239, 68, 68, 0.1)' : 'transparent',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name="briefcase"
                        size={24}
                        color={taskType === 'DURUŞMA' ? '#EF4444' : isDark ? '#9ca3af' : '#64748b'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.taskTypeText,
                      taskType === 'DURUŞMA' && { color: '#EF4444', fontWeight: '600' }
                    ]}>
                      Duruşma
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.taskTypeButton,
                      taskType === 'TOPLANTI' && styles.taskTypeButtonActive,
                      { borderColor: '#10B981' }
                    ]}
                    onPress={() => setTaskType('TOPLANTI')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: taskType === 'TOPLANTI' ? 'rgba(16, 185, 129, 0.1)' : 'transparent',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name="people"
                        size={24}
                        color={taskType === 'TOPLANTI' ? '#10B981' : isDark ? '#9ca3af' : '#64748b'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.taskTypeText,
                      taskType === 'TOPLANTI' && { color: '#10B981', fontWeight: '600' }
                    ]}>
                      Toplantı
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.taskTypeButton,
                      taskType === 'HATIRLATMA' && styles.taskTypeButtonActive,
                      { borderColor: '#F59E0B' }
                    ]}
                    onPress={() => setTaskType('HATIRLATMA')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: taskType === 'HATIRLATMA' ? 'rgba(245, 158, 11, 0.1)' : 'transparent',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name="alarm"
                        size={24}
                        color={taskType === 'HATIRLATMA' ? '#F59E0B' : isDark ? '#9ca3af' : '#64748b'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.taskTypeText,
                      taskType === 'HATIRLATMA' && { color: '#F59E0B', fontWeight: '600' }
                    ]}>
                      Hatırlatma
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Ionicons name="flag" size={16} color={isDark ? '#9ca3af' : '#64748b'} style={styles.labelIcon} />
                  <ThemedText style={styles.label}>Öncelik Seviyesi</ThemedText>
                </View>
                <View style={styles.priorityContainer}>
                  <TouchableOpacity
                    style={[
                      styles.priorityButton,
                      priority === 'DÜŞÜK' && styles.priorityButtonActive,
                      {
                        backgroundColor: priority === 'DÜŞÜK' ? 'rgba(16, 185, 129, 0.1)' : 'white',
                        borderColor: '#10B981'
                      }
                    ]}
                    onPress={() => setPriority('DÜŞÜK')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 28,
                      height: 28,
                      borderRadius: 14,
                      backgroundColor: priority === 'DÜŞÜK' ? '#10B981' : 'rgba(16, 185, 129, 0.1)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name={priority === 'DÜŞÜK' ? "checkmark" : "ellipse-outline"}
                        size={16}
                        color={priority === 'DÜŞÜK' ? 'white' : '#10B981'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.priorityText,
                      { color: '#10B981', fontWeight: priority === 'DÜŞÜK' ? '700' : '600' }
                    ]}>
                      DÜŞÜK
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.priorityButton,
                      priority === 'ORTA' && styles.priorityButtonActive,
                      {
                        backgroundColor: priority === 'ORTA' ? 'rgba(245, 158, 11, 0.1)' : 'white',
                        borderColor: '#F59E0B'
                      }
                    ]}
                    onPress={() => setPriority('ORTA')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 28,
                      height: 28,
                      borderRadius: 14,
                      backgroundColor: priority === 'ORTA' ? '#F59E0B' : 'rgba(245, 158, 11, 0.1)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name={priority === 'ORTA' ? "checkmark" : "ellipse-outline"}
                        size={16}
                        color={priority === 'ORTA' ? 'white' : '#F59E0B'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.priorityText,
                      { color: '#F59E0B', fontWeight: priority === 'ORTA' ? '700' : '600' }
                    ]}>
                      ORTA
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.priorityButton,
                      priority === 'YÜKSEK' && styles.priorityButtonActive,
                      {
                        backgroundColor: priority === 'YÜKSEK' ? 'rgba(239, 68, 68, 0.1)' : 'white',
                        borderColor: '#EF4444'
                      }
                    ]}
                    onPress={() => setPriority('YÜKSEK')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 28,
                      height: 28,
                      borderRadius: 14,
                      backgroundColor: priority === 'YÜKSEK' ? '#EF4444' : 'rgba(239, 68, 68, 0.1)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name={priority === 'YÜKSEK' ? "checkmark" : "ellipse-outline"}
                        size={16}
                        color={priority === 'YÜKSEK' ? 'white' : '#EF4444'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.priorityText,
                      { color: '#EF4444', fontWeight: priority === 'YÜKSEK' ? '700' : '600' }
                    ]}>
                      YÜKSEK
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.priorityButton,
                      priority === 'KRİTİK' && styles.priorityButtonActive,
                      {
                        backgroundColor: priority === 'KRİTİK' ? 'rgba(124, 58, 237, 0.1)' : 'white',
                        borderColor: '#7C3AED'
                      }
                    ]}
                    onPress={() => setPriority('KRİTİK')}
                    activeOpacity={0.7}
                  >
                    <View style={{
                      width: 28,
                      height: 28,
                      borderRadius: 14,
                      backgroundColor: priority === 'KRİTİK' ? '#7C3AED' : 'rgba(124, 58, 237, 0.1)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12
                    }}>
                      <Ionicons
                        name={priority === 'KRİTİK' ? "checkmark" : "ellipse-outline"}
                        size={16}
                        color={priority === 'KRİTİK' ? 'white' : '#7C3AED'}
                      />
                    </View>
                    <ThemedText style={[
                      styles.priorityText,
                      { color: '#7C3AED', fontWeight: priority === 'KRİTİK' ? '700' : '600' }
                    ]}>
                      KRİTİK
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
            </View>



            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleClose}
                disabled={loading}
                activeOpacity={0.7}
              >
                <Ionicons name="close-circle" size={20} color="#64748b" style={styles.buttonIcon} />
                <ThemedText style={styles.buttonText}>İptal</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSave}
                disabled={loading}
                activeOpacity={0.7}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonIcon} />
                ) : (
                  <Ionicons name="checkmark-circle" size={20} color="white" style={styles.buttonIcon} />
                )}
                <ThemedText style={styles.saveButtonText}>
                  {loading
                    ? (isEditing ? 'Güncelleniyor...' : 'Kaydediliyor...')
                    : (isEditing ? 'Güncelle' : 'Görev Ekle')
                  }
                </ThemedText>
              </TouchableOpacity>
            </View>
              </View>
            </View>
          </View>
        </View>

        {/* Date Time Picker for Start Date */}
        {Platform.OS !== 'web' ? (
          <DateTimePickerModal
            isVisible={isStartDatePickerVisible}
            mode="datetime"
            onConfirm={handleStartDateConfirm}
            onCancel={hideStartDatePicker}
            date={startDate}
            locale="tr"
            confirmTextIOS="Tamam"
            cancelTextIOS="İptal"
            // headerTextIOS property removed as it's not supported
          />
        ) : (
          isStartDatePickerVisible && (
            <Modal
              visible={isStartDatePickerVisible}
              transparent={true}
              animationType="fade"
              onRequestClose={hideStartDatePicker}
            >
              <View style={styles.webDatePickerOverlay}>
                <View style={styles.webDatePickerContainer}>
                  <View style={styles.webDatePickerHeader}>
                    <ThemedText style={styles.webDatePickerTitle}>Başlangıç Tarihi ve Saati</ThemedText>
                    <TouchableOpacity onPress={hideStartDatePicker} style={styles.webDatePickerCloseButton}>
                      <Ionicons name="close" size={20} color="#FFFFFF" />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.webDatePickerContent}>
                    {/* Date Picker */}
                    <View style={styles.webDatePickerSection}>
                      <View style={styles.webDatePickerRow}>
                        <Ionicons name="calendar-outline" size={18} color={Colors.light.tint} />
                        <ThemedText style={styles.webDatePickerSectionTitle}>Tarih</ThemedText>
                      </View>
                      {Platform.OS === 'web' && (
                        <input
                          id="start-date-picker"
                          type="date"
                          value={startDate.toISOString().split('T')[0]}
                          onChange={(e) => {
                            console.log('Date input changed:', e.target.value);
                            // Create a new date object preserving the time from the current startDate
                            const currentHours = startDate.getHours();
                            const currentMinutes = startDate.getMinutes();
                            const currentSeconds = startDate.getSeconds();

                            // Parse the new date from the input
                            const [year, month, day] = e.target.value.split('-');

                            // Create a new date with the selected date but preserve the time
                            const newDate = new Date(
                              parseInt(year),
                              parseInt(month) - 1,
                              parseInt(day),
                              currentHours,
                              currentMinutes,
                              currentSeconds
                            );

                            if (!isNaN(newDate.getTime())) {
                              console.log('Setting new start date:', newDate);
                              console.log('ISO string:', newDate.toISOString());
                              handleStartDateConfirm(newDate);
                            }
                          }}
                          title="Başlangıç Tarihi"
                          aria-label="Başlangıç Tarihi"
                          style={{
                            fontSize: 15,
                            padding: 10,
                            borderRadius: 6,
                            borderWidth: 1,
                            borderColor: '#e5e7eb',
                            width: '100%',
                            backgroundColor: '#FFFFFF',
                            color: '#1f2937',
                            height: 44,
                          }}
                        />
                      )}
                    </View>

                    {/* Time Picker */}
                    <View style={styles.webDatePickerSection}>
                      <View style={styles.webDatePickerRow}>
                        <Ionicons name="time-outline" size={18} color={Colors.light.tint} />
                        <ThemedText style={styles.webDatePickerSectionTitle}>Saat</ThemedText>
                      </View>
                      {Platform.OS === 'web' && (
                        <input
                          id="start-time-picker"
                          type="time"
                          value={`${String(startDate.getHours()).padStart(2, '0')}:${String(startDate.getMinutes()).padStart(2, '0')}`}
                          onChange={(e) => {
                            console.log('Time input changed:', e.target.value);

                            // Create a new date object preserving the date from the current startDate
                            const currentYear = startDate.getFullYear();
                            const currentMonth = startDate.getMonth();
                            const currentDay = startDate.getDate();

                            // Parse the new time from the input
                            const [hours, minutes] = e.target.value.split(':');

                            // Create a new date with the current date but update the time
                            const newDate = new Date(
                              currentYear,
                              currentMonth,
                              currentDay,
                              parseInt(hours),
                              parseInt(minutes),
                              0
                            );

                            if (!isNaN(newDate.getTime())) {
                              console.log('Setting new start time:', newDate);
                              console.log('ISO string:', newDate.toISOString());
                              handleStartDateConfirm(newDate);
                            }
                          }}
                          title="Başlangıç Saati"
                          aria-label="Başlangıç Saati"
                          style={{
                            fontSize: 15,
                            padding: 10,
                            borderRadius: 6,
                            borderWidth: 1,
                            borderColor: '#e5e7eb',
                            width: '100%',
                            backgroundColor: '#FFFFFF',
                            color: '#1f2937',
                            height: 44,
                          }}
                        />
                      )}
                    </View>
                  </View>

                  <View style={styles.webDatePickerButtons}>
                    <TouchableOpacity
                      style={{
                        paddingVertical: 10,
                        paddingHorizontal: 16,
                        borderRadius: 6,
                      }}
                      onPress={hideStartDatePicker}
                    >
                      <ThemedText style={{
                        color: '#64748b',
                        fontWeight: '500',
                        fontSize: 15,
                      }}>İptal</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        borderRadius: 6,
                        backgroundColor: Colors.light.tint,
                      }}
                      onPress={() => {
                        console.log('Confirming start date:', startDate);
                        handleStartDateConfirm(startDate);
                      }}
                    >
                      <ThemedText style={{
                        color: '#FFFFFF',
                        fontWeight: '600',
                        fontSize: 15,
                      }}>Tamam</ThemedText>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
          )
        )}

        {/* Date Time Picker for Due Date */}
        {Platform.OS !== 'web' ? (
          <DateTimePickerModal
            isVisible={isDueDatePickerVisible}
            mode="datetime"
            onConfirm={handleDueDateConfirm}
            onCancel={hideDueDatePicker}
            date={dueDate}
            locale="tr"
            confirmTextIOS="Tamam"
            cancelTextIOS="İptal"
            // headerTextIOS property removed as it's not supported
          />
        ) : (
          isDueDatePickerVisible && (
            <Modal
              visible={isDueDatePickerVisible}
              transparent={true}
              animationType="fade"
              onRequestClose={hideDueDatePicker}
            >
              <View style={styles.webDatePickerOverlay}>
                <View style={styles.webDatePickerContainer}>
                  <View style={styles.webDatePickerHeader}>
                    <ThemedText style={styles.webDatePickerTitle}>Bitiş Tarihi ve Saati</ThemedText>
                    <TouchableOpacity onPress={hideDueDatePicker} style={styles.webDatePickerCloseButton}>
                      <Ionicons name="close" size={20} color="#FFFFFF" />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.webDatePickerContent}>
                    {/* Date Picker */}
                    <View style={styles.webDatePickerSection}>
                      <View style={styles.webDatePickerRow}>
                        <Ionicons name="calendar-outline" size={18} color={Colors.light.tint} />
                        <ThemedText style={styles.webDatePickerSectionTitle}>Tarih</ThemedText>
                      </View>
                      {Platform.OS === 'web' && (
                        <input
                          id="due-date-picker"
                          type="date"
                          value={dueDate.toISOString().split('T')[0]}
                          onChange={(e) => {
                            console.log('Due date input changed:', e.target.value);

                            // Create a new date object preserving the time from the current dueDate
                            const currentHours = dueDate.getHours();
                            const currentMinutes = dueDate.getMinutes();
                            const currentSeconds = dueDate.getSeconds();

                            // Parse the new date from the input
                            const [year, month, day] = e.target.value.split('-');

                            // Create a new date with the selected date but preserve the time
                            const newDate = new Date(
                              parseInt(year),
                              parseInt(month) - 1,
                              parseInt(day),
                              currentHours,
                              currentMinutes,
                              currentSeconds
                            );

                            if (!isNaN(newDate.getTime())) {
                              console.log('Setting new due date:', newDate);
                              console.log('ISO string:', newDate.toISOString());
                              handleDueDateConfirm(newDate);
                            }
                          }}
                          title="Bitiş Tarihi"
                          aria-label="Bitiş Tarihi"
                          style={{
                            fontSize: 15,
                            padding: 10,
                            borderRadius: 6,
                            borderWidth: 1,
                            borderColor: '#e5e7eb',
                            width: '100%',
                            backgroundColor: '#FFFFFF',
                            color: '#1f2937',
                            height: 44,
                          }}
                        />
                      )}
                    </View>

                    {/* Time Picker */}
                    <View style={styles.webDatePickerSection}>
                      <View style={styles.webDatePickerRow}>
                        <Ionicons name="time-outline" size={18} color={Colors.light.tint} />
                        <ThemedText style={styles.webDatePickerSectionTitle}>Saat</ThemedText>
                      </View>
                      {Platform.OS === 'web' && (
                        <input
                          id="due-time-picker"
                          type="time"
                          value={`${String(dueDate.getHours()).padStart(2, '0')}:${String(dueDate.getMinutes()).padStart(2, '0')}`}
                          onChange={(e) => {
                            console.log('Due time input changed:', e.target.value);

                            // Create a new date object preserving the date from the current dueDate
                            const currentYear = dueDate.getFullYear();
                            const currentMonth = dueDate.getMonth();
                            const currentDay = dueDate.getDate();

                            // Parse the new time from the input
                            const [hours, minutes] = e.target.value.split(':');

                            // Create a new date with the current date but update the time
                            const newDate = new Date(
                              currentYear,
                              currentMonth,
                              currentDay,
                              parseInt(hours),
                              parseInt(minutes),
                              0
                            );

                            if (!isNaN(newDate.getTime())) {
                              console.log('Setting new due time:', newDate);
                              console.log('ISO string:', newDate.toISOString());
                              handleDueDateConfirm(newDate);
                            }
                          }}
                          title="Bitiş Saati"
                          aria-label="Bitiş Saati"
                          style={{
                            fontSize: 15,
                            padding: 10,
                            borderRadius: 6,
                            borderWidth: 1,
                            borderColor: '#e5e7eb',
                            width: '100%',
                            backgroundColor: '#FFFFFF',
                            color: '#1f2937',
                            height: 44,
                          }}
                        />
                      )}
                    </View>
                  </View>

                  <View style={styles.webDatePickerButtons}>
                    <TouchableOpacity
                      style={{
                        paddingVertical: 10,
                        paddingHorizontal: 16,
                        borderRadius: 6,
                      }}
                      onPress={hideDueDatePicker}
                    >
                      <ThemedText style={{
                        color: '#64748b',
                        fontWeight: '500',
                        fontSize: 15,
                      }}>İptal</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        borderRadius: 6,
                        backgroundColor: Colors.light.tint,
                      }}
                      onPress={() => {
                        console.log('Confirming due date:', dueDate);
                        handleDueDateConfirm(dueDate);
                      }}
                    >
                      <ThemedText style={{
                        color: '#FFFFFF',
                        fontWeight: '600',
                        fontSize: 15,
                      }}>Tamam</ThemedText>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
          )
        )}

        {/* Dava Seçim Modali */}
        <Modal
          visible={showCasePicker}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowCasePicker(false)}
        >
          <SafeAreaView style={styles.pickerModalContainer}>
            <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.pickerModalContent}>
              <View style={styles.pickerModalHeader}>
                <ThemedText style={styles.pickerModalTitle}>Dava Seçin</ThemedText>
                <TouchableOpacity onPress={() => setShowCasePicker(false)} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                </TouchableOpacity>
              </View>

              <TextInput
                style={[styles.input, styles.searchInput, isDark && styles.inputDark]}
                placeholder="Dava ara..."
                placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                value={caseFilter}
                onChangeText={setCaseFilter}
              />

              {loadingCases ? (
                <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} style={styles.loader} />
              ) : (
                <>
                  <FlatList
                    data={paginatedCases}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                      <TouchableOpacity
                        style={[
                          styles.pickerItem,
                          selectedCase === item.id && styles.pickerItemSelected
                        ]}
                        onPress={() => {
                          setSelectedCase(item.id);
                          setShowCasePicker(false);
                        }}
                      >
                        <ThemedText style={styles.pickerItemText}>{item.title}</ThemedText>
                        {item.dosyaTur && (
                          <View style={[styles.caseTypeTag, { backgroundColor: getCaseTypeColor(item.dosyaTur) + '20' }]}>
                            <ThemedText style={[styles.caseTypeText, { color: getCaseTypeColor(item.dosyaTur) }]}>
                              {item.dosyaTur}
                            </ThemedText>
                          </View>
                        )}
                      </TouchableOpacity>
                    )}
                    ListEmptyComponent={
                      <View style={styles.emptyList}>
                        <ThemedText style={styles.emptyListText}>Dava bulunamadı</ThemedText>
                      </View>
                    }
                  />

                  {totalPages > 1 && (
                    <View style={styles.pagination}>
                      <TouchableOpacity
                        style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
                        onPress={goToPrevPage}
                        disabled={currentPage === 1}
                      >
                        <Ionicons name="chevron-back" size={16} color={currentPage === 1 ? '#9ca3af' : Colors[colorScheme ?? 'light'].tint} />
                      </TouchableOpacity>
                      <ThemedText style={styles.paginationText}>{currentPage} / {totalPages}</ThemedText>
                      <TouchableOpacity
                        style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
                        onPress={goToNextPage}
                        disabled={currentPage === totalPages}
                      >
                        <Ionicons name="chevron-forward" size={16} color={currentPage === totalPages ? '#9ca3af' : Colors[colorScheme ?? 'light'].tint} />
                      </TouchableOpacity>
                    </View>
                  )}
                </>
              )}
            </BlurView>
          </SafeAreaView>
        </Modal>
      </SafeAreaView>
    </Modal>
  );
};

// Dava türüne göre renk belirleme fonksiyonu
const getCaseTypeColor = (caseType: string | undefined): string => {
  if (!caseType) return '#64748b'; // Varsayılan gri

  const type = caseType.toLowerCase();
  if (type.includes('ceza')) return '#EF4444'; // Kırmızı
  if (type.includes('hukuk')) return '#10B981'; // Yeşil
  if (type.includes('idari')) return '#F59E0B'; // Turuncu
  if (type.includes('icra')) return '#3B82F6'; // Mavi
  if (type.includes('vergi')) return '#8B5CF6'; // Mor

  return '#64748b'; // Varsayılan gri
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  modalContent: {
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: '95%',
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    ...(Platform.OS === 'web'
      ? {
          boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
          maxWidth: 1000,
          marginHorizontal: 'auto'
        }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 20,
          elevation: 5,
        }
    ),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    backgroundColor: 'white',
  },
  scrollableContainer: {
    flex: 1,
    overflow: 'scroll',
    ...(Platform.OS === 'web' && {
      // Use proper React Native Web compatible properties
      scrollbarWidth: 'none'
    }),
  },
  modalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalTitleIcon: {
    marginRight: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  formContainer: {
    padding: 16,
    flex: 1,
    ...(Platform.OS === 'web' ? {
      overflow: 'scroll',
      // Web-specific styles handled via CSS
    } : {
      overflow: 'scroll',
    }),
  },
  formContentContainer: {
    paddingBottom: 16,
    ...(Platform.OS === 'web' && {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      gap: '16px',
    }),
  },
  formSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    ...(Platform.OS === 'web'
      ? {
          // Web-specific styles
          marginBottom: 0,
          // Width will be handled via CSS
          width: '48%', // Approximate equivalent that's valid in RN
        }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.05,
          shadowRadius: 8,
          elevation: 2,
          marginBottom: 16,
        }
    ),
  },
  formSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  formSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    color: Colors.light.tint,
  },
  sectionDivider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    marginVertical: 16,
  },
  subSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
  },
  subSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  formGroup: {
    marginBottom: 14,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  labelIcon: {
    marginRight: 6,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
  },
  requiredStar: {
    color: '#EF4444',
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    height: 44,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 10,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#1f2937',
  },
  inputDark: {
    borderColor: '#374151',
    backgroundColor: '#1f2937',
    color: '#f9fafb',
  },
  textArea: {
    height: 100,
    paddingTop: 12,
    paddingBottom: 12,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#EF4444',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 12,
    marginTop: 4,
  },
  clearButton: {
    position: 'absolute',
    right: 12,
    padding: 4,
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    paddingHorizontal: 14,
    flexWrap: 'nowrap',
    overflow: 'hidden',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    width: '48%',
    overflow: 'hidden',
  },
  dateTimeIcon: {
    marginRight: 6,
    color: Colors.light.tint,
    flexShrink: 0,
  },
  dateTimeText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    flexShrink: 1,
    overflow: 'hidden',
    maxWidth: '80%',
  },
  pickerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 44,
  },
  selectedPickerInput: {
    borderColor: Colors.light.tint,
    borderWidth: 1.5,
  },
  selectedCaseContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  caseTypeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  selectedPickerText: {
    color: Colors.light.tint,
    fontWeight: '500',
  },
  placeholderText: {
    color: '#9ca3af',
  },
  taskTypeContainer: {
    flexDirection: 'column',
    marginVertical: 4,
  },
  taskTypeButton: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 8,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 3,
          elevation: 1,
        }
    ),
  },
  taskTypeButtonActive: {
    borderWidth: 2,
  },
  taskTypeIcon: {
    marginRight: 12,
  },
  taskTypeText: {
    fontSize: 15,
    fontWeight: '500',
  },
  priorityContainer: {
    flexDirection: 'column',
    marginVertical: 4,
  },
  priorityButton: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    backgroundColor: 'white',
  },
  priorityButtonActive: {
    borderWidth: 2,
  },
  priorityText: {
    fontWeight: '600',
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    ...(Platform.OS === 'web' && {
      width: '100%',
      paddingRight: 0,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: '#f0f0f0',
    }),
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    minWidth: 120,
  },
  buttonIcon: {
    marginRight: 8,
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  saveButton: {
    backgroundColor: Colors.light.tint,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(59, 130, 246, 0.3)' }
      : {
          shadowColor: Colors.light.tint,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 4,
          elevation: 2,
        }
    ),
  },
  buttonText: {
    color: '#4B5563',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  dateTimePickerContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  dateTimePickerContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px -4px 20px rgba(0, 0, 0, 0.15)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 0.15,
          shadowRadius: 20,
          elevation: 5,
        }
    ),
  },
  dateTimePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 12,
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  dateTimePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  dateTimePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    paddingTop: 16,
  },
  dateTimePickerCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dateTimePickerCancelText: {
    color: '#4B5563',
    fontWeight: '500',
    fontSize: 16,
  },
  dateTimePickerConfirmButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: Colors.light.tint,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(59, 130, 246, 0.3)' }
      : {
          shadowColor: Colors.light.tint,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 4,
          elevation: 2,
        }
    ),
  },
  dateTimePickerConfirmText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  datePickerHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  datePickerHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  datePickerCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  datePickerCancelText: {
    color: '#64748b',
    fontWeight: '500',
    fontSize: 15,
  },
  datePickerConfirmButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: Colors.light.tint,
    alignItems: 'center',
    justifyContent: 'center',
    ...(Platform.OS === 'web'
      ? {
          boxShadow: '0px 2px 4px rgba(59, 130, 246, 0.3)',
          transition: 'all 0.2s ease',
          ':hover': {
            backgroundColor: '#2563eb',
            transform: 'translateY(-1px)',
            boxShadow: '0px 4px 6px rgba(59, 130, 246, 0.4)'
          },
          ':active': {
            transform: 'translateY(0px)',
            boxShadow: '0px 2px 4px rgba(59, 130, 246, 0.3)'
          }
        }
      : {
          shadowColor: Colors.light.tint,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 4,
          elevation: 2,
        }
    ),
  },
  datePickerConfirmText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 15,
  },
  webDatePickerOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  webDatePickerContainer: {
    width: 320,
    maxWidth: '90%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 0,
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? {
          // Remove non-React Native compatible styles
          // boxShadow and animation will be handled via CSS
        }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.2,
          shadowRadius: 30,
          elevation: 10,
        }
    ),
  },
  webDatePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.tint,
    borderBottomWidth: 0,
  },
  webDatePickerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  webDatePickerContent: {
    padding: 16,
  },
  webDatePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  webDatePickerLabel: {
    marginBottom: 8,
    fontWeight: '500',
    fontSize: 14,
    color: '#64748b',
  },
  webDatePickerSection: {
    marginBottom: 16,
  },
  webDatePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  webDatePickerIcon: {
    marginRight: 8,
    color: Colors.light.tint,
  },
  webDatePickerSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.light.tint,
  },
  webDateInput: {
    fontSize: 15,
    padding: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    width: '100%',
    backgroundColor: '#FFFFFF',
    color: '#1f2937',
    height: 44,
    // Remove non-React Native compatible styles
  },
  webTimeInput: {
    fontSize: 15,
    padding: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    width: '100%',
    backgroundColor: '#FFFFFF',
    color: '#1f2937',
    height: 44,
    // Remove non-React Native compatible styles
  },
  webDatePickerCloseButton: {
    padding: 4,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  pickerModalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerModalContent: {
    margin: 20,
    borderRadius: 12,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  pickerModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  pickerModalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchInput: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  loader: {
    marginVertical: 20,
  },
  pickerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  pickerItemSelected: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  pickerItemText: {
    fontSize: 16,
  },
  caseTypeTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  caseTypeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyList: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: '#9ca3af',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  paginationButton: {
    padding: 8,
  },
  paginationButtonDisabled: {
    opacity: 0.5,
  },
  paginationText: {
    marginHorizontal: 8,
  },
});

export default AddTaskModal;