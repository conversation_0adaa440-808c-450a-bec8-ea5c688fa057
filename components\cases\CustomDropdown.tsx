import React, { useState, useEffect, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, FlatList, ActivityIndicator, Text, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ThemedText } from '@/components/ThemedText';

interface DropdownOption {
  code: string;
  displayName: string;
}

interface CustomDropdownProps {
  options: DropdownOption[];
  selectedValue: string;
  selectedLabel: string;
  placeholder: string;
  onSelect: (code: string, displayName: string) => void;
  loading?: boolean;
  disabled?: boolean;
  zIndex?: number;
}

export const CustomDropdown = ({
  options,
  selectedValue,
  selectedLabel,
  placeholder,
  onSelect,
  loading = false,
  disabled = false,
  zIndex = 1
}: CustomDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const dropdownRef = useRef<View>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !(dropdownRef.current as any).contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = (e: any) => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      setTimeout(() => {
        document.addEventListener('click', handleOutsideClick);
      }, 0);
    }

    return () => {
      document.removeEventListener('click', handleOutsideClick);
    };
  }, [isOpen]);

  return (
    <View ref={dropdownRef} style={[styles.container, { zIndex: isOpen ? 9999 : zIndex }]}>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          isDark && styles.dropdownButtonDark,
          isOpen && styles.dropdownButtonOpen
        ]}
        onPress={(e) => {
          e.stopPropagation();
          if (!disabled && !loading) {
            setIsOpen(!isOpen);
          }
        }}
        disabled={disabled || loading}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
            <Text style={styles.loadingText}>Yükleniyor...</Text>
          </View>
        ) : (
          <>
            <ThemedText style={selectedLabel ? undefined : styles.placeholderText}>
              {selectedLabel || placeholder}
            </ThemedText>
            <Ionicons
              name={isOpen ? "chevron-up" : "chevron-down"}
              size={20}
              color={isDark ? '#9ca3af' : '#6B7280'}
            />
          </>
        )}
      </TouchableOpacity>

      {isOpen && options.length > 0 && (
        <div
          style={{
            position: 'absolute',
            top: 40,
            left: 0,
            right: 0,
            backgroundColor: isDark ? '#1f2937' : '#fff',
            borderWidth: 1,
            borderTopWidth: 0,
            borderColor: isDark ? '#374151' : '#e5e7eb',
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
            zIndex: 9999,
            maxHeight: 150,
            overflowY: 'auto',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          }}
          className="custom-scrollbar"
        >
          {options.map((item) => (
            <TouchableOpacity
              key={item.code}
              style={[
                styles.dropdownItem,
                selectedValue === item.code && styles.selectedItem
              ]}
              onPress={(e) => {
                e.stopPropagation();
                onSelect(item.code, item.displayName);
                setIsOpen(false);
              }}
            >
              <ThemedText style={selectedValue === item.code ? styles.selectedItemText : undefined}>
                {item.displayName}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </div>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    marginBottom: 30,
  },
  dropdownButton: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownButtonDark: {
    borderColor: '#374151',
    backgroundColor: '#1f2937',
  },
  dropdownButtonOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  placeholderText: {
    color: '#9ca3af',
  },
  dropdown: {
    position: 'absolute',
    top: 40,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: '#e5e7eb',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    zIndex: 9999,
    maxHeight: 150,
    overflow: 'auto',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginBottom: 20,
    WebkitOverflowScrolling: 'touch',
  },
  dropdownDark: {
    backgroundColor: '#1f2937',
    borderColor: '#374151',
  },
  dropdownItem: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  selectedItem: {
    backgroundColor: '#f3f4f6',
  },
  selectedItemText: {
    fontWeight: 'bold',
    color: '#2563eb',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  loadingText: {
    marginLeft: 8,
    color: '#9ca3af',
  },
});
