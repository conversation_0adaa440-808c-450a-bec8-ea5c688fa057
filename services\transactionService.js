import apiClient from './api';

// Ofis işlemleri servisi (gelir-gider)
const transactionService = {
  // Tüm işlemleri getir (gelir veya gider filtrelemesi yapılabilir)
  getAllTransactions: async (category = null) => {
    try {
      let url = '/api/user/office/transactions/income-expense';
      if (category) {
        url += `?category=${category}`;
      }
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error('Get all transactions error:', error);
      throw error;
    }
  },
  
  // Yeni işlem oluştur
  createTransaction: async (transactionData) => {
    try {
      const response = await apiClient.post('/api/user/office/transactions/income-expense', transactionData);
      return response.data;
    } catch (error) {
      console.error('Create transaction error:', error);
      throw error;
    }
  },
  
  // İşlem güncelle
  updateTransaction: async (transactionId, transactionData) => {
    try {
      const response = await apiClient.put(`/api/user/office/transactions/income-expense/${transactionId}`, transactionData);
      return response.data;
    } catch (error) {
      console.error(`Update transaction ${transactionId} error:`, error);
      throw error;
    }
  },
  
  // İşlem sil
  deleteTransaction: async (transactionId) => {
    try {
      const response = await apiClient.delete(`/api/user/office/transactions/income-expense/${transactionId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete transaction ${transactionId} error:`, error);
      throw error;
    }
  },
  
  // Para birimlerini getir
  getCurrencies: async () => {
    try {
      const response = await apiClient.get('/api/user/office/transactions/income-expense/currencies');
      return response.data;
    } catch (error) {
      console.error('Get currencies error:', error);
      throw error;
    }
  },
  
  // İşlem türlerini getir
  getTransactionTypes: async (category = null) => {
    try {
      let url = '/api/user/office/transactions/types';
      if (category) {
        url += `?category=${category}`;
      }
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error('Get transaction types error:', error);
      throw error;
    }
  },
  
  // İşlem türü oluştur
  createTransactionType: async (typeData) => {
    try {
      const response = await apiClient.post('/api/user/office/transactions/types', typeData);
      return response.data;
    } catch (error) {
      console.error('Create transaction type error:', error);
      throw error;
    }
  },
  
  // İşlem türü güncelle
  updateTransactionType: async (typeId, typeData) => {
    try {
      const response = await apiClient.put(`/api/user/office/transactions/types/${typeId}`, typeData);
      return response.data;
    } catch (error) {
      console.error(`Update transaction type ${typeId} error:`, error);
      throw error;
    }
  },
  
  // İşlem türü sil
  deleteTransactionType: async (typeId) => {
    try {
      const response = await apiClient.delete(`/api/user/office/transactions/types/${typeId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete transaction type ${typeId} error:`, error);
      throw error;
    }
  }
};

export default transactionService;
