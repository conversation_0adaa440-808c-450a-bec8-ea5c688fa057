import apiClient from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tokenManager from '../utils/tokenManager';

// Dava servisi
const caseService = {
  // Dava detayı ekle
  addCaseDetails: async (caseDetails) => {
    try {
      console.log('Adding case details:', caseDetails);

      // Ensure all required fields are present
      if (!caseDetails.caseNumber) {
        throw new Error('Case number is required');
      }

      if (!caseDetails.caseType) {
        throw new Error('Case type is required');
      }

      if (!caseDetails.caseTitle) {
        throw new Error('Case title is required');
      }

      // If it's a criminal case, ensure crime type is present
      if (caseDetails.caseType === 'CEZA_DAVASI' && !caseDetails.crimeType) {
        throw new Error('Crime type is required for criminal cases');
      }

      // Ensure crimeType is an empty string, not null, when not a criminal case
      if (caseDetails.caseType !== 'CEZA_DAVASI') {
        caseDetails.crimeType = null;
      }

      // Ensure caseReason is an empty string, not null
      caseDetails.caseReason = caseDetails.caseReason || "";

      // Format the case number consistently
      if (caseDetails.caseNumber && caseDetails.caseNumber.includes('-')) {
        caseDetails.caseNumber = caseDetails.caseNumber.replace(/-/g, '/');
      }

      // Use apiClient instead of direct fetch to ensure consistent behavior between web and mobile
      const response = await apiClient.post('/api/user/case-details', caseDetails);

      console.log('Add case details response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Add case details error:', error);

      // Improve error handling for debugging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);

        // Try to get more detailed error information
        let errorMessage = `API error: ${error.response.status}`;
        if (error.response.data) {
          errorMessage = error.response.data.message || error.response.data.error || errorMessage;
        }
        throw new Error(errorMessage);
      } else if (error.request) {
        console.error('Error request:', error.request);
        throw new Error('No response received from server');
      } else {
        console.error('Error message:', error.message);
        throw error;
      }
    }
  },

  // Dava detayını getir (Dosya numarası ile)
  getCaseDetailsByCaseNumber: async (caseNumber) => {
    try {
      // Ensure case number is properly formatted (replace dash with slash if needed)
      let formattedCaseNumber = caseNumber;
      if (formattedCaseNumber && formattedCaseNumber.includes('-')) {
        formattedCaseNumber = formattedCaseNumber.replace('-', '/');
      }

      console.log(`Fetching case details for case number: ${formattedCaseNumber}`);
      // Use the correct API endpoint format with the caseNumber as a query parameter
      const url = `http://193.35.154.97:4244/api/user/case-details/by-case-number?caseNumber=${encodeURIComponent(formattedCaseNumber)}`;
      console.log('API URL:', url);

      // Make a direct fetch request to ensure we're using the correct URL
      const token = await tokenManager.getToken();
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        console.error(`API error when fetching case details: ${response.status} ${response.statusText}`);
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      console.log('Case details response:', data);
      return data;
    } catch (error) {
      console.error(`Get case details by case number ${caseNumber} error:`, error);
      throw error;
    }
  },

  // Dava türleri ve suç türlerini getir
  getCaseAndCrimeTypes: async () => {
    try {
      console.log('Fetching case and crime types');

      // Use apiClient instead of direct fetch to ensure consistent behavior between web and mobile
      const response = await apiClient.get('/api/user/case-details/types');

      console.log('Case and crime types response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Get case and crime types error:', error);

      // Improve error handling for debugging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      throw error;
    }
  },
  // Kullanıcının davalarını getir (aktif veya arşiv)
  getUserCases: async (active = true) => {
    try {
      const response = await apiClient.get(`/api/user/cases?active=${active}`);
      return response.data;
    } catch (error) {
      console.error(`Get user cases (active=${active}) error:`, error);
      throw error;
    }
  },

  // Dava detayını getir (ID ile)
  getCaseById: async (id) => {
    try {
      console.log(`Fetching case with ID: ${id}`);

      // Use apiClient instead of direct fetch to ensure consistent behavior between web and mobile
      const response = await apiClient.get(`/api/user/cases/by-id?id=${encodeURIComponent(id)}`);

      console.log('Case data response:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Get case ${id} error:`, error);

      // Improve error handling for debugging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      throw error;
    }
  },



  // Dava detayını getir (Dosya ID'si ile)
  getCaseDetail: async (caseId) => {
    try {
      const response = await apiClient.get(`/api/user/cases/report?caseNumber=${caseId}`);
      return response.data;
    } catch (error) {
      // Hata durumunda sadece hata mesajını göster
      if (__DEV__) {
        console.error(`Get case detail error: ${error.message || 'Unknown error'}`);
      }
      throw error;
    }
  },

  // Dava taraflarını getir (Dosya numarası ile)
  getCaseParties: async (caseNumber) => {
    try {
      const response = await apiClient.get(`/api/user/cases/taraflar?caseNumber=${caseNumber}`);
      return response.data;
    } catch (error) {
      // Hata durumunda sadece hata mesajını göster
      if (__DEV__) {
        console.error(`Get case parties error: ${error.message || 'Unknown error'}`);
      }
      throw error;
    }
  },

  // Tüm davaların taraflarını getir
  getAllCaseParties: async () => {
    try {
      console.log('Fetching all case parties from API...');

      // Önce apiClient ile deneyelim
      try {
        console.log('Trying with apiClient first...');
        const response = await apiClient.get('/api/user/cases/taraflar-all');

        console.log('API response status with apiClient:', response.status);
        console.log('API response data type:', typeof response.data);

        // API yanıtını detaylı logla
        console.log('DETAILED API RESPONSE:', JSON.stringify(response.data, null, 2).substring(0, 2000) + '...');

        // Yanıt formatını kontrol et - daha detaylı loglama ve hata işleme
        if (response.data) {
          console.log('API response received, analyzing structure...');

          // Log the full structure of the response for debugging
          try {
            const responseKeys = Object.keys(response.data);
            console.log('Response data keys:', responseKeys);

            // If the response has a data property, check its structure
            if (response.data.data) {
              console.log('Response has data property of type:', typeof response.data.data);
              if (Array.isArray(response.data.data)) {
                console.log('data property is an array with length:', response.data.data.length);
              }
            }

            // If the response has a taraflar property, check its structure
            if (response.data.taraflar) {
              console.log('Response has taraflar property of type:', typeof response.data.taraflar);
              if (Array.isArray(response.data.taraflar)) {
                console.log('taraflar property is an array with length:', response.data.taraflar.length);
              }
            }
          } catch (structureError) {
            console.error('Error analyzing response structure:', structureError);
          }

          // Eğer veri bir dizi değilse ve data özelliği varsa, data özelliğini kullan
          if (!Array.isArray(response.data) && response.data && Array.isArray(response.data.data)) {
            console.log('Using data.data instead of data');
            console.log('data.data length:', response.data.data.length);
            console.log('First few items:', JSON.stringify(response.data.data.slice(0, 3), null, 2));

            // Tarafları dosya numarasına göre grupla
            const result = {};
            response.data.data.forEach(party => {
              if (party.dosyaNo) {
                const dosyaNo = party.dosyaNo.trim();
                if (!result[dosyaNo]) {
                  result[dosyaNo] = [];
                }
                result[dosyaNo].push(party);
              }
            });

            console.log('Grouped by dosyaNo, total cases:', Object.keys(result).length);
            console.log('Case numbers:', Object.keys(result));

            return result;
          }

          // Eğer veri bir dizi ise, doğrudan kullan
          if (Array.isArray(response.data)) {
            console.log('Response is an array with length:', response.data.length);
            console.log('First few items:', JSON.stringify(response.data.slice(0, 3), null, 2));

            // Tarafları dosya numarasına göre grupla
            const result = {};
            response.data.forEach(party => {
              if (party.dosyaNo) {
                const dosyaNo = party.dosyaNo.trim();
                if (!result[dosyaNo]) {
                  result[dosyaNo] = [];
                }
                result[dosyaNo].push(party);
              }
            });

            console.log('Grouped by dosyaNo, total cases:', Object.keys(result).length);
            console.log('Case numbers:', Object.keys(result));

            return result;
          }

          // Eğer veri bir obje ise ve taraflar içeriyorsa
          if (!Array.isArray(response.data) && typeof response.data === 'object') {
            console.log('Response is an object, checking for parties data');

            // Obje içinde dosya numaralarına göre gruplandırılmış taraflar olabilir
            const result = {};
            let hasData = false;

            for (const key in response.data) {
              if (Array.isArray(response.data[key])) {
                result[key] = response.data[key];
                hasData = true;
                console.log(`Found array for key ${key} with length:`, response.data[key].length);
              }
            }

            if (hasData) {
              console.log('Found grouped parties data in response');
              console.log('Total cases:', Object.keys(result).length);
              console.log('Case numbers:', Object.keys(result));
              return result;
            }
          }

          // Eğer veri bir obje ise ve taraflar özelliği varsa
          if (!Array.isArray(response.data) && typeof response.data === 'object' && response.data.taraflar) {
            console.log('Response has taraflar property');

            if (Array.isArray(response.data.taraflar)) {
              console.log('taraflar is an array with length:', response.data.taraflar.length);
              console.log('First few items:', JSON.stringify(response.data.taraflar.slice(0, 3), null, 2));

              // Tarafları dosya numarasına göre grupla
              const result = {};
              response.data.taraflar.forEach(party => {
                if (party.dosyaNo) {
                  if (!result[party.dosyaNo]) {
                    result[party.dosyaNo] = [];
                  }
                  result[party.dosyaNo].push(party);
                }
              });

              console.log('Grouped by dosyaNo, total cases:', Object.keys(result).length);
              console.log('Case numbers:', Object.keys(result));

              return result;
            }
          }

          console.log('Returning raw response data');
          return response.data;
        }
      } catch (apiClientError) {
        console.error('Error with apiClient, falling back to fetch:', apiClientError);
      }

      // apiClient başarısız olursa, doğrudan fetch ile deneyelim
      console.log('Falling back to direct fetch...');
      const token = await tokenManager.getToken();
      const url = 'http://193.35.154.97:4244/api/user/cases/taraflar-all';
      console.log('API URL for all case parties:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API response status with fetch:', response.status);
      console.log('API response data type:', typeof data);

      // API yanıtını detaylı logla
      console.log('DETAILED API RESPONSE (fetch):', JSON.stringify(data, null, 2).substring(0, 2000) + '...');

      // Yanıt formatını kontrol et - daha detaylı loglama ve hata işleme
      console.log('Direct fetch API response received, analyzing structure...');

      // Log the full structure of the response for debugging
      try {
        if (data && typeof data === 'object') {
          const responseKeys = Object.keys(data);
          console.log('Response data keys:', responseKeys);

          // If the response has a data property, check its structure
          if (data.data) {
            console.log('Response has data property of type:', typeof data.data);
            if (Array.isArray(data.data)) {
              console.log('data property is an array with length:', data.data.length);
            }
          }

          // If the response has a taraflar property, check its structure
          if (data.taraflar) {
            console.log('Response has taraflar property of type:', typeof data.taraflar);
            if (Array.isArray(data.taraflar)) {
              console.log('taraflar property is an array with length:', data.taraflar.length);
            }
          }
        }
      } catch (structureError) {
        console.error('Error analyzing response structure:', structureError);
      }

      // Eğer veri bir dizi değilse ve data özelliği varsa, data özelliğini kullan
      if (!Array.isArray(data) && data && Array.isArray(data.data)) {
        console.log('Using data.data instead of data');
        console.log('data.data length:', data.data.length);
        console.log('First few items:', JSON.stringify(data.data.slice(0, 3), null, 2));

        // Tarafları dosya numarasına göre grupla
        const result = {};
        data.data.forEach(party => {
          if (party.dosyaNo) {
            const dosyaNo = party.dosyaNo.trim();
            if (!result[dosyaNo]) {
              result[dosyaNo] = [];
            }
            result[dosyaNo].push(party);
          }
        });

        console.log('Grouped by dosyaNo, total cases:', Object.keys(result).length);
        console.log('Case numbers:', Object.keys(result));

        return result;
      }

      // Eğer veri bir dizi ise, doğrudan kullan
      if (Array.isArray(data)) {
        console.log('Response is an array with length:', data.length);
        console.log('First few items:', JSON.stringify(data.slice(0, 3), null, 2));

        // Tarafları dosya numarasına göre grupla
        const result = {};
        data.forEach(party => {
          if (party.dosyaNo) {
            const dosyaNo = party.dosyaNo.trim();
            if (!result[dosyaNo]) {
              result[dosyaNo] = [];
            }
            result[dosyaNo].push(party);
          }
        });

        console.log('Grouped by dosyaNo, total cases:', Object.keys(result).length);
        console.log('Case numbers:', Object.keys(result));

        return result;
      }

      // Eğer veri bir obje ise ve taraflar içeriyorsa
      if (!Array.isArray(data) && typeof data === 'object') {
        console.log('Response is an object, checking for parties data');

        // Obje içinde dosya numaralarına göre gruplandırılmış taraflar olabilir
        const result = {};
        let hasData = false;

        for (const key in data) {
          if (Array.isArray(data[key])) {
            result[key] = data[key];
            hasData = true;
            console.log(`Found array for key ${key} with length:`, data[key].length);
          }
        }

        if (hasData) {
          console.log('Found grouped parties data in response');
          console.log('Total cases:', Object.keys(result).length);
          console.log('Case numbers:', Object.keys(result));
          return result;
        }
      }

      // Eğer veri bir obje ise ve taraflar özelliği varsa
      if (!Array.isArray(data) && typeof data === 'object' && data.taraflar) {
        console.log('Response has taraflar property');

        if (Array.isArray(data.taraflar)) {
          console.log('taraflar is an array with length:', data.taraflar.length);
          console.log('First few items:', JSON.stringify(data.taraflar.slice(0, 3), null, 2));

          // Tarafları dosya numarasına göre grupla
          const result = {};
          data.taraflar.forEach(party => {
            if (party.dosyaNo) {
              if (!result[party.dosyaNo]) {
                result[party.dosyaNo] = [];
              }
              result[party.dosyaNo].push(party);
            }
          });

          console.log('Grouped by dosyaNo, total cases:', Object.keys(result).length);
          console.log('Case numbers:', Object.keys(result));

          return result;
        }
      }

      console.log('Returning raw data');
      return data;
    } catch (error) {
      console.error(`Get all case parties error: ${error.message || 'Unknown error'}`);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      }

      // Log the error but don't return an empty object immediately
      // Try to extract any partial data that might be available
      console.log('Error occurred, but checking if we have any partial data before returning empty object');

      // If we have any data in the error response, try to use it
      if (error.response && error.response.data) {
        console.log('Found data in error response, attempting to use it');
        try {
          const errorData = error.response.data;

          // Check if the error response contains any usable data
          if (Array.isArray(errorData)) {
            console.log('Error response contains an array, using it');
            return errorData;
          } else if (typeof errorData === 'object' && errorData !== null) {
            console.log('Error response contains an object, using it');
            return errorData;
          }
        } catch (extractError) {
          console.error('Error extracting data from error response:', extractError);
        }
      }

      // If all else fails, return an empty object so the app doesn't crash
      console.log('Returning empty object due to error - no usable data found');
      return {};
    }
  },

  // Dava geçmişini getir
  getCaseHistory: async (caseNumber) => {
    try {
      const response = await apiClient.get(`/api/user/cases/safahat?caseNumber=${caseNumber}`);
      return response.data;
    } catch (error) {
      // Hata durumunda sadece hata mesajını göster
      if (__DEV__) {
        console.error(`Get case history error: ${error.message || 'Unknown error'}`);
      }
      throw error;
    }
  },

  // Safahat bilgisini getir (with caching to prevent rate limiting)
  getSafahatInfo: async (caseNumber) => {
    try {
      console.log(`Fetching safahat info for case number: ${caseNumber}`);

      // Check if we have cached data and when it was last fetched
      const cacheKey = `safahat_cache_${caseNumber}`;
      let shouldFetch = true;
      let cachedData = null;

      try {
        const cachedString = await AsyncStorage.getItem(cacheKey);
        if (cachedString) {
          const cache = JSON.parse(cachedString);
          const now = new Date().getTime();
          const cacheTime = cache.timestamp;

          // Cache is valid for 115 minutes (just under the 120 minute rate limit)
          const cacheValidityPeriod = 115 * 60 * 1000; // 115 minutes in milliseconds

          if (now - cacheTime < cacheValidityPeriod) {
            console.log('Using cached safahat data (cache is still valid)');
            shouldFetch = false;
            cachedData = cache.data;
          } else {
            console.log('Cache expired, fetching fresh safahat data');
          }
        }
      } catch (cacheError) {
        console.error('Error reading from cache:', cacheError);
        // Continue with fetch if cache read fails
      }

      // Return cached data if valid
      if (!shouldFetch && cachedData) {
        return cachedData;
      }

      // If no valid cache, make the API request
      console.log(`API URL: /api/user/cases/safahat?caseNumber=${caseNumber}`);

      // Use apiClient instead of direct fetch to ensure consistent behavior between web and mobile
      let data;
      try {
        const response = await apiClient.get(`/api/user/cases/safahat?caseNumber=${encodeURIComponent(caseNumber)}`);

        // Data is already parsed by axios
        data = response.data;

        // If the response is a string, try to parse it as JSON
        if (typeof data === 'string') {
          try {
            data = JSON.parse(data);
          } catch (parseError) {
            console.error('Error parsing safahat response:', parseError);
            // Keep as string if parsing fails
          }
        }
      } catch (error) {
        // If we get a rate limit error, try to use cached data even if expired
        if (error.response && error.response.status === 429 && cachedData) {
          console.log('Rate limited, using expired cached data');
          return {
            ...cachedData,
            error: "Bu işlem 120 dakikada 1 defa yapılabilmektedir. Önceki veri gösteriliyor.",
            isExpiredCache: true
          };
        }
        throw error;
      }

      // Cache the successful response
      try {
        await AsyncStorage.setItem(cacheKey, JSON.stringify({
          data: data,
          timestamp: new Date().getTime()
        }));
        console.log('Safahat data cached successfully');
      } catch (cacheError) {
        console.error('Error caching safahat data:', cacheError);
        // Continue even if caching fails
      }

      console.log('Safahat API response data:', data);
      return data;
    } catch (error) {
      console.error(`Get safahat info error: ${error.message || 'Unknown error'}`);

      // Try to return cached data as fallback, even if expired
      try {
        const cacheKey = `safahat_cache_${caseNumber}`;
        const cachedString = await AsyncStorage.getItem(cacheKey);
        if (cachedString) {
          const cache = JSON.parse(cachedString);
          console.log('Returning cached data as fallback after error');
          return {
            ...cache.data,
            error: "Veri alınırken hata oluştu. Önceki veri gösteriliyor.",
            isExpiredCache: true
          };
        }
      } catch (cacheError) {
        console.error('Error reading from cache as fallback:', cacheError);
      }

      throw error;
    }
  },

  // Dava tahsilat ve reddiyat bilgilerini getir
  getCaseCollectionAndDenial: async (caseNumber) => {
    try {
      console.log(`Fetching tahsilat-reddiyat for case number: ${caseNumber}`);
      const response = await apiClient.get(`/api/user/cases/tahsilat-reddiyat?caseNumber=${caseNumber}`);
      console.log('Tahsilat-reddiyat response:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Get tahsilat-reddiyat error: ${error.message || 'Unknown error'}`);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      }
      throw error;
    }
  },

  // UYAP ile senkronizasyon
  syncToUyap: async (jsid) => {
    try {
      const response = await apiClient.post('/api/user/sync', { jsid });
      return response.data;
    } catch (error) {
      console.error('Sync to UYAP error:', error);
      throw error;
    }
  },

  // Dava detayını sil (ID ile)
  deleteCaseDetail: async (id) => {
    try {
      console.log(`Deleting case detail with ID: ${id}`);

      // Use apiClient instead of direct fetch to ensure consistent behavior between web and mobile
      const response = await apiClient.delete(`/api/user/case-details/${id}`);

      console.log('Delete case detail response:', response.data);
      return response.data || { success: true };
    } catch (error) {
      console.error(`Delete case detail ${id} error:`, error);

      // Improve error handling for debugging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      throw error;
    }
  },

  // Dava detayını güncelle (ID ile)
  updateCaseDetail: async (id, caseDetails) => {
    try {
      console.log(`Updating case detail with ID: ${id}`, caseDetails);

      // Ensure all required fields are present
      if (!caseDetails.caseNumber) {
        throw new Error('Case number is required');
      }

      if (!caseDetails.caseType) {
        throw new Error('Case type is required');
      }

      if (!caseDetails.caseTitle) {
        throw new Error('Case title is required');
      }

      // If it's a criminal case, ensure crime type is present
      if (caseDetails.caseType === 'CEZA_DAVASI' && !caseDetails.crimeType) {
        throw new Error('Crime type is required for criminal cases');
      }

      // Ensure crimeType is an empty string, not null, when not a criminal case
      if (caseDetails.caseType !== 'CEZA_DAVASI') {
        caseDetails.crimeType = null;
      }

      // Ensure caseReason is an empty string, not null
      caseDetails.caseReason = caseDetails.caseReason || "";

      // Use apiClient instead of direct fetch to ensure consistent behavior between web and mobile
      const response = await apiClient.put(`/api/user/case-details/${id}`, caseDetails);

      console.log('Update case detail response:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Update case detail ${id} error:`, error);

      // Improve error handling for debugging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      throw error;
    }
  }
};

export default caseService;
