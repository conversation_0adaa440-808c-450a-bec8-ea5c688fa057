import { StyleSheet, ScrollView, TouchableOpacity, View, TextInput, Alert, Platform, ActivityIndicator, Modal, SafeAreaView, FlatList, ImageBackground } from 'react-native';
import React, { useState, useEffect, useMemo } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import taskService from '@/services/taskService';
import caseService from '@/services/caseService';
import { useCaseParties } from '@/contexts/CasePartiesContext';
import BackgroundWrapper from '@/components/BackgroundWrapper';

// Define types for our data
interface CaseItem {
  id: string;
  title: string;
  dosyaNo: string;
  birimAdi?: string;
  dosyaTur?: string;
}

interface ClientItem {
  id: string;
  name: string;
  role?: string;
  type?: string;
}

interface PartyItem {
  id: string;
  name: string;
  role?: string;
  type?: string;
  tarafId?: string;
  adi?: string;
  tarafAdi?: string;
  ad?: string;
  tarafAd?: string;
  tarafSoyad?: string;
  rol?: string;
  tarafTur?: string;
  tarafTipi?: string;
  kisiKurum?: string;
  tarafTip?: string;
}

// Fallback kişi ve dava verileri (API bağlantısı başarısız olduğunda kullanılır)
const CLIENTS_DATA: ClientItem[] = [];
const CASES_DATA: CaseItem[] = [];

export default function AddTaskScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [taskType, setTaskType] = useState('GÖREV');
  const [priority, setPriority] = useState('ORTA');
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(new Date(new Date().setDate(new Date().getDate() + 1)));
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [dateTimePickerMode, setDateTimePickerMode] = useState('start'); // 'start' veya 'due'
  const [showAndroidDatePicker, setShowAndroidDatePicker] = useState(false);
  const [showAndroidTimePicker, setShowAndroidTimePicker] = useState(false);
  const [location, setLocation] = useState('');
  const [repeat, setRepeat] = useState(0); // Saat cinsinden tekrarlama
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [selectedCase, setSelectedCase] = useState<string | null>(null);

  // Dava taraflarını almak için context'i kullan
  const { allParties, loading: partiesLoading, fetchPartiesIfNeeded } = useCaseParties();

  // Seçilen davanın tarafları
  const [caseParties, setCaseParties] = useState<PartyItem[]>([]);
  const [showClientPicker, setShowClientPicker] = useState(false);
  const [showCasePicker, setShowCasePicker] = useState(false);

  // API state
  const [loading, setLoading] = useState(false);
  const [loadingCases, setLoadingCases] = useState(true);
  const [loadingClients, setLoadingClients] = useState(true);
  const [cases, setCases] = useState<CaseItem[]>([]);
  const [clients, setClients] = useState<ClientItem[]>([]);
  const [casesError, setCasesError] = useState('');

  // Dava listesi sayfalama ve filtreleme için state'ler
  const [currentPage, setCurrentPage] = useState(1);
  const [caseFilter, setCaseFilter] = useState('');
  const casesPerPage = 5;

  // Dava türüne göre renk belirleme fonksiyonu
  const getCaseTypeColor = (caseType: string | undefined): string => {
    if (!caseType) return '#64748b'; // Varsayılan gri

    const type = caseType.toLowerCase();
    if (type.includes('ceza')) return '#EF4444'; // Kırmızı
    if (type.includes('hukuk')) return '#10B981'; // Yeşil
    if (type.includes('idari')) return '#F59E0B'; // Turuncu
    if (type.includes('icra')) return '#3B82F6'; // Mavi
    if (type.includes('vergi')) return '#8B5CF6'; // Mor

    return '#64748b'; // Varsayılan gri
  };

  // Filtrelenmiş davaları hesapla
  const filteredCases = useMemo(() => {
    if (!caseFilter) return cases;

    const searchLower = caseFilter.toLowerCase();
    return cases.filter(caseItem =>
      (caseItem.title?.toLowerCase() || '').includes(searchLower) ||
      (caseItem.dosyaNo?.toLowerCase() || '').includes(searchLower) ||
      (caseItem.birimAdi?.toLowerCase() || '').includes(searchLower) ||
      (caseItem.dosyaTur?.toLowerCase() || '').includes(searchLower)
    );
  }, [cases, caseFilter]);

  // Sayfalanmış davaları hesapla
  const paginatedCases = useMemo(() => {
    const startIndex = (currentPage - 1) * casesPerPage;
    return filteredCases.slice(startIndex, startIndex + casesPerPage);
  }, [filteredCases, currentPage, casesPerPage]);

  // Toplam sayfa sayısı
  const totalPages = Math.ceil(filteredCases.length / casesPerPage);

  // Sayfalama işlemleri
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Tarafları yükle
  useEffect(() => {
    fetchPartiesIfNeeded();
  }, []);

  // Seçilen dava değiştiğinde tarafları güncelle
  useEffect(() => {
    if (selectedCase) {
      // Seçilen davanın bilgilerini bul
      const selectedCaseData = cases.find(c => c.id === selectedCase);
      if (selectedCaseData && selectedCaseData.dosyaNo) {
        // Davanın taraflarını bul
        let parties = allParties[selectedCaseData.dosyaNo] || [];

        // Eğer taraflar bulunamadıysa, tüm anahtarları kontrol et
        if (parties.length === 0) {
          for (const key of Object.keys(allParties)) {
            if (key === selectedCaseData.dosyaNo) {
              parties = allParties[key];
              break;
            }
          }
        }

        // Hala bulunamadıysa, dosyaNo'yu farklı formatlarda dene
        if (parties.length === 0) {
          // Dosya numarasını farklı formatlarda dene
          const alternativeFormats = [
            selectedCaseData.dosyaNo,
            selectedCaseData.dosyaNo.replace('/', '-'),
            selectedCaseData.dosyaNo.replace('-', '/'),
            selectedCaseData.dosyaNo.trim(),
          ];

          for (const format of alternativeFormats) {
            if (allParties[format] && allParties[format].length > 0) {
              console.log(`FOUND MATCH FOR ALTERNATIVE FORMAT: ${format}`);
              parties = allParties[format];
              break;
            }
          }
        }

        console.log(`Dava ${selectedCaseData.dosyaNo} için ${parties.length} taraf bulundu`);

        // Tarafları kişi formatına dönüştür
        const formattedParties = parties.map(party => {
          // Benzersiz bir ID oluştur
          const partyId = party.tarafId || party.id || `party-${Math.random().toString(36).substring(2, 9)}`;

          return {
            id: partyId,
            name: party.adi || party.tarafAdi || party.ad || `${party.tarafAd || ''} ${party.tarafSoyad || ''}`.trim() || 'Belirtilmemiş',
            role: party.rol || party.tarafTur || party.tarafTipi || 'Belirtilmemiş',
            type: party.kisiKurum || party.tarafTip || 'Belirtilmemiş'
          };
        });

        // Tarafları ayarla
        setCaseParties(formattedParties);

        // Eğer henüz bir kişi seçilmemişse ve taraflar varsa, ilk tarafı seç
        if (!selectedClient && formattedParties.length > 0) {
          setSelectedClient(formattedParties[0].id);
        }
      } else {
        setCaseParties([]);
      }
    } else {
      // Dava seçilmemişse tarafları temizle
      setCaseParties([]);
    }
  }, [selectedCase, allParties, cases]);

  // API'den dava listesini getir
  useEffect(() => {
    const fetchCases = async () => {
      try {
        setLoadingCases(true);
        setCasesError('');
        const data = await caseService.getUserCases(true);

        // API'den gelen veriyi işle
        if (data && Array.isArray(data) && data.length > 0) {
          // API'nin döndüğü formatı kontrol et ve uygun şekilde işle
          const casesData = data[0] || [];

          // Dava listesini oluştur
          const formattedCases = casesData.map((caseItem: any): CaseItem => ({
            id: caseItem.dosyaId || caseItem.id,
            title: `${caseItem.dosyaNo || ''} ${caseItem.birimAdi || ''}`.trim() || 'Belirtilmemiş',
            dosyaNo: caseItem.dosyaNo,
            birimAdi: caseItem.birimAdi,
            dosyaTur: caseItem.dosyaTur
          }));

          // Dosya numarasına göre sırala
          formattedCases.sort((a: CaseItem, b: CaseItem) => {
            if (!a.dosyaNo) return 1;
            if (!b.dosyaNo) return -1;
            return b.dosyaNo.localeCompare(a.dosyaNo);
          });

          setCases(formattedCases);
        } else {
          setCases([]);
        }

        setLoadingCases(false);
      } catch (err) {
        console.error('Error fetching cases:', err);
        setCasesError('Davalar yüklenirken bir hata oluştu.');
        setLoadingCases(false);
        // Geliştirme aşamasında örnek verileri kullan
        setCases(CASES_DATA);
      }
    };

    fetchCases();
  }, []);

  // Kişi listesini yükle - CasePartiesContext'ten alınıyor
  useEffect(() => {
    const loadClients = async () => {
      try {
        setLoadingClients(true);

        // Örnek verileri göster
        setClients(CLIENTS_DATA);

        // NOT: Artık API'ye istek atmak yerine CasePartiesContext'ten veri kullanıyoruz
        // Bu nedenle clientService.getAllClients() çağrısını kaldırdık
        console.log('Kişi verileri CasePartiesContext\'ten alınıyor');

        setLoadingClients(false);
      } catch (err) {
        console.error('Error loading clients:', err);
        setLoadingClients(false);
      }
    };

    loadClients();
  }, []);

  // Form validation
  const [errors, setErrors] = useState({
    title: '',
  });

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      title: '',
    };

    if (!title.trim()) {
      newErrors.title = 'Başlık alanı zorunludur';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = async () => {
    if (validateForm()) {
      try {
        setLoading(true);

        // Seçilen dava bilgilerini al
        const selectedCaseData = selectedCase ? cases.find(c => c.id === selectedCase) : null;

        // API'ye gönderilecek görev verisi - API'nin beklediği formata uygun
        // clientId ve type gönderilmeyecek
        const taskData: {
          title: string;
          description: string;
          priority: string;
          status: string;
          startDate: string;
          dueDate: string;
          caseNumber: string | null;
          location?: string;
        } = {
          title,
          description,
          priority: taskService.mapPriorityToApi(priority),
          status: 'OPEN',
          startDate: startDate.toISOString(),
          dueDate: dueDate.toISOString(),
          caseNumber: selectedCaseData?.dosyaNo || null // Dava numarası
        };

        // Sadece location alanını ekle
        if (location) {
          taskData.location = location;
        }

        // Seçilen dava ve kişi bilgilerini logla
        if (selectedCase) {
          const selectedCaseData = cases.find(c => c.id === selectedCase);
          console.log('Seçilen dava:', selectedCaseData);
        }

        if (selectedClient) {
          const selectedClientData = clients.find(c => c.id === selectedClient);
          console.log('Seçilen kişi:', selectedClientData);
        }

        // API'ye gönder
        console.log('Görev verisi gönderiliyor:', JSON.stringify(taskData, null, 2));
        const response = await taskService.createTask(taskData);

        console.log('API yanıtı:', JSON.stringify(response, null, 2));

        // Yanıtı kontrol et
        if (!response || !response.id) {
          throw new Error('Görev oluşturuldu ancak geçerli bir yanıt alınamadı.');
        }

        // Görev hatırlatıcısını planla
        try {
          const taskReminderService = require('@/services/taskReminderService').default;
          await taskReminderService.scheduleTaskReminder({
            id: response.id,
            title: response.title,
            description: response.description,
            dueDate: response.dueDate,
            type: taskType,
            priority: priority
          });
          console.log('Hatırlatıcı planlandı:', response.id);
        } catch (reminderErr) {
          console.error('Hatırlatıcı planlama hatası:', reminderErr);
          // Hatırlatıcı hatası görev oluşturmayı etkilemez
        }

        // Görevler sayfasına dönmeden önce bir flag ayarla
        // Bu flag, görevler sayfasına dönüldüğünde görevlerin yenilenmesi gerektiğini belirtir
        try {
          await AsyncStorage.setItem('refresh_tasks', 'true');
          console.log('Görevleri yenileme bayrağı ayarlandı');
        } catch (storageErr) {
          console.error('AsyncStorage error:', storageErr);
        }

        // Başarılı mesajı göster
        Alert.alert(
          'Başarılı',
          `Görev başarıyla eklendi. Görev ID: ${response.id}`,
          [
            {
              text: 'Tamam',
              onPress: () => router.back()
            }
          ]
        );
      } catch (err: any) {
        console.error('Görev oluşturma hatası:', err);

        // Hata detaylarını göster
        let errorMessage = 'Görev eklenirken bir hata oluştu. Lütfen tekrar deneyin.';
        let errorDetails = '';

        if (err.response) {
          // API yanıtı ile ilgili hata
          console.error('API hata yanıtı:', err.response.data);
          console.error('API hata durumu:', err.response.status);

          if (err.response.data && err.response.data.message) {
            errorMessage = err.response.data.message;
          }

          if (err.response.status === 400) {
            errorMessage = 'Görev bilgileri geçersiz. Lütfen tüm alanları kontrol edin.';
          } else if (err.response.status === 401) {
            errorMessage = 'Oturum süresi dolmuş olabilir. Lütfen yeniden giriş yapın.';
          } else if (err.response.status === 403) {
            errorMessage = 'Bu işlemi yapmaya yetkiniz yok.';
          } else if (err.response.status === 404) {
            errorMessage = 'Görev oluşturma servisi bulunamadı.';
          } else if (err.response.status === 500) {
            errorMessage = 'Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.';
          }

          // Hata detaylarını ekle
          if (err.response.data && typeof err.response.data === 'object') {
            errorDetails = JSON.stringify(err.response.data, null, 2);
          }
        } else if (err.request) {
          // İstek yapıldı ama yanıt alınamadı
          console.error('API yanıt vermedi:', err.request);
          errorMessage = 'Sunucudan yanıt alınamadı. İnternet bağlantınızı kontrol edin.';
        } else {
          // İstek oluşturulurken hata
          console.error('Görev oluşturma hata mesajı:', err.message);
          errorMessage = `Hata: ${err.message}`;
        }

        // Hata mesajını göster
        Alert.alert(
          'Görev Eklenemedi',
          errorMessage,
          [{ text: 'Tamam' }]
        );

        // Hata detaylarını konsola yazdır
        if (errorDetails) {
          console.error('Hata detayları:', errorDetails);
        }
      } finally {
        setLoading(false);
      }
    }
  };



  return (
    <ThemedView style={styles.container}>
      <BackgroundWrapper>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
          </TouchableOpacity>
          <ThemedText type="title">Görev Ekle</ThemedText>
          <View style={{width: 24}} />
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <ThemedText style={styles.sectionTitle}>Görev Bilgileri</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>
              Başlık
              <ThemedText style={styles.requiredStar}> *</ThemedText>
            </ThemedText>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.title ? styles.inputError : null
              ]}
              placeholder="Görev başlığı giriniz"
              placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
              value={title}
              onChangeText={(text) => {
                setTitle(text);
                if (text.trim()) {
                  setErrors({...errors, title: ''});
                }
              }}
            />
            {errors.title ? <ThemedText style={styles.errorText}>{errors.title}</ThemedText> : null}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Açıklama</ThemedText>
            <TextInput
              style={[
                styles.input,
                styles.textArea,
                isDark && styles.inputDark
              ]}
              placeholder="Görev açıklaması giriniz"
              placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Görev Tipi</ThemedText>
            <View style={styles.segmentedControl}>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  taskType === 'GÖREV' && styles.segmentButtonActive
                ]}
                onPress={() => setTaskType('GÖREV')}
              >
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={taskType === 'GÖREV' ? 'white' : Colors[colorScheme ?? 'light'].tint}
                />
                <ThemedText style={taskType === 'GÖREV' ? styles.segmentTextActive : null}>
                  Görev
                </ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  taskType === 'DURUŞMA' && styles.segmentButtonActive
                ]}
                onPress={() => setTaskType('DURUŞMA')}
              >
                <Ionicons
                  name="briefcase"
                  size={16}
                  color={taskType === 'DURUŞMA' ? 'white' : Colors[colorScheme ?? 'light'].tint}
                />
                <ThemedText style={taskType === 'DURUŞMA' ? styles.segmentTextActive : null}>
                  Duruşma
                </ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  taskType === 'TOPLANTI' && styles.segmentButtonActive
                ]}
                onPress={() => setTaskType('TOPLANTI')}
              >
                <Ionicons
                  name="people"
                  size={16}
                  color={taskType === 'TOPLANTI' ? 'white' : Colors[colorScheme ?? 'light'].tint}
                />
                <ThemedText style={taskType === 'TOPLANTI' ? styles.segmentTextActive : null}>
                  Toplantı
                </ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  taskType === 'HATIRLATMA' && styles.segmentButtonActive
                ]}
                onPress={() => setTaskType('HATIRLATMA')}
              >
                <Ionicons
                  name="alarm"
                  size={16}
                  color={taskType === 'HATIRLATMA' ? 'white' : Colors[colorScheme ?? 'light'].tint}
                />
                <ThemedText style={taskType === 'HATIRLATMA' ? styles.segmentTextActive : null}>
                  Hatırlatma
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <ThemedText style={styles.sectionTitle}>Öncelik ve Durum</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Öncelik Seviyesi</ThemedText>
            <View style={styles.prioritySelector}>
              <TouchableOpacity
                style={[
                  styles.priorityButton,
                  priority === 'DÜŞÜK' && styles.priorityButtonActive,
                  { backgroundColor: priority === 'DÜŞÜK' ? '#10B981' : 'rgba(16, 185, 129, 0.1)' }
                ]}
                onPress={() => setPriority('DÜŞÜK')}
              >
                <ThemedText style={[
                  styles.priorityText,
                  { color: priority === 'DÜŞÜK' ? 'white' : '#10B981' }
                ]}>
                  DÜŞÜK
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.priorityButton,
                  priority === 'ORTA' && styles.priorityButtonActive,
                  { backgroundColor: priority === 'ORTA' ? '#F59E0B' : 'rgba(245, 158, 11, 0.1)' }
                ]}
                onPress={() => setPriority('ORTA')}
              >
                <ThemedText style={[
                  styles.priorityText,
                  { color: priority === 'ORTA' ? 'white' : '#F59E0B' }
                ]}>
                  ORTA
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.priorityButton,
                  priority === 'YÜKSEK' && styles.priorityButtonActive,
                  { backgroundColor: priority === 'YÜKSEK' ? '#EF4444' : 'rgba(239, 68, 68, 0.1)' }
                ]}
                onPress={() => setPriority('YÜKSEK')}
              >
                <ThemedText style={[
                  styles.priorityText,
                  { color: priority === 'YÜKSEK' ? 'white' : '#EF4444' }
                ]}>
                  YÜKSEK
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.priorityButton,
                  priority === 'KRİTİK' && styles.priorityButtonActive,
                  { backgroundColor: priority === 'KRİTİK' ? '#7C3AED' : 'rgba(124, 58, 237, 0.1)' }
                ]}
                onPress={() => setPriority('KRİTİK')}
              >
                <ThemedText style={[
                  styles.priorityText,
                  { color: priority === 'KRİTİK' ? 'white' : '#7C3AED' }
                ]}>
                  KRİTİK
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <ThemedText style={styles.sectionTitle}>Tarih ve Konum</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Tarih ve Saat</ThemedText>
            <View style={styles.dateTimeContainer}>
              {/* Başlangıç Tarihi */}
              <View style={styles.dateTimeItem}>
                <ThemedText style={styles.dateTimeLabel}>Başlangıç</ThemedText>
                <TouchableOpacity
                  style={[
                    styles.input,
                    styles.dateInput,
                    isDark && { backgroundColor: '#1F2937', color: '#fff' }
                  ].filter(Boolean)}
                  onPress={() => {
                    setDateTimePickerMode('start');
                    setShowDateTimePicker(true);
                  }}
                >
                  <ThemedText>
                    {startDate.toLocaleDateString('tr-TR')} {startDate.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                  </ThemedText>
                  <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              </View>

              {/* Bitiş Tarihi */}
              <View style={styles.dateTimeItem}>
                <ThemedText style={styles.dateTimeLabel}>Bitiş</ThemedText>
                <TouchableOpacity
                  style={[
                    styles.input,
                    styles.dateInput,
                    isDark && { backgroundColor: '#1F2937', color: '#fff' }
                  ].filter(Boolean)}
                  onPress={() => {
                    setDateTimePickerMode('due');
                    setShowDateTimePicker(true);
                  }}
                >
                  <ThemedText>
                    {dueDate.toLocaleDateString('tr-TR')} {dueDate.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                  </ThemedText>
                  <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Mobil için DateTimePicker - Modal içinde göster */}
            {Platform.OS !== 'web' && (
              <Modal
                visible={showDateTimePicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowDateTimePicker(false)}
              >
                <View style={styles.dateTimePickerModalContainer}>
                  <View style={[styles.dateTimePickerModalContent, isDark && styles.dateTimePickerModalContentDark]}>
                    <View style={styles.dateTimePickerModalHeader}>
                      <ThemedText style={styles.dateTimePickerModalTitle}>
                        {dateTimePickerMode === 'start' ? 'Başlangıç Tarihi ve Saati' : 'Bitiş Tarihi ve Saati'}
                      </ThemedText>
                      <TouchableOpacity onPress={() => setShowDateTimePicker(false)}>
                        <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.dateTimePickerContainer}>
                      {Platform.OS === 'ios' ? (
                        <DateTimePicker
                          value={dateTimePickerMode === 'start' ? startDate : dueDate}
                          mode="datetime"
                          display="spinner"
                          onChange={(_event: any, selectedDate?: Date) => {
                            if (selectedDate) {
                              if (dateTimePickerMode === 'start') {
                                setStartDate(selectedDate);
                              } else {
                                setDueDate(selectedDate);
                              }
                            }
                          }}
                          style={styles.iosDateTimePicker}
                        />
                      ) : (
                        // Android için tarih ve saat seçimi
                        <View style={styles.androidPickerContainer}>
                          <ThemedText style={styles.androidPickerLabel}>Tarih</ThemedText>
                          <View style={styles.androidPickerValue}>
                            <ThemedText>
                              {(dateTimePickerMode === 'start' ? startDate : dueDate).toLocaleDateString('tr-TR')}
                            </ThemedText>
                          </View>

                          <TouchableOpacity
                            style={styles.androidPickerButton}
                            onPress={() => setShowAndroidDatePicker(true)}
                          >
                            <ThemedText style={styles.androidPickerButtonText}>Tarih Seç</ThemedText>
                          </TouchableOpacity>

                          <View style={styles.androidPickerSeparator} />

                          <ThemedText style={styles.androidPickerLabel}>Saat</ThemedText>
                          <View style={styles.androidPickerValue}>
                            <ThemedText>
                              {(dateTimePickerMode === 'start' ? startDate : dueDate).toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                            </ThemedText>
                          </View>

                          <TouchableOpacity
                            style={styles.androidPickerButton}
                            onPress={() => setShowAndroidTimePicker(true)}
                          >
                            <ThemedText style={styles.androidPickerButtonText}>Saat Seç</ThemedText>
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>

                    {/* Android için tarih seçici */}
                    {Platform.OS === 'android' && showAndroidDatePicker && (
                      <DateTimePicker
                        value={dateTimePickerMode === 'start' ? startDate : dueDate}
                        mode="date"
                        display="default"
                        onChange={(event, selectedDate) => {
                          setShowAndroidDatePicker(false);
                          if (event.type === 'set' && selectedDate) {
                            const newDate = new Date(dateTimePickerMode === 'start' ? startDate : dueDate);
                            newDate.setFullYear(selectedDate.getFullYear());
                            newDate.setMonth(selectedDate.getMonth());
                            newDate.setDate(selectedDate.getDate());

                            if (dateTimePickerMode === 'start') {
                              setStartDate(newDate);
                            } else {
                              setDueDate(newDate);
                            }
                          }
                        }}
                      />
                    )}

                    {/* Android için saat seçici */}
                    {Platform.OS === 'android' && showAndroidTimePicker && (
                      <DateTimePicker
                        value={dateTimePickerMode === 'start' ? startDate : dueDate}
                        mode="time"
                        display="default"
                        onChange={(event, selectedTime) => {
                          setShowAndroidTimePicker(false);
                          if (event.type === 'set' && selectedTime) {
                            const newDate = new Date(dateTimePickerMode === 'start' ? startDate : dueDate);
                            newDate.setHours(selectedTime.getHours());
                            newDate.setMinutes(selectedTime.getMinutes());

                            if (dateTimePickerMode === 'start') {
                              setStartDate(newDate);
                            } else {
                              setDueDate(newDate);
                            }
                          }
                        }}
                      />
                    )}

                    <View style={styles.dateTimePickerModalFooter}>
                      <TouchableOpacity
                        style={[styles.dateTimePickerModalButton, styles.dateTimePickerModalCancelButton]}
                        onPress={() => setShowDateTimePicker(false)}
                      >
                        <ThemedText style={styles.dateTimePickerModalButtonText}>İptal</ThemedText>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.dateTimePickerModalButton, styles.dateTimePickerModalConfirmButton]}
                        onPress={() => setShowDateTimePicker(false)}
                      >
                        <ThemedText style={[styles.dateTimePickerModalButtonText, styles.dateTimePickerModalConfirmButtonText]}>Tamam</ThemedText>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Modal>
            )}
          </View>

          {(taskType === 'DURUŞMA' || taskType === 'TOPLANTI') && (
            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>Konum</ThemedText>
              <TextInput
                style={[styles.input, isDark && styles.inputDark]}
                placeholder="Konum bilgisi giriniz"
                placeholderTextColor={isDark ? '#9ca3af' : '#6B7280'}
                value={location}
                onChangeText={setLocation}
              />
            </View>
          )}

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Tekrarlama</ThemedText>
            <View style={styles.repeatSelector}>
              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 0 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(0)}
              >
                <ThemedText style={repeat === 0 ? styles.repeatTextActive : null}>
                  Yok
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 24 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(24)}
              >
                <ThemedText style={repeat === 24 ? styles.repeatTextActive : null}>
                  Günlük
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 168 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(168)}
              >
                <ThemedText style={repeat === 168 ? styles.repeatTextActive : null}>
                  Haftalık
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.repeatButton,
                  repeat === 720 && styles.repeatButtonActive
                ]}
                onPress={() => setRepeat(720)}
              >
                <ThemedText style={repeat === 720 ? styles.repeatTextActive : null}>
                  Aylık
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>

        <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.formCard}>
          <ThemedText style={styles.sectionTitle}>İlgili Kayıtlar</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>İlgili Dava</ThemedText>
            <TouchableOpacity
              style={[
                styles.input,
                styles.pickerInput,
                isDark && { backgroundColor: '#1F2937', color: '#fff' }
              ].filter(Boolean)}
              onPress={() => {
                // Dava seçim modalini aç
                setShowCasePicker(true);
                setShowClientPicker(false);
              }}
            >
              <ThemedText>
                {loadingCases ? 'Davalar yükleniyor...' :
                  selectedCase ?
                    cases.find(c => c.id === selectedCase)?.title :
                    'Dava seçiniz (opsiyonel)'}
              </ThemedText>
              <Ionicons
                name="chevron-down"
                size={20}
                color={isDark ? '#9ca3af' : '#64748b'}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>İlgili Kişi</ThemedText>
            <TouchableOpacity
              style={[
                styles.input,
                styles.pickerInput,
                isDark && { backgroundColor: '#1F2937', color: '#fff' }
              ].filter(Boolean)}
              onPress={() => {
                // Kişi seçim modalini aç
                setShowClientPicker(true);
                setShowCasePicker(false);
              }}
            >
              <ThemedText>
                {loadingClients || partiesLoading ? 'Kişiler yükleniyor...' :
                  selectedClient ?
                    // Önce dava taraflarında ara, sonra tüm kişilerde
                    caseParties.find(c => c.id === selectedClient)?.name ||
                    clients.find(c => c.id === selectedClient)?.name :
                    selectedCase ? 'Dava taraflarından seçiniz' : 'Kişi seçiniz (opsiyonel)'}
              </ThemedText>
              <Ionicons
                name="chevron-down"
                size={20}
                color={isDark ? '#9ca3af' : '#64748b'}
              />
            </TouchableOpacity>
          </View>
        </BlurView>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={() => router.back()}
            disabled={loading}
          >
            <ThemedText style={styles.buttonText}>İptal</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.actionButton]}
            onPress={handleSave}
            disabled={loading}
          >
            <ThemedText style={styles.saveButtonText}>
              {loading ? 'Kaydediliyor...' : 'Görev Ekle'}
            </ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
      </BackgroundWrapper>

      {/* Dava Seçim Modali */}
      <Modal
        visible={showCasePicker}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCasePicker(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={[styles.modalContent, isDark && styles.modalContentDark]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Dava Seçin</ThemedText>
              <TouchableOpacity onPress={() => setShowCasePicker(false)}>
                <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalSearchContainer}>
              <TextInput
                style={[styles.modalSearchInput, isDark && styles.modalSearchInputDark]}
                placeholder="Dava ara..."
                placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
                value={caseFilter}
                onChangeText={setCaseFilter}
              />
              {caseFilter ? (
                <TouchableOpacity
                  style={styles.modalSearchClear}
                  onPress={() => setCaseFilter('')}
                >
                  <Ionicons name="close-circle" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              ) : null}
            </View>

            {casesError ? (
              <View style={styles.modalError}>
                <ThemedText style={styles.modalErrorText}>{casesError}</ThemedText>
              </View>
            ) : loadingCases ? (
              <View style={styles.modalLoading}>
                <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.modalLoadingText}>Davalar yükleniyor...</ThemedText>
              </View>
            ) : filteredCases.length === 0 ? (
              <View style={styles.modalEmpty}>
                <ThemedText style={styles.modalEmptyText}>
                  {caseFilter ? `"${caseFilter}" için dava bulunamadı` : 'Dava bulunamadı'}
                </ThemedText>
              </View>
            ) : (
              <FlatList
                data={paginatedCases}
                keyExtractor={(item) => (item.id || `case-${Math.random()}`).toString()}
                renderItem={({ item: caseItem }) => (
                  <TouchableOpacity
                    style={[
                      styles.modalItem,
                      selectedCase === caseItem.id && styles.modalItemSelected
                    ]}
                    onPress={async () => {
                      // Dava seçildiğinde tarafları yükle
                      setSelectedCase(caseItem.id);
                      setShowCasePicker(false);

                      // Önce tarafları yeniden yükle
                      await fetchPartiesIfNeeded();

                      // Dava taraflarını kontrol et
                      if (caseItem.dosyaNo) {
                        // Farklı formatları dene
                        const formats = [
                          caseItem.dosyaNo,
                          caseItem.dosyaNo.replace('/', '-'),
                          caseItem.dosyaNo.replace('-', '/'),
                          caseItem.dosyaNo.trim()
                        ];

                        let found = false;
                        for (const format of formats) {
                          if (allParties[format] && allParties[format].length > 0) {
                            console.log(`Dava ${format} için ${allParties[format].length} taraf bulundu`);
                            found = true;
                            break;
                          }
                        }

                        if (!found) {
                          console.log(`Dava ${caseItem.dosyaNo} için taraf bulunamadı, tüm taraflar:`, Object.keys(allParties));
                        }
                      }
                    }}
                  >
                    <View style={styles.caseItemContainer}>
                      <View style={[styles.caseTypeIndicator, { backgroundColor: getCaseTypeColor(caseItem.dosyaTur) }]} />
                      <View style={styles.caseItemContent}>
                        <ThemedText
                          style={[
                            selectedCase === caseItem.id ? styles.modalItemTextSelected : null,
                            styles.caseItemTitle
                          ]}
                        >
                          {caseItem.title}
                        </ThemedText>
                        {caseItem.dosyaTur ? (
                          <View style={[styles.caseTypeChip, { backgroundColor: `${getCaseTypeColor(caseItem.dosyaTur)}20` }]}>
                            <ThemedText style={[styles.caseTypeText, { color: getCaseTypeColor(caseItem.dosyaTur) }]}>
                              {caseItem.dosyaTur}
                            </ThemedText>
                          </View>
                        ) : null}
                      </View>
                    </View>
                  </TouchableOpacity>
                )}
                ListHeaderComponent={
                  <TouchableOpacity
                    style={styles.modalItem}
                    onPress={() => {
                      setSelectedCase(null);
                      setShowCasePicker(false);
                    }}
                  >
                    <ThemedText>Seçim yok</ThemedText>
                  </TouchableOpacity>
                }
                ListFooterComponent={
                  filteredCases.length > casesPerPage ? (
                    <View style={styles.paginationContainer}>
                      <TouchableOpacity
                        style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
                        onPress={goToPrevPage}
                        disabled={currentPage === 1}
                      >
                        <Ionicons name="chevron-back" size={16} color={currentPage === 1 ? '#9ca3af' : Colors[colorScheme ?? 'light'].tint} />
                      </TouchableOpacity>

                      <ThemedText style={styles.paginationText}>
                        {currentPage} / {totalPages}
                      </ThemedText>

                      <TouchableOpacity
                        style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
                        onPress={goToNextPage}
                        disabled={currentPage === totalPages}
                      >
                        <Ionicons name="chevron-forward" size={16} color={currentPage === totalPages ? '#9ca3af' : Colors[colorScheme ?? 'light'].tint} />
                      </TouchableOpacity>
                    </View>
                  ) : null
                }
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* Tarih ve Saat Seçici Modal */}
      {Platform.OS === 'web' && (
        <Modal
          visible={showDateTimePicker}
          animationType="fade"
          transparent={true}
          onRequestClose={() => setShowDateTimePicker(false)}
        >
          <View style={styles.modalContainer}>
            <View style={[styles.dateTimePickerModal, isDark && styles.dateTimePickerModalDark]}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>
                  {dateTimePickerMode === 'start' ? 'Başlangıç Tarihi ve Saati' : 'Bitiş Tarihi ve Saati'}
                </ThemedText>
                <TouchableOpacity onPress={() => setShowDateTimePicker(false)}>
                  <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
                </TouchableOpacity>
              </View>

              <View style={styles.dateTimePickerContent}>
                <View style={styles.dateTimePickerSection}>
                  <ThemedText style={styles.dateTimePickerSectionTitle}>Tarih</ThemedText>
                  <input
                    type="date"
                    value={(dateTimePickerMode === 'start' ? startDate : dueDate).toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newDate = new Date(e.target.value);
                      if (!isNaN(newDate.getTime())) {
                        const currentDate = dateTimePickerMode === 'start' ? new Date(startDate) : new Date(dueDate);
                        currentDate.setFullYear(newDate.getFullYear());
                        currentDate.setMonth(newDate.getMonth());
                        currentDate.setDate(newDate.getDate());

                        if (dateTimePickerMode === 'start') {
                          setStartDate(currentDate);
                        } else {
                          setDueDate(currentDate);
                        }
                      }
                    }}
                    title="Tarih seçin"
                    placeholder="Tarih seçin"
                    style={styles.webDateInput}
                  />
                </View>

                <View style={styles.dateTimePickerSection}>
                  <ThemedText style={styles.dateTimePickerSectionTitle}>Saat</ThemedText>
                  <input
                    type="time"
                    value={`${(dateTimePickerMode === 'start' ? startDate : dueDate).getHours().toString().padStart(2, '0')}:${(dateTimePickerMode === 'start' ? startDate : dueDate).getMinutes().toString().padStart(2, '0')}`}
                    onChange={(e) => {
                      const [hours, minutes] = e.target.value.split(':').map(Number);
                      const currentDate = dateTimePickerMode === 'start' ? new Date(startDate) : new Date(dueDate);
                      currentDate.setHours(hours);
                      currentDate.setMinutes(minutes);

                      if (dateTimePickerMode === 'start') {
                        setStartDate(currentDate);
                      } else {
                        setDueDate(currentDate);
                      }
                    }}
                    title="Saat seçin"
                    placeholder="Saat seçin"
                    style={styles.webTimeInput}
                  />
                </View>
              </View>

              <View style={styles.modalFooter}>
                <TouchableOpacity
                  onPress={() => setShowDateTimePicker(false)}
                  style={[styles.modalButton, styles.modalCancelButton]}
                >
                  <ThemedText style={styles.modalButtonText}>İptal</ThemedText>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => setShowDateTimePicker(false)}
                  style={[styles.modalButton, styles.modalConfirmButton]}
                >
                  <ThemedText style={[styles.modalButtonText, styles.modalConfirmButtonText]}>Tamam</ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {/* Müvekkil Seçim Modali */}
      <Modal
        visible={showClientPicker}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowClientPicker(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={[styles.modalContent, isDark && styles.modalContentDark]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Kişi Seçin</ThemedText>
              <TouchableOpacity onPress={() => setShowClientPicker(false)}>
                <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>
            </View>

            {/* Dava seçilmişse, davanın taraflarını göster */}
            {selectedCase && caseParties.length > 0 ? (
              <FlatList
                data={caseParties}
                keyExtractor={(item) => (item.id || `client-${Math.random()}`).toString()}
                ListHeaderComponent={
                  <>
                    <TouchableOpacity
                      style={styles.modalItem}
                      onPress={() => {
                        setSelectedClient(null);
                        setShowClientPicker(false);
                      }}
                    >
                      <ThemedText>Seçim yok</ThemedText>
                    </TouchableOpacity>
                    <View style={styles.modalSectionHeader}>
                      <ThemedText style={styles.modalSectionHeaderText}>Dava Tarafları</ThemedText>
                    </View>
                  </>
                }
                renderItem={({ item: party }) => (
                  <TouchableOpacity
                    style={[
                      styles.modalItem,
                      selectedClient === party.id && styles.modalItemSelected
                    ]}
                    onPress={() => {
                      setSelectedClient(party.id);
                      setShowClientPicker(false);
                    }}
                  >
                    <View style={styles.pickerItemContent}>
                      <ThemedText
                        style={[
                          selectedClient === party.id ? styles.modalItemTextSelected : null,
                          styles.pickerItemName,
                          party.role?.includes('Davacı') || party.role?.includes('Müşteki') ? styles.plaintiffText :
                          party.role?.includes('Davalı') || party.role?.includes('Şüpheli') ? styles.defendantText : null
                        ]}
                      >
                        {party.name}
                      </ThemedText>
                      <ThemedText style={[
                        styles.pickerItemRole,
                        party.role?.includes('Davacı') || party.role?.includes('Müşteki') ? styles.plaintiffRole :
                        party.role?.includes('Davalı') || party.role?.includes('Şüpheli') ? styles.defendantRole : null
                      ]}>
                        {party.role}
                      </ThemedText>
                    </View>
                  </TouchableOpacity>
                )}
                ListFooterComponent={
                  clients.length > 0 ? (
                    <>
                      <View style={styles.modalSectionHeader}>
                        <ThemedText style={styles.modalSectionHeaderText}>Diğer Kişiler</ThemedText>
                      </View>
                      {clients.map(client => (
                        <TouchableOpacity
                          key={client.id}
                          style={[
                            styles.modalItem,
                            selectedClient === client.id && styles.modalItemSelected
                          ]}
                          onPress={() => {
                            setSelectedClient(client.id);
                            setShowClientPicker(false);
                          }}
                        >
                          <ThemedText style={selectedClient === client.id ? styles.modalItemTextSelected : null}>
                            {client.name}
                          </ThemedText>
                        </TouchableOpacity>
                      ))}
                    </>
                  ) : null
                }
              />
            ) : (
              <>
                {/* Yükleme durumu */}
                {loadingClients || partiesLoading ? (
                  <View style={styles.modalLoading}>
                    <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
                    <ThemedText style={styles.modalLoadingText}>Kişiler yükleniyor...</ThemedText>
                  </View>
                ) : clients.length === 0 ? (
                  <View style={styles.modalEmpty}>
                    <ThemedText style={styles.modalEmptyText}>Kişi bulunamadı</ThemedText>
                  </View>
                ) : (
                  <FlatList
                    data={clients}
                    keyExtractor={(item) => (item.id || `other-client-${Math.random()}`).toString()}
                    ListHeaderComponent={
                      <TouchableOpacity
                        style={styles.modalItem}
                        onPress={() => {
                          setSelectedClient(null);
                          setShowClientPicker(false);
                        }}
                      >
                        <ThemedText>Seçim yok</ThemedText>
                      </TouchableOpacity>
                    }
                    renderItem={({ item: client }) => (
                      <TouchableOpacity
                        style={[
                          styles.modalItem,
                          selectedClient === client.id && styles.modalItemSelected
                        ]}
                        onPress={() => {
                          setSelectedClient(client.id);
                          setShowClientPicker(false);
                        }}
                      >
                        <ThemedText style={selectedClient === client.id ? styles.modalItemTextSelected : null}>
                          {client.name}
                        </ThemedText>
                      </TouchableOpacity>
                    )}
                  />
                )}
              </>
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  modernHeader: {
    flexDirection: 'column',
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  headerStatsContainer: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  headerStats: {
    fontSize: 14,
    color: Colors.light.tint,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    backgroundColor: Platform.OS === 'web' ? 'rgba(255, 255, 255, 0.8)' : 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    padding: 4,
  },
  saveButton: {
    padding: 4,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.05,
          shadowRadius: 4,
        }
    ),
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: Colors.light.tint,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  requiredStar: {
    color: '#F44336',
  },
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#1f2937',
  },
  inputDark: {
    borderColor: '#374151',
    backgroundColor: '#1f2937',
    color: '#f9fafb',
  },
  textArea: {
    height: 100,
    paddingTop: 12,
    paddingBottom: 12,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#F44336',
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  segmentButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 4,
  },
  segmentButtonActive: {
    backgroundColor: Colors.light.tint,
  },
  segmentTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  prioritySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  priorityButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
  },
  priorityButtonActive: {
    borderWidth: 1,
    borderColor: 'white',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  repeatSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  repeatButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  repeatButtonActive: {
    backgroundColor: Colors.light.tint,
  },
  repeatTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  pickerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerDropdown: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    maxHeight: 200,
    backgroundColor: '#fff',
  },
  pickerDropdownDark: {
    backgroundColor: '#1c1c1e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  casePickerDropdown: {
    maxHeight: 250,
    position: 'absolute',
    zIndex: 9999, // Çok yüksek z-index değeri
    top: '100%',
    left: 0,
    right: 0,
    marginTop: 4,
    backgroundColor: '#fff',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  clientPickerDropdown: {
    maxHeight: 250,
    position: 'absolute',
    zIndex: 9998, // Dava dropdown'undan bir düşük z-index
    top: '100%',
    left: 0,
    right: 0,
    marginTop: 4,
    backgroundColor: '#fff',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  pickerItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  casePickerItem: {
    paddingVertical: 16,
  },
  pickerItemSelected: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  pickerItemTextSelected: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    marginBottom: 24,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F0F0F0',
    marginRight: 12,
  },
  actionButton: {
    backgroundColor: Colors.light.tint,
  },
  buttonText: {
    color: '#333333',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  pickerError: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pickerErrorText: {
    color: '#EF4444',
    marginBottom: 8,
    textAlign: 'center',
  },
  pickerRetryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  pickerRetryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  pickerLoading: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  pickerLoadingText: {
    marginLeft: 8,
  },
  pickerEmpty: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pickerEmptyText: {
    color: '#9ca3af',
  },
  pickerSectionHeader: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  pickerSectionHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.7,
  },
  pickerItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  pickerItemName: {
    flex: 1,
  },
  pickerItemRole: {
    fontSize: 12,
    opacity: 0.7,
    marginLeft: 8,
  },
  plaintiffText: {
    color: '#10B981', // Yeşil
  },
  defendantText: {
    color: '#EF4444', // Kırmızı
  },
  plaintiffRole: {
    color: '#10B981',
    opacity: 0.9,
  },
  defendantRole: {
    color: '#EF4444',
    opacity: 0.9,
  },
  // Dava listesi stilleri
  filterContainer: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    position: 'relative',
  },
  filterInput: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 8,
    fontSize: 14,
    paddingRight: 30,
  },
  filterInputDark: {
    color: '#fff',
  },
  clearFilterButton: {
    position: 'absolute',
    right: 16,
    top: 16,
  },
  caseItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  caseTypeIndicator: {
    width: 4,
    height: 40,
    marginRight: 8,
    borderRadius: 2,
    alignSelf: 'center',
  },
  caseItemContent: {
    flex: 1,
  },
  caseItemTitle: {
    marginBottom: 4,
  },
  caseTypeChip: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  caseTypeText: {
    fontSize: 10,
    fontWeight: '700',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  paginationButton: {
    padding: 8,
    borderRadius: 4,
  },
  paginationButtonDisabled: {
    opacity: 0.5,
  },
  paginationText: {
    marginHorizontal: 8,
    fontSize: 12,
  },

  // Modal stilleri
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 30,
    maxHeight: '80%',
  },
  modalContentDark: {
    backgroundColor: '#1c1c1e',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSearchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    position: 'relative',
  },
  modalSearchInput: {
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 8,
    padding: 10,
    paddingRight: 40,
  },
  modalSearchInputDark: {
    borderColor: 'rgba(255, 255, 255, 0.2)',
    color: '#fff',
  },
  modalSearchClear: {
    position: 'absolute',
    right: 26,
    top: 26,
  },
  modalItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalItemSelected: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  modalItemTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  modalSectionHeader: {
    padding: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  modalSectionHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.7,
  },
  modalLoading: {
    padding: 20,
    alignItems: 'center',
  },
  modalLoadingText: {
    marginTop: 10,
  },
  modalEmpty: {
    padding: 20,
    alignItems: 'center',
  },
  modalEmptyText: {
    opacity: 0.7,
  },
  modalError: {
    padding: 20,
    alignItems: 'center',
  },
  modalErrorText: {
    color: '#EF4444',
  },

  // Tarih ve saat seçici stilleri
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  dateTimeItem: {
    width: '48%',
    marginBottom: 8,
  },
  dateTimeLabel: {
    fontSize: 14,
    marginBottom: 4,
    color: '#6B7280',
  },
  dateTimePickerModal: {
    width: 380,
    borderRadius: 16,
    backgroundColor: 'white',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.3)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }
    ),
    elevation: 10,
    overflow: 'hidden',
  },
  dateTimePickerModalDark: {
    backgroundColor: '#1F2937',
    borderColor: '#374151',
  },
  dateTimePickerContent: {
    padding: 24,
  },
  dateTimePickerSection: {
    marginBottom: 20,
  },
  dateTimePickerSectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalCancelButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  modalConfirmButton: {
    backgroundColor: '#3B82F6',
  },
  modalButtonText: {
    fontWeight: '500',
    fontSize: 15,
  },
  modalConfirmButtonText: {
    color: 'white',
  },
  // DateTimePicker Modal Stilleri
  dateTimePickerModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  dateTimePickerModalContent: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  dateTimePickerModalContentDark: {
    backgroundColor: '#1F2937',
  },
  dateTimePickerModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  dateTimePickerModalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  dateTimePickerContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  iosDateTimePicker: {
    width: '100%',
    height: 200,
  },
  androidPickerContainer: {
    width: '100%',
    padding: 10,
  },
  androidPickerLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  androidPickerValue: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  androidPickerButton: {
    backgroundColor: Colors.light.tint,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  androidPickerButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  androidPickerSeparator: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginVertical: 16,
  },
  dateTimePickerModalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  dateTimePickerModalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 10,
  },
  dateTimePickerModalCancelButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  dateTimePickerModalConfirmButton: {
    backgroundColor: Colors.light.tint,
  },
  dateTimePickerModalButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  dateTimePickerModalConfirmButtonText: {
    color: 'white',
  },
  webDateInput: Platform.select({
    web: {
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#D1D5DB',
      borderStyle: 'solid',
      backgroundColor: 'white',
      color: 'black',
      fontSize: 16,
      width: '100%',
      // outline is not supported in React Native Web
      // boxShadow is not directly supported in React Native
      marginBottom: 16
    },
    default: {}
  }),
  webTimeInput: Platform.select({
    web: {
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#D1D5DB',
      borderStyle: 'solid',
      backgroundColor: 'white',
      color: 'black',
      fontSize: 16,
      width: '100%',
      // outline is not supported in React Native Web
      // boxShadow is not directly supported in React Native
    },
    default: {}
  }),
});