import React from 'react';
import { View, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onNextPage: () => void;
  onPrevPage: () => void;
  onPageSelect: (page: number) => void;
}

export default function SimplePagination({
  currentPage,
  totalPages,
  onNextPage,
  onPrevPage,
  onPageSelect,
}: SimplePaginationProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View style={[
      styles.paginationContainer,
      {
        backgroundColor: isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        borderTopColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
      }
    ]}>
      <View style={styles.paginationContent}>
        <View style={styles.paginationControls}>
          {/* First Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === 1 && styles.disabledPaginationButton]}
            onPress={() => onPageSelect(1)}
            disabled={currentPage === 1}
          >
            <Ionicons
              name="play-skip-back"
              size={16}
              color={currentPage === 1 ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === 1 && styles.disabledPaginationButtonText
            ]}>
              İlk
            </ThemedText>
          </TouchableOpacity>

          {/* Previous Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === 1 && styles.disabledPaginationButton]}
            onPress={onPrevPage}
            disabled={currentPage === 1}
          >
            <Ionicons
              name="chevron-back"
              size={16}
              color={currentPage === 1 ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === 1 && styles.disabledPaginationButtonText
            ]}>
              Önceki
            </ThemedText>
          </TouchableOpacity>

          {/* Page Numbers */}
          <View style={styles.pageNumbersContainer}>
            {/* Generate page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Logic to determine which page numbers to show
              let pageNum;
              if (totalPages <= 5) {
                // If 5 or fewer pages, show all
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                // If near the start
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                // If near the end
                pageNum = totalPages - 4 + i;
              } else {
                // If in the middle
                pageNum = currentPage - 2 + i;
              }

              return (
                <TouchableOpacity
                  key={i}
                  style={[
                    styles.pageNumberButton,
                    { backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)' },
                    currentPage === pageNum && styles.activePageNumberButton
                  ]}
                  onPress={() => onPageSelect(pageNum)}
                  disabled={currentPage === pageNum}
                >
                  <ThemedText style={[
                    styles.pageNumberText,
                    currentPage === pageNum && styles.activePageNumberText
                  ]}>
                    {pageNum}
                  </ThemedText>
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Next Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === totalPages && styles.disabledPaginationButton]}
            onPress={onNextPage}
            disabled={currentPage === totalPages}
          >
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === totalPages && styles.disabledPaginationButtonText
            ]}>
              Sonraki
            </ThemedText>
            <Ionicons
              name="chevron-forward"
              size={16}
              color={currentPage === totalPages ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
          </TouchableOpacity>

          {/* Last Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === totalPages && styles.disabledPaginationButton]}
            onPress={() => onPageSelect(totalPages)}
            disabled={currentPage === totalPages}
          >
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === totalPages && styles.disabledPaginationButtonText
            ]}>
              Son
            </ThemedText>
            <Ionicons
              name="play-skip-forward"
              size={16}
              color={currentPage === totalPages ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  paginationContainer: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    left: 0,
    right: 0,
    zIndex: 10,
    position: 'relative',
    bottom: 0,
  },
  paginationContent: {
    width: '100%',
    maxWidth: 1200,
    marginLeft: 'auto',
    marginRight: 'auto',
    flexDirection: 'column',
    alignItems: 'center',
  },
  paginationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  paginationButton: {
    width: 36,
    height: 36,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    position: 'relative',
    marginHorizontal: 2,
  },
  paginationButtonWithText: {
    width: 'auto',
    paddingHorizontal: 12,
    flexDirection: 'row',
  },
  paginationButtonText: {
    color: 'white',
    fontSize: 13,
    fontWeight: '500',
    marginHorizontal: 4,
  },
  disabledPaginationButton: {
    opacity: 0.5,
  },
  disabledPaginationButtonText: {
    opacity: 0.8,
  },
  pageNumbersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  pageNumberButton: {
    width: 36,
    height: 36,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  activePageNumberButton: {
    backgroundColor: Colors.light.tint,
  },
  pageNumberText: {
    fontSize: 14,
    fontWeight: '500',
  },
  activePageNumberText: {
    color: 'white',
    fontWeight: '600',
  },
});
