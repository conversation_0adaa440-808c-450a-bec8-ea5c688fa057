import apiClient from './api';

// Hatırlatıcı servisi
const reminderService = {
  // Tüm hatırlatıcıları getir
  getAllReminders: async () => {
    try {
      const response = await apiClient.get('/api/reminders');
      return response.data;
    } catch (error) {
      console.error('Get all reminders error:', error);
      throw error;
    }
  },
  
  // Hatırlatıcı detayını getir
  getReminderById: async (reminderId) => {
    try {
      const response = await apiClient.get(`/api/reminders/${reminderId}`);
      return response.data;
    } catch (error) {
      console.error(`Get reminder ${reminderId} error:`, error);
      throw error;
    }
  },
  
  // Yeni hatırlatıcı oluştur
  createReminder: async (reminderData) => {
    try {
      const response = await apiClient.post('/api/reminders', reminderData);
      return response.data;
    } catch (error) {
      console.error('Create reminder error:', error);
      throw error;
    }
  },
  
  // Hat<PERSON>rlatıcı güncelle
  updateReminder: async (reminderId, reminderData) => {
    try {
      const response = await apiClient.put(`/api/reminders/${reminderId}`, reminderData);
      return response.data;
    } catch (error) {
      console.error(`Update reminder ${reminderId} error:`, error);
      throw error;
    }
  },
  
  // Hatırlatıcı sil
  deleteReminder: async (reminderId) => {
    try {
      const response = await apiClient.delete(`/api/reminders/${reminderId}`);
      return response.data;
    } catch (error) {
      console.error(`Delete reminder ${reminderId} error:`, error);
      throw error;
    }
  }
};

export default reminderService;
