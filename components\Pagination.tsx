import React from 'react';
import { View, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  itemsPerPage?: number;
  showSummary?: boolean;
}

export default function Pagination({
  currentPage,
  totalPages,
  totalItems,
  onPageChange,
  itemsPerPage = 10,
  showSummary = true,
}: PaginationProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Go to specific page
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  // Go to next page
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  // Go to previous page
  const goToPrevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  return (
    <View style={styles.paginationContainer}>
      <View style={styles.paginationContent}>
        {showSummary && (
          <View style={styles.paginationSummary}>
            <ThemedText style={styles.paginationSummaryText}>
              Toplam {totalItems} kayıt • Sayfa {currentPage}/{totalPages}
            </ThemedText>
          </View>
        )}

        <View style={styles.paginationControls}>
          {/* First Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === 1 && styles.disabledPaginationButton]}
            onPress={() => goToPage(1)}
            disabled={currentPage === 1}
          >
            <Ionicons
              name="play-skip-back"
              size={12}
              color={currentPage === 1 ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === 1 && styles.disabledPaginationButtonText
            ]}>
              İlk
            </ThemedText>
          </TouchableOpacity>

          {/* Previous Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === 1 && styles.disabledPaginationButton]}
            onPress={goToPrevPage}
            disabled={currentPage === 1}
          >
            <Ionicons
              name="chevron-back"
              size={12}
              color={currentPage === 1 ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === 1 && styles.disabledPaginationButtonText
            ]}>
              Önceki
            </ThemedText>
          </TouchableOpacity>

          {/* Page Numbers */}
          <View style={styles.pageNumbersContainer}>
            {/* Generate page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Logic to determine which page numbers to show
              let pageNum;
              if (totalPages <= 5) {
                // If 5 or fewer pages, show all
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                // If near the start
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                // If near the end
                pageNum = totalPages - 4 + i;
              } else {
                // If in the middle
                pageNum = currentPage - 2 + i;
              }

              return (
                <TouchableOpacity
                  key={i}
                  style={[
                    styles.pageNumberButton,
                    { backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.03)' },
                    currentPage === pageNum && styles.activePageNumberButton
                  ]}
                  onPress={() => goToPage(pageNum)}
                  disabled={currentPage === pageNum}
                >
                  <ThemedText style={[
                    styles.pageNumberText,
                    currentPage === pageNum && styles.activePageNumberText
                  ]}>
                    {pageNum}
                  </ThemedText>
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Next Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === totalPages && styles.disabledPaginationButton]}
            onPress={goToNextPage}
            disabled={currentPage === totalPages}
          >
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === totalPages && styles.disabledPaginationButtonText
            ]}>
              Sonraki
            </ThemedText>
            <Ionicons
              name="chevron-forward"
              size={12}
              color={currentPage === totalPages ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
          </TouchableOpacity>

          {/* Last Page */}
          <TouchableOpacity
            style={[styles.paginationButton, styles.paginationButtonWithText, currentPage === totalPages && styles.disabledPaginationButton]}
            onPress={() => goToPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            <ThemedText style={[
              styles.paginationButtonText,
              currentPage === totalPages && styles.disabledPaginationButtonText
            ]}>
              Son
            </ThemedText>
            <Ionicons
              name="play-skip-forward"
              size={12}
              color={currentPage === totalPages ? (isDark ? '#666' : '#ccc') : '#fff'}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  paginationContainer: {
    paddingVertical: 8,
    paddingHorizontal: 0,
    marginTop: 0, // Removed margin to position directly under cards
    // No background or border
    width: '100%',
    zIndex: 10,
  },
  paginationContent: {
    width: '70%', // Match the 70% width of cards container
    maxWidth: 1000, // Match the 1000px max-width of cards container
    marginLeft: 'auto',
    marginRight: 'auto',
    flexDirection: 'column',
    alignItems: 'center',
  },
  paginationSummary: {
    marginBottom: 8,
    alignItems: 'center',
  },
  paginationSummaryText: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.8,
  },
  paginationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  paginationButton: {
    width: 28,
    height: 28,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.tint,
    position: 'relative',
    marginHorizontal: 2,
  },
  paginationButtonWithText: {
    width: 'auto',
    paddingHorizontal: 6,
    flexDirection: 'row',
  },
  paginationButtonText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '500',
    marginHorizontal: 2,
  },
  disabledPaginationButton: {
    opacity: 0.5,
  },
  disabledPaginationButtonText: {
    opacity: 0.8,
  },
  pageNumbersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  pageNumberButton: {
    width: 28,
    height: 28,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  activePageNumberButton: {
    backgroundColor: Colors.light.tint,
  },
  pageNumberText: {
    fontSize: 11,
    fontWeight: '500',
  },
  activePageNumberText: {
    color: 'white',
    fontWeight: '600',
  },
});
