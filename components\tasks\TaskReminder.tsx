import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface TaskReminderProps {
  id: string;
  title: string;
  description?: string;
  dueDate: string;
  priority: 'DÜŞÜK' | 'ORTA' | 'YÜKSEK' | 'KRİTİK';
  type: 'GÖREV' | 'DURUŞMA' | 'TOPLANTI' | 'HATIRLATMA';
  onDismiss?: () => void;
  onSnooze?: () => void;
}

const PRIORITY_COLORS = {
  'DÜŞÜK': { bg: '#E5F6FD', text: '#0EA5E9' },
  'ORTA': { bg: '#F0FDF4', text: '#22C55E' },
  'YÜKSEK': { bg: '#FEF3C7', text: '#F59E0B' },
  'KRİTİK': { bg: '#FEE2E2', text: '#EF4444' },
};

export default function TaskReminder({ 
  id, 
  title, 
  description, 
  dueDate, 
  priority, 
  type,
  onDismiss,
  onSnooze
}: TaskReminderProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const formattedDate = new Date(dueDate).toLocaleString('tr-TR', {
    day: '2-digit',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit'
  });
  
  const getTaskTypeIcon = () => {
    switch (type) {
      case 'GÖREV':
        return 'checkmark-circle';
      case 'DURUŞMA':
        return 'briefcase';
      case 'TOPLANTI':
        return 'people';
      case 'HATIRLATMA':
        return 'alarm';
      default:
        return 'checkmark-circle';
    }
  };
  
  const handleViewTask = () => {
    router.push(`/tasks/${id}`);
  };
  
  return (
    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.container}>
      <View style={styles.header}>
        <View style={styles.typeContainer}>
          <Ionicons name={getTaskTypeIcon()} size={20} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.typeText}>{type}</ThemedText>
        </View>
        
        <View style={[
          styles.priorityBadge,
          { backgroundColor: PRIORITY_COLORS[priority].bg }
        ]}>
          <ThemedText style={[
            styles.priorityText,
            { color: PRIORITY_COLORS[priority].text }
          ]}>
            {priority}
          </ThemedText>
        </View>
      </View>
      
      <ThemedText type="defaultSemiBold" style={styles.title}>{title}</ThemedText>
      
      {description && (
        <ThemedText style={styles.description} numberOfLines={2}>
          {description}
        </ThemedText>
      )}
      
      <View style={styles.dateContainer}>
        <Ionicons name="time-outline" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
        <ThemedText style={styles.dateText}>{formattedDate}</ThemedText>
      </View>
      
      <View style={styles.actions}>
        <TouchableOpacity style={styles.actionButton} onPress={onSnooze}>
          <Ionicons name="time-outline" size={18} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.actionText}>Ertele</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={onDismiss}>
          <Ionicons name="close-circle-outline" size={18} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.actionText}>Kapat</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.viewButton} onPress={handleViewTask}>
          <ThemedText style={styles.viewButtonText}>Görüntüle</ThemedText>
        </TouchableOpacity>
      </View>
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.8,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateText: {
    fontSize: 12,
    marginLeft: 4,
    opacity: 0.7,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
  },
  actionText: {
    fontSize: 12,
    marginLeft: 4,
  },
  viewButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  viewButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});
