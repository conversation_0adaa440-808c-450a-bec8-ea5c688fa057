import { StyleSheet, TouchableOpacity, FlatList, View, TextInput, ActivityIndicator, Platform } from 'react-native';
import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'react-native-axios';
import logger from '@/utils/logger';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import Pagination from '@/components/Pagination';
import Footer from '@/components/layout/Footer';
import BackgroundWrapper from '@/components/BackgroundWrapper';

// API base URL
const API_URL = 'http://193.35.154.97:4244';

// Client interface
interface Client {
  id: string;
  adi: string;
  soyad: string;
  rol: string;
  kisiKurum: string;
  dosyaNo: string;
}

// Fallback client data (used when API fails)
const CLIENTS_DATA: Client[] = [
  { id: 'sample-1', adi: 'Ahmet', soyad: 'Yılmaz', rol: 'Davacı', kisiKurum: 'Kişi', dosyaNo: '2023/123' },
  { id: 'sample-2', adi: 'Mehmet', soyad: 'Kaya', rol: 'Davalı', kisiKurum: 'Kişi', dosyaNo: '2023/123' },
  { id: 'sample-3', adi: 'Ayşe', soyad: 'Demir', rol: 'Davacı Vekili', kisiKurum: 'Kişi', dosyaNo: '2023/123' },
  { id: 'sample-4', adi: 'Fatma', soyad: 'Öztürk', rol: 'Davacı', kisiKurum: 'Kişi', dosyaNo: '2022/456' },
  { id: 'sample-5', adi: 'Ali', soyad: 'Yıldız', rol: 'Davalı', kisiKurum: 'Kişi', dosyaNo: '2022/456' },
  { id: 'sample-6', adi: 'XYZ Şirketi', soyad: '', rol: 'Davalı', kisiKurum: 'Kurum', dosyaNo: '2022/456' },
];

export default function ClientsScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // State
  const [activeFilter, setActiveFilter] = useState('Tümü'); // Tümü, Kişi, Kurum
  const [sortBy, setSortBy] = useState('adi');
  const [sortOrder, setSortOrder] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [searchText, setSearchText] = useState('');
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [isSortModalVisible, setIsSortModalVisible] = useState(false);
  const itemsPerPage = 10;
  const flatListRef = useRef<FlatList>(null);

  // We no longer need expanded card state
  // Removed expandedCardIds state

  // API state
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Get client type color - Enhanced version with more distinct colors
  const getClientTypeColor = (type: string, role: string): string => {
    // Primary role-based colors
    const getRoleColor = (role: string): string => {
      // Plaintiff/Claimant roles
      if (role.includes('Davacı') || role.includes('Müşteki') || role.includes('Alacaklı')) {
        return '#3B82F6'; // Blue
      }
      // Defendant/Accused roles
      else if (role.includes('Davalı') || role.includes('Şüpheli') || role.includes('Sanık') || role.includes('Borçlu')) {
        return '#EF4444'; // Red
      }
      // Legal representative roles
      else if (role.includes('Vekil') || role.includes('Avukat')) {
        return '#10B981'; // Green
      }
      // Witness roles
      else if (role.includes('Tanık')) {
        return '#F59E0B'; // Amber
      }
      // Expert roles
      else if (role.includes('Bilirkişi')) {
        return '#8B5CF6'; // Purple
      }
      // Judge/Court roles
      else if (role.includes('Hakim') || role.includes('Mahkeme')) {
        return '#0EA5E9'; // Sky Blue
      }
      // Prosecutor roles
      else if (role.includes('Savcı')) {
        return '#F43F5E'; // Rose
      }
      // Mediator roles
      else if (role.includes('Arabulucu')) {
        return '#14B8A6'; // Teal
      }
      // Third party roles
      else if (role.includes('Üçüncü')) {
        return '#EC4899'; // Pink
      }
      // Default for other roles
      else {
        return '#6B7280'; // Gray
      }
    };

    // Get base color from role
    const baseColor = getRoleColor(role);

    // Adjust color based on type (Kişi/Kurum)
    if (type === 'Kişi') {
      return baseColor; // Keep original color for individuals
    } else if (type === 'Kurum') {
      // For organizations, create a different shade of the same color family
      switch (baseColor) {
        case '#3B82F6': return '#2563EB'; // Darker Blue
        case '#EF4444': return '#DC2626'; // Darker Red
        case '#10B981': return '#059669'; // Darker Green
        case '#F59E0B': return '#D97706'; // Darker Amber
        case '#8B5CF6': return '#7C3AED'; // Darker Purple
        case '#0EA5E9': return '#0284C7'; // Darker Sky Blue
        case '#F43F5E': return '#E11D48'; // Darker Rose
        case '#14B8A6': return '#0D9488'; // Darker Teal
        case '#EC4899': return '#DB2777'; // Darker Pink
        case '#6B7280': return '#4B5563'; // Darker Gray
        default: return '#4B5563'; // Darker Gray as fallback
      }
    } else {
      return '#6B7280'; // Gray for unknown types
    }
  };

  // Standardize client object
  const standardizeClient = (client: any, dosyaNo?: string): Client => {
    return {
      id: client.id || `client-${Math.random().toString(36).substring(2, 11)}`,
      adi: client.adi || client.isim || client.name || 'İsimsiz',
      soyad: client.soyad || client.surname || '',
      rol: client.rol || client.sifat || client.role || 'Belirtilmemiş',
      kisiKurum: client.kisiKurum || client.type || 'Kişi',
      dosyaNo: client.dosyaNo || dosyaNo || 'Belirtilmemiş',
    };
  };

  // Process API response
  const processApiResponse = (data: any): Client[] => {
    console.log('Processing API response...');
    console.log('Response type:', typeof data);

    const processedClients: Client[] = [];

    try {
      // Case 1: Response is an array
      if (Array.isArray(data)) {
        console.log('Response is an array with', data.length, 'items');

        data.forEach((client: any) => {
          if (client) {
            processedClients.push(standardizeClient(client, ''));
          }
        });
      }
      // Case 2: Response is an object with case numbers as keys
      else if (data && typeof data === 'object') {
        console.log('Response is an object with keys:', Object.keys(data));

        Object.entries(data).forEach(([dosyaNo, parties]: [string, any]) => {
          if (Array.isArray(parties)) {
            console.log(`Processing ${parties.length} parties for case ${dosyaNo}`);

            parties.forEach((party: any) => {
              if (party) {
                const standardizedParty = standardizeClient(party, dosyaNo);
                processedClients.push(standardizedParty);
              }
            });
          }
        });

        // Case 3: Response has a data property
        if (data.data && Array.isArray(data.data)) {
          console.log('Processing data property with', data.data.length, 'items');

          data.data.forEach((client: any) => {
            if (client) {
              processedClients.push(standardizeClient(client, ''));
            }
          });
        }

        // Case 4: Response has a taraflar property
        if (data.taraflar && Array.isArray(data.taraflar)) {
          console.log('Processing taraflar property with', data.taraflar.length, 'items');

          data.taraflar.forEach((client: any) => {
            if (client) {
              processedClients.push(standardizeClient(client, ''));
            }
          });
        }
      }

      console.log(`Processed ${processedClients.length} clients`);

      return processedClients;
    } catch (err) {
      console.error('Error processing API response:', err);
      return [];
    }
  };

  // Add custom CSS to remove focus outline and add transitions
  useEffect(() => {
    if (Platform.OS === 'web') {
      // Create a style element
      const style = document.createElement('style');
      style.textContent = `
        .search-input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
        input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
        .search-container {
          transition: all 0.3s ease !important;
        }
        .search-container:focus-within {
          border-color: ${Colors.light.tint} !important;
          box-shadow: 0 0 0 1px ${Colors.light.tint} !important;
        }
      `;
      // Add it to the document head
      document.head.appendChild(style);

      // Clean up when component unmounts
      return () => {
        document.head.removeChild(style);
      };
    }
  }, []);

  // Track initial mount and reset state
  const [initialMount, setInitialMount] = useState(true);
  const [needsReset, setNeedsReset] = useState(false);

  // Parse URL parameters for web
  const parseUrlParams = useCallback(() => {
    if (Platform.OS === 'web') {
      const urlParams = new URLSearchParams(window.location.search);

      // Get page from URL
      const pageParam = urlParams.get('page');
      if (pageParam) {
        const parsedPage = parseInt(pageParam, 10);
        if (!isNaN(parsedPage) && parsedPage > 0) {
          setCurrentPage(parsedPage);
        }
      }

      // Get search text from URL
      const searchParam = urlParams.get('search');
      if (searchParam) {
        setSearchText(searchParam);
      }

      // Get filter from URL
      const filterParam = urlParams.get('filter');
      if (filterParam && ['Tümü', 'Kişi', 'Kurum'].includes(filterParam)) {
        setActiveFilter(filterParam);
      }

      // Get sort parameters from URL
      const sortParam = urlParams.get('sort');
      const orderParam = urlParams.get('order');

      if (sortParam && ['adi', 'soyad', 'rol', 'kisiKurum', 'dosyaNo'].includes(sortParam)) {
        setSortBy(sortParam);
      }

      if (orderParam && ['asc', 'desc'].includes(orderParam)) {
        setSortOrder(orderParam as 'asc' | 'desc');
      }

      return urlParams.toString() !== '';
    }
    return false;
  }, []);

  // Check if we need to reset the page when returning from another tab
  useEffect(() => {
    const checkNeedsReset = async () => {
      if (Platform.OS === 'web') {
        // Check if we need to reset based on localStorage
        const needsReset = localStorage.getItem('kisilerNeedsReset');

        if (needsReset === 'true') {
          logger.info('Returning to Kişiler page from another tab, needs reset');
          setNeedsReset(true);
          // Clear the flag
          localStorage.removeItem('kisilerNeedsReset');
        }
      } else {
        // For mobile, check AsyncStorage
        try {
          const needsReset = await AsyncStorage.getItem('kisilerNeedsReset');

          if (needsReset === 'true') {
            logger.info('Mobile: Kişiler page needs reset');
            setNeedsReset(true);
            // Clear the flag
            await AsyncStorage.removeItem('kisilerNeedsReset');
          }
        } catch (error) {
          logger.error('Error checking reset state:', error);
        }
      }
    };

    checkNeedsReset();
  }, []);

  // Always reset on first mount, but allow pagination to work
  useEffect(() => {
    // This effect runs only once when the component mounts
    if (initialMount) {
      logger.info('Kişiler page mounted - checking if reset needed');

      // For web: Check if we're coming from pagination (URL has query parameters)
      const hasUrlParams = Platform.OS === 'web' && parseUrlParams();

      // For mobile: Always reset if needed
      // For web: Reset if not pagination navigation or if we need to reset
      if (Platform.OS !== 'web' || !hasUrlParams || needsReset) {
        logger.info('Resetting Kişiler page to default state');

        // Reset to default values
        setCurrentPage(1);
        setSearchText('');
        setActiveFilter('Tümü');
        setSortBy('adi');
        setSortOrder('asc');

        // Clear any stored pagination state in web
        if (Platform.OS === 'web') {
          localStorage.removeItem('clientsCurrentPage');
          localStorage.removeItem('clientsSearchText');
          localStorage.removeItem('clientsActiveFilter');
          localStorage.removeItem('clientsSortBy');
          localStorage.removeItem('clientsSortOrder');

          // Clear the needs reset flag
          setNeedsReset(false);
        }

        // Clear any stored state in mobile
        if (Platform.OS !== 'web') {
          const clearMobileState = async () => {
            try {
              await AsyncStorage.removeItem('clientsCurrentPage');
              await AsyncStorage.removeItem('clientsSearchText');
              await AsyncStorage.removeItem('clientsActiveFilter');
              await AsyncStorage.removeItem('clientsSortBy');
              await AsyncStorage.removeItem('clientsSortOrder');
              await AsyncStorage.removeItem('kisilerNeedsReset');

              // Clear the needs reset flag
              setNeedsReset(false);
            } catch (error) {
              logger.error('Error clearing mobile state:', error);
            }
          };
          clearMobileState();
        }
      } else {
        // We're navigating with pagination on web, restore state
        if (Platform.OS === 'web') {
          const storedPage = localStorage.getItem('clientsCurrentPage');
          const storedSearchText = localStorage.getItem('clientsSearchText');
          const storedActiveFilter = localStorage.getItem('clientsActiveFilter');
          const storedSortBy = localStorage.getItem('clientsSortBy');
          const storedSortOrder = localStorage.getItem('clientsSortOrder');

          if (storedPage) setCurrentPage(parseInt(storedPage, 10));
          if (storedSearchText) setSearchText(storedSearchText);
          if (storedActiveFilter) setActiveFilter(storedActiveFilter);
          if (storedSortBy) setSortBy(storedSortBy);
          if (storedSortOrder) setSortOrder(storedSortOrder as 'asc' | 'desc');
        }
      }

      // Mark initial mount as complete
      setInitialMount(false);
    }
  }, [initialMount, needsReset]);

  // Fetch clients from API
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        setError('');

        // Get auth token
        const token = await AsyncStorage.getItem('auth_token');

        if (!token) {
          console.log('No auth token found, using sample data');
          setLoading(false);
          setError('Oturum açık değil. Lütfen tekrar giriş yapın.');
          setClients(CLIENTS_DATA);
          return;
        }

        // Make API request
        console.log('Fetching clients from API...');
        const response = await axios.get(`${API_URL}/api/user/cases/taraflar-all`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('API response received:', response.status);

        // Process the data
        const processedClients = processApiResponse(response.data);
        setClients(processedClients);
        setError('');
      } catch (err: any) {
        console.error('Error fetching clients:', err);
        setError(`Kişiler yüklenirken bir hata oluştu: ${err.message || 'Bilinmeyen hata'}`);
        // Use sample data as fallback
        setClients(CLIENTS_DATA);
      } finally {
        setLoading(false);
      }
    };

    fetchClients();
  }, []);

  // Sorting function
  const sortClients = (clients: Client[]) => {
    if (!Array.isArray(clients) || clients.length === 0) {
      return [];
    }

    return [...clients].sort((a, b) => {
      if (!a || !b) return 0;

      let valueA: string | number;
      let valueB: string | number;

      // Get values based on sort field
      switch (sortBy) {
        case 'adi':
          valueA = String(a.adi || '').toLowerCase();
          valueB = String(b.adi || '').toLowerCase();
          break;
        case 'soyad':
          valueA = String(a.soyad || '').toLowerCase();
          valueB = String(b.soyad || '').toLowerCase();
          break;
        case 'rol':
          valueA = String(a.rol || '').toLowerCase();
          valueB = String(b.rol || '').toLowerCase();
          break;
        case 'kisiKurum':
          valueA = String(a.kisiKurum || '').toLowerCase();
          valueB = String(b.kisiKurum || '').toLowerCase();
          break;
        case 'dosyaNo':
          valueA = String(a.dosyaNo || '');
          valueB = String(b.dosyaNo || '');
          break;
        default:
          valueA = String(a.adi || '').toLowerCase();
          valueB = String(b.adi || '').toLowerCase();
      }

      // Compare based on sort order
      if (sortOrder === 'asc') {
        return (valueA as string).localeCompare(valueB as string);
      } else {
        return (valueB as string).localeCompare(valueA as string);
      }
    });
  };

  // Search and filter function - optimized with useMemo
  const filteredClients = useMemo(() => {
    // Add debugging
    console.log('Filtering clients...');
    console.log('Total clients:', clients.length);
    console.log('Current search text:', searchText);
    console.log('Current filter:', activeFilter);

    let filtered = clients;

    // Type filter
    if (activeFilter !== 'Tümü') {
      filtered = filtered.filter(item => item.kisiKurum === activeFilter);
      console.log('After type filter:', filtered.length);
    }

    // Text search - case-insensitive
    if (searchText.trim() !== '') {
      const searchLower = searchText.toLowerCase();
      console.log('Search term (lowercase):', searchLower);

      // Log a few sample clients for debugging
      if (clients.length > 0) {
        console.log('Sample client data:');
        console.log(clients[0]);
      }

      filtered = filtered.filter(item => {
        // Check each field individually for debugging
        const matchesName = (item.adi?.toLowerCase() || '').includes(searchLower);
        const matchesSurname = (item.soyad?.toLowerCase() || '').includes(searchLower);
        const matchesRole = (item.rol?.toLowerCase() || '').includes(searchLower);
        const matchesCaseNumber = (item.dosyaNo?.toLowerCase() || '').includes(searchLower);
        const matchesType = (item.kisiKurum?.toLowerCase() || '').includes(searchLower);

        // Return true if any field matches
        return matchesName || matchesSurname || matchesRole || matchesCaseNumber || matchesType;
      });

      console.log('After text search:', filtered.length);
    }

    // Sort
    const result = sortClients(filtered);
    console.log('Final filtered count:', result.length);
    return result;
  }, [clients, activeFilter, searchText, sortBy, sortOrder]);

  // Platform-specific pagination strategy
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedClients, setDisplayedClients] = useState<Client[]>([]);

  // Update displayedClients when filteredClients changes
  useEffect(() => {
    if (Platform.OS !== 'web') {
      // Load first page for mobile
      setDisplayedClients(filteredClients.slice(0, itemsPerPage));
    }
  }, [filteredClients, itemsPerPage]);

  // Scroll to top when page changes
  const scrollToTop = useCallback(() => {
    if (Platform.OS === 'web') {
      // Force immediate scroll to top without animation
      window.scrollTo(0, 0);

      // For safety, also try to scroll the container element if it exists
      const container = document.getElementById('clients-container');
      if (container) {
        container.scrollTop = 0;
      }

      // Also try to scroll the body and html elements
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
    }
  }, []);

  // Scroll to top when page changes
  useEffect(() => {
    scrollToTop();
  }, [currentPage, scrollToTop]);

  // Current page clients
  const currentClients = Platform.OS === 'web'
    ? filteredClients.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : displayedClients;

  // Total pages
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);

  // Define sections for FlatList
  const sections = [
    { id: 'content', type: 'content' },
    { id: 'pagination', type: 'pagination' },
    { id: 'footer', type: 'footer' }
  ];

  // Load more items for mobile
  const loadMoreItems = () => {
    if (isLoadingMore || displayedClients.length >= filteredClients.length) return;

    setIsLoadingMore(true);

    // Simulated loading delay
    setTimeout(() => {
      // Get next page from filteredClients
      const nextItems = filteredClients.slice(
        displayedClients.length,
        displayedClients.length + itemsPerPage
      );

      setDisplayedClients([...displayedClients, ...nextItems]);
      setIsLoadingMore(false);

      // Scroll to top for mobile if this is the first page
      if (Platform.OS !== 'web' && displayedClients.length === 0) {
        // Scroll FlatList to top if ref exists
        if (flatListRef.current) {
          flatListRef.current.scrollToOffset({ offset: 0, animated: true });
        }
      }
    }, 300);
  };

  // Navigate to case detail
  const navigateToCaseDetail = (dosyaNo: string, client?: Client): void => {
    if (!dosyaNo || dosyaNo === 'Belirtilmemiş') {
      alert('Bu kişi için bir dosya numarası belirtilmemiş.');
      return;
    }

    const dosyaId = dosyaNo.split('/')[0];
    const dosyaNumara = dosyaNo.split('/')[1] || '';
    const queryParam = `?dosyaNo=${encodeURIComponent(dosyaNo)}&from=clients`;

    // Store data in localStorage for web
    if (Platform.OS === 'web') {
      // Store the current path to return to later
      localStorage.setItem('previousPath', window.location.pathname + window.location.search);

      // Set flag to reset Kişiler page when returning
      localStorage.setItem('kisilerNeedsReset', 'true');

      // Create a more complete case data object
      const caseData = {
        dosyaNo: dosyaNo,
        dosyaId: dosyaId,
        esasYil: dosyaNo.split('/')[0],
        dosyaNumara: dosyaNumara,
        // Add default values for required fields
        dosyaTur: 'Hukuk', // Default value
        birimAdi: 'Asliye Hukuk Mahkemesi', // Default value
        dosyaDurum: 'Açık', // Default value
        // Add client information if available
        taraflar: client ? [client] : [],
        // Add opening date if available (current date as fallback)
        acilisTarihi: new Date().toLocaleDateString('tr-TR')
      };

      // Store the case data
      localStorage.setItem('selectedCaseData', JSON.stringify(caseData));
      console.log('Stored case data for navigation:', caseData);
    } else {
      // For mobile, set the reset flag in AsyncStorage
      AsyncStorage.setItem('kisilerNeedsReset', 'true')
        .catch(error => logger.error('Error setting kisilerNeedsReset flag:', error));
    }

    router.push(`/cases/${dosyaId}${queryParam}`);
  };

  // We've removed the card expansion functionality

  // Note: We're using simple URL construction with just the page parameter
  // This matches the implementation in cases.tsx which is working correctly

  // Note: Pagination is handled by the Pagination component
  // The component calls onPageChange which handles the navigation

  // Handle sort
  const handleSort = (field: string): void => {
    let newSortOrder = sortOrder;

    if (sortBy === field) {
      // Toggle sort order for the same field
      newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
      setSortOrder(newSortOrder);
    } else {
      // New field selected, default to ascending
      setSortBy(field);
      setSortOrder('asc');
      newSortOrder = 'asc';
    }

    // For both web and mobile, just update the state and let the useMemo handle the sorting
    if (Platform.OS === 'web') {
      // Save the new sort state in localStorage for persistence
      localStorage.setItem('clientsSortBy', field);
      localStorage.setItem('clientsSortOrder', newSortOrder);
      // Reset to page 1
      localStorage.setItem('clientsCurrentPage', '1');
    }

    // Reset to page 1 for consistency
    setCurrentPage(1);

    // Close the modal
    setIsSortModalVisible(false);
  };

  // Render client item
  const renderClientItem = ({ item }: { item: Client }) => {
    // Extract fields
    const id = item.id || '';
    const adi = item.adi || '';
    const soyad = item.soyad || '';
    const rol = item.rol || '';
    const kisiKurum = item.kisiKurum || '';
    const dosyaNo = item.dosyaNo || '';

    // Get client type color
    const clientTypeColor = getClientTypeColor(kisiKurum, rol);

    // We no longer need to check if card is expanded

    // Format client name based on type
    const clientName = kisiKurum === 'Kişi'
      ? `${adi} ${soyad}`.trim()
      : adi;

    return (
      <TouchableOpacity
        style={styles.clientCard}
        activeOpacity={0.8}
      >
        <BlurView
          intensity={isDark ? 40 : 60}
          tint={isDark ? 'dark' : 'light'}
          style={[
            styles.cardBlur,
            {
              backgroundColor: isDark
                ? `${clientTypeColor}15` // 15% opacity for dark mode
                : `${clientTypeColor}08`  // 8% opacity for light mode
            }
          ]}
        >
          {/* Color indicator */}
          <View style={[styles.clientTypeIndicator, { backgroundColor: clientTypeColor }]} />

          {/* Single row header with all key information */}
          <View style={styles.singleRowHeader}>
            {/* Left section */}
            <View style={styles.headerLeftSection}>
              <View style={styles.clientNameContainer}>
                <View style={styles.clientTitleContainer}>
                  <ThemedText numberOfLines={1} style={styles.clientTitle}>
                    {clientName}
                  </ThemedText>

                  {/* Client type chip */}
                  <View style={[
                    styles.typeChip,
                    {
                      backgroundColor: `${clientTypeColor}20`,
                      borderWidth: 1,
                      borderColor: `${clientTypeColor}40`,
                      marginLeft: 8
                    }
                  ]}>
                    <ThemedText style={[styles.typeChipText, { color: clientTypeColor, fontWeight: '600' }]}>
                      {kisiKurum || 'Belirtilmemiş'}
                    </ThemedText>
                  </View>
                </View>
              </View>

              {/* Key information in a single row */}
              <View style={styles.inlineInfoContainer}>
                {/* Role */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Rol:</ThemedText>
                  <ThemedText style={[
                    styles.inlineInfoValue,
                    {
                      color: clientTypeColor,
                      fontWeight: '600'
                    }
                  ]}>
                    {rol || 'Belirtilmemiş'}
                  </ThemedText>
                </View>

                {/* Case Number */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Dosya No:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{dosyaNo || 'Belirtilmemiş'}</ThemedText>
                </View>

                {/* Type */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Tür:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{kisiKurum || 'Belirtilmemiş'}</ThemedText>
                </View>

                {/* ID */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>ID:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>{id.substring(0, 8) || 'Belirtilmemiş'}</ThemedText>
                </View>

                {/* Status */}
                <View style={styles.inlineInfoItem}>
                  <ThemedText style={styles.inlineInfoLabel}>Durum:</ThemedText>
                  <ThemedText style={styles.inlineInfoValue}>Aktif</ThemedText>
                </View>
              </View>
            </View>

            {/* Right section with Dosyaya Git button */}
            <View style={styles.headerRightSection}>
              <TouchableOpacity
                style={styles.dosyayaGitButton}
                onPress={() => navigateToCaseDetail(dosyaNo, item)}
              >
                <Ionicons name="folder-open-outline" size={16} color="#fff" />
                <ThemedText style={styles.dosyayaGitButtonText}>Dosyaya Git</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>
      </TouchableOpacity>
    );
  };

  // Main render
  return (
    <BackgroundWrapper style={styles.container}>

        {/* Main FlatList as the scrollable component */}
        <FlatList
          ref={flatListRef}
          data={sections}
          keyExtractor={item => item.id}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => {
            // Content section
            if (item.type === 'content') {
              // Loading and Error States
              if (loading) {
                return (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={isDark ? '#fff' : Colors.light.tint} />
                    <ThemedText style={styles.loadingText}>Kişiler yükleniyor...</ThemedText>
                  </View>
                );
              } else if (error) {
                return (
                  <View style={styles.errorContainer}>
                    <Ionicons name="alert-circle" size={48} color="#ef4444" />
                    <ThemedText style={styles.errorText}>{error}</ThemedText>
                    <TouchableOpacity
                      style={styles.retryButton}
                      onPress={() => {
                        setLoading(true);
                        setError('');
                        // Reload the page
                        if (Platform.OS === 'web') {
                          window.location.reload();
                        } else {
                          router.replace('/clients');
                        }
                      }}
                    >
                      <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
                    </TouchableOpacity>
                  </View>
                );
              } else if (filteredClients.length === 0) {
                return (
                  <View style={styles.emptyContainer}>
                    <Ionicons name="people" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
                    <ThemedText style={styles.emptyText}>
                      {searchText
                        ? 'Arama kriterlerine uygun kişi bulunamadı.'
                        : 'Henüz kişi bulunmuyor.'}
                    </ThemedText>
                  </View>
                );
              } else {
                // Render the client list
                return (
                  <View style={{ flex: 1, display: 'flex', flexDirection: 'column', width: '100%' }}>
                    {/* Main content area */}
                    <View style={{ flex: 1, width: '100%' }}>
                      {/* Ultra Compact Filter Bar */}
                      {Platform.OS === 'web' && (
                        <View style={styles.ultraCompactFilterBar}>
                          {/* Search Input */}
                          <View style={[
                            styles.searchContainer,
                            isDark && styles.searchInputContainerDark
                          ]}
                            // @ts-ignore - className is valid in web
                            {...(Platform.OS === 'web' ? { className: 'search-container' } : {})}>
                            <Ionicons name="search" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                            <TextInput
                              style={[
                                styles.searchInput,
                                isDark && styles.searchInputDark
                              ]}
                              // @ts-ignore - className is valid in web
                              {...(Platform.OS === 'web' ? { className: 'search-input' } : {})}
                              placeholder="Kişi ara..."
                              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
                              value={searchText}
                              onChangeText={(text) => {
                                // Just update the search text state - no reloading
                                setSearchText(text);

                                // Reset to page 1 when search changes
                                if (currentPage !== 1) {
                                  setCurrentPage(1);
                                }

                                // Save the search text for persistence
                                if (Platform.OS === 'web') {
                                  localStorage.setItem('clientsSearchText', text);
                                  localStorage.setItem('clientsCurrentPage', '1');
                                }
                              }}
                            />
                            {searchText.length > 0 && (
                              <TouchableOpacity
                                style={styles.clearSearchButton}
                                onPress={() => {
                                  // Just clear the search text - no reloading
                                  setSearchText('');

                                  // Reset to page 1
                                  if (currentPage !== 1) {
                                    setCurrentPage(1);
                                  }

                                  // Save the empty search text for persistence
                                  if (Platform.OS === 'web') {
                                    localStorage.setItem('clientsSearchText', '');
                                    localStorage.setItem('clientsCurrentPage', '1');
                                  }
                                }}
                              >
                                <Ionicons name="close-circle" size={18} color={isDark ? '#9ca3af' : '#64748b'} />
                              </TouchableOpacity>
                            )}
                          </View>

                          {/* Filter Controls */}
                          <View style={styles.filterControls}>
                            {/* Filter Button */}
                            <TouchableOpacity
                              style={styles.miniFilterButton}
                              onPress={() => setIsFilterModalVisible(true)}
                            >
                              <Ionicons
                                name="filter"
                                size={20}
                                color={activeFilter !== 'Tümü' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                              />
                            </TouchableOpacity>

                            {/* Sort Button */}
                            <TouchableOpacity
                              style={styles.miniFilterButton}
                              onPress={() => setIsSortModalVisible(true)}
                            >
                              <Ionicons
                                name="swap-vertical"
                                size={20}
                                color={isDark ? '#9ca3af' : '#64748b'}
                              />
                            </TouchableOpacity>
                          </View>
                        </View>
                      )}

                      {/* Kart Görünümü */}
                      <View style={styles.contentContainer}>
                        <View style={styles.cardsContainer}>
                          <FlatList
                            data={Platform.OS === 'web' ? currentClients : displayedClients}
                            renderItem={renderClientItem}
                            keyExtractor={item => item.id}
                            contentContainerStyle={[
                              Platform.OS !== 'web' && { paddingBottom: 80 }
                            ]}
                            showsVerticalScrollIndicator={false}
                            onEndReached={Platform.OS !== 'web' ? loadMoreItems : undefined}
                            onEndReachedThreshold={0.3}
                            scrollEnabled={false} // Disable scrolling to prevent nested scrolling
                            ListFooterComponent={Platform.OS !== 'web' && isLoadingMore ? (
                              <View style={styles.loadingMoreContainer}>
                                <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                                <ThemedText style={styles.loadingMoreText}>Daha fazla yükleniyor...</ThemedText>
                              </View>
                            ) : null}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                );
              }
            }

            // Pagination section - Web only
            if (item.type === 'pagination' && Platform.OS === 'web' && totalPages > 1 && !loading && !error && filteredClients.length > 0) {
              return (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={filteredClients.length}
                  onPageChange={(page) => {
                    // For both web and mobile, just update the state without page reload
                    setCurrentPage(page);

                    // Save all filter states for pagination in localStorage for persistence
                    if (Platform.OS === 'web') {
                      localStorage.setItem('clientsCurrentPage', String(page));
                      localStorage.setItem('clientsSearchText', searchText);
                      localStorage.setItem('clientsActiveFilter', activeFilter);
                      localStorage.setItem('clientsSortBy', sortBy);
                      localStorage.setItem('clientsSortOrder', sortOrder);

                      // Scroll to top when changing pages
                      scrollToTop();
                    }
                  }}
                  itemsPerPage={itemsPerPage}
                />
              );
            }

            // Footer section - Web only
            if (item.type === 'footer' && Platform.OS === 'web' && !loading && !error) {
              return (
                <View style={styles.fullWidthContainer}>
                  <Footer />
                </View>
              );
            }

            return null;
          }}
        />

        {/* Filter Modal */}
        {isFilterModalVisible && (
          <View style={styles.modalOverlay}>
            <TouchableOpacity
              style={styles.modalBackdrop}
              onPress={() => setIsFilterModalVisible(false)}
              activeOpacity={0.7}
            />
            <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>Kişi Türü Filtrele</ThemedText>
                <ThemedText style={styles.modalSubtitle}>Gösterilecek kişi türünü seçin</ThemedText>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setIsFilterModalVisible(false)}
                >
                  <Ionicons name="close" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              </View>

              <View style={styles.modalContent}>
                {/* All Filter Option */}
                <TouchableOpacity
                  style={[styles.modalOption, activeFilter === 'Tümü' && styles.activeModalOption]}
                  onPress={() => {
                    setActiveFilter('Tümü');
                    setIsFilterModalVisible(false);
                    setCurrentPage(1);
                  }}
                >
                  <Ionicons
                    name="people-outline"
                    size={20}
                    color={activeFilter === 'Tümü' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      activeFilter === 'Tümü' && styles.activeModalOptionText
                    ]}>
                      Tüm Kişiler
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Tüm kişileri göster
                    </ThemedText>
                  </View>
                  {activeFilter === 'Tümü' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                {/* Person Filter Option */}
                <TouchableOpacity
                  style={[styles.modalOption, activeFilter === 'Kişi' && styles.activeModalOption]}
                  onPress={() => {
                    setActiveFilter('Kişi');
                    setIsFilterModalVisible(false);
                    setCurrentPage(1);
                  }}
                >
                  <Ionicons
                    name="person-outline"
                    size={20}
                    color={activeFilter === 'Kişi' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      activeFilter === 'Kişi' && styles.activeModalOptionText
                    ]}>
                      Kişiler
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Sadece gerçek kişileri göster
                    </ThemedText>
                  </View>
                  {activeFilter === 'Kişi' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                {/* Institution Filter Option */}
                <TouchableOpacity
                  style={[styles.modalOption, activeFilter === 'Kurum' && styles.activeModalOption]}
                  onPress={() => {
                    setActiveFilter('Kurum');
                    setIsFilterModalVisible(false);
                    setCurrentPage(1);
                  }}
                >
                  <Ionicons
                    name="business-outline"
                    size={20}
                    color={activeFilter === 'Kurum' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      activeFilter === 'Kurum' && styles.activeModalOptionText
                    ]}>
                      Kurumlar
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Sadece kurumları göster
                    </ThemedText>
                  </View>
                  {activeFilter === 'Kurum' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {/* Sort Modal */}
        {isSortModalVisible && (
          <View style={styles.modalOverlay}>
            <TouchableOpacity
              style={styles.modalBackdrop}
              onPress={() => setIsSortModalVisible(false)}
              activeOpacity={0.7}
            />
            <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>Sıralama Seçenekleri</ThemedText>
                <ThemedText style={styles.modalSubtitle}>Kişileri sıralama şeklini seçin</ThemedText>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setIsSortModalVisible(false)}
                >
                  <Ionicons name="close" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
                </TouchableOpacity>
              </View>

              <View style={styles.modalContent}>
                <ThemedText style={styles.modalSectionTitle}>Sıralama Alanı</ThemedText>

                {/* Sort by Name */}
                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'adi' && styles.activeModalOption]}
                  onPress={() => handleSort('adi')}
                >
                  <Ionicons
                    name="person-outline"
                    size={20}
                    color={sortBy === 'adi' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'adi' && styles.activeModalOptionText
                    ]}>
                      Ad {sortBy === 'adi' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Kişileri ada göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'adi' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                {/* Sort by Surname */}
                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'soyad' && styles.activeModalOption]}
                  onPress={() => handleSort('soyad')}
                >
                  <Ionicons
                    name="text-outline"
                    size={20}
                    color={sortBy === 'soyad' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'soyad' && styles.activeModalOptionText
                    ]}>
                      Soyad {sortBy === 'soyad' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Kişileri soyada göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'soyad' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                {/* Sort by Role */}
                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'rol' && styles.activeModalOption]}
                  onPress={() => handleSort('rol')}
                >
                  <Ionicons
                    name="briefcase-outline"
                    size={20}
                    color={sortBy === 'rol' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'rol' && styles.activeModalOptionText
                    ]}>
                      Rol {sortBy === 'rol' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Kişileri role göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'rol' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                {/* Sort by Type */}
                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'kisiKurum' && styles.activeModalOption]}
                  onPress={() => handleSort('kisiKurum')}
                >
                  <Ionicons
                    name="people-outline"
                    size={20}
                    color={sortBy === 'kisiKurum' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'kisiKurum' && styles.activeModalOptionText
                    ]}>
                      Tür {sortBy === 'kisiKurum' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Kişileri türe göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'kisiKurum' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                {/* Sort by Case Number */}
                <TouchableOpacity
                  style={[styles.modalOption, sortBy === 'dosyaNo' && styles.activeModalOption]}
                  onPress={() => handleSort('dosyaNo')}
                >
                  <Ionicons
                    name="folder-outline"
                    size={20}
                    color={sortBy === 'dosyaNo' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                    style={styles.modalOptionIcon}
                  />
                  <View style={styles.modalOptionTextContainer}>
                    <ThemedText style={[
                      styles.modalOptionText,
                      sortBy === 'dosyaNo' && styles.activeModalOptionText
                    ]}>
                      Dosya No {sortBy === 'dosyaNo' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </ThemedText>
                    <ThemedText style={styles.modalOptionDescription}>
                      Kişileri dosya numarasına göre sırala
                    </ThemedText>
                  </View>
                  {sortBy === 'dosyaNo' && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                  )}
                </TouchableOpacity>

                <View style={styles.modalDivider} />

                <ThemedText style={styles.modalSectionTitle}>Sıralama Yönü</ThemedText>

                <View style={styles.sortDirectionContainer}>
                  <TouchableOpacity
                    style={[styles.sortDirectionButton, sortOrder === 'asc' && styles.activeSortDirectionButton]}
                    onPress={() => {
                      // Use the same logic as handleSort for consistency
                      const newSortOrder = 'asc';
                      setSortOrder(newSortOrder);

                      if (Platform.OS === 'web') {
                        localStorage.setItem('clientsSortOrder', newSortOrder);
                        localStorage.setItem('clientsCurrentPage', '1');
                        // Just update the state without page reload
                        setCurrentPage(1);
                      } else {
                        // For mobile, just update the state
                        setCurrentPage(1);
                      }
                      setIsSortModalVisible(false);
                    }}
                  >
                    <Ionicons
                      name="arrow-up"
                      size={20}
                      color={sortOrder === 'asc' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                    />
                    <ThemedText style={[
                      styles.sortDirectionText,
                      sortOrder === 'asc' && styles.activeSortDirectionText
                    ]}>
                      Artan
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.sortDirectionButton, sortOrder === 'desc' && styles.activeSortDirectionButton]}
                    onPress={() => {
                      // Use the same logic as handleSort for consistency
                      const newSortOrder = 'desc';
                      setSortOrder(newSortOrder);

                      if (Platform.OS === 'web') {
                        localStorage.setItem('clientsSortOrder', newSortOrder);
                        localStorage.setItem('clientsCurrentPage', '1');
                        // Just update the state without page reload
                        setCurrentPage(1);
                      } else {
                        // For mobile, just update the state
                        setCurrentPage(1);
                      }
                      setIsSortModalVisible(false);
                    }}
                  >
                    <Ionicons
                      name="arrow-down"
                      size={20}
                      color={sortOrder === 'desc' ? 'white' : (isDark ? '#9ca3af' : '#64748b')}
                    />
                    <ThemedText style={[
                      styles.sortDirectionText,
                      sortOrder === 'desc' && styles.activeSortDirectionText
                    ]}>
                      Azalan
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        )}
    </BackgroundWrapper>
  );
}

// Styles
const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  backgroundImageStyle: {
    opacity: 0.15,
  },
  container: {
    flex: 1,
    paddingTop: 30, // Reduced from 50 to decrease space between navbar and header
    height: '100%', // Ensure container takes full height
    display: 'flex',
    flexDirection: 'column',
  },
  ultraCompactFilterBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 6,
    marginBottom: 2,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
    ...(Platform.OS !== 'web' && {
      paddingHorizontal: 12, // Smaller padding for mobile
      paddingBottom: 4, // Smaller padding for mobile
    }),
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    marginRight: 8,
  },
  searchInputContainerDark: {
    backgroundColor: 'rgba(30,41,59,0.8)',
    borderColor: 'rgba(255,255,255,0.1)',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#1e293b',
    // @ts-ignore - outlineStyle is valid in web
    ...(Platform.OS === 'web' ? { outlineStyle: 'none' } : {}),
  },
  searchInputDark: {
    color: '#f1f5f9',
  },
  clearSearchButton: {
    padding: 4,
  },
  filterControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  miniFilterButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    position: 'relative',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
  },
  activeDot: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.tint,
    borderWidth: 1,
    borderColor: 'white',
  },

  contentContainer: {
    flex: 1,
    padding: 16,
    paddingTop: 16,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
  },
  cardsContainer: {
    padding: 12,
    paddingHorizontal: 16,
    paddingBottom: 80,
    ...(Platform.OS === 'web' && {
      width: '100%', // Take full width of the content container
    }),
    ...(Platform.OS !== 'web' && {
      padding: 8, // Smaller padding for mobile
      paddingHorizontal: 12, // Smaller horizontal padding for mobile
    }),
  },
  fullWidthContainer: {
    width: '100%',
    maxWidth: '100%',
    marginLeft: 0,
    marginRight: 0,
  },
  listContent: {
    paddingBottom: 16,
  },
  clientCard: {
    marginBottom: 12, // Reduced margin
    borderRadius: 12, // Smaller radius
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? {
          width: '100%', // Take full width of container
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)',
          marginLeft: 'auto', // Center the card
          marginRight: 'auto', // Center the card
        }
      : {
          width: '100%',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 }, // Reduced shadow
          shadowOpacity: 0.1,
          shadowRadius: 3, // Reduced shadow radius
          marginBottom: 8, // Even smaller margin for mobile
          borderRadius: 10, // Smaller radius for mobile
          maxWidth: '100%', // Ensure cards don't overflow on mobile
        }
    ),
    elevation: 2,
  },
  cardBlur: {
    borderRadius: 12, // Smaller radius
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    ...(Platform.OS !== 'web' && {
      borderRadius: 10, // Smaller radius for mobile
      borderWidth: StyleSheet.hairlineWidth, // Thinner border for mobile
    }),
  },
  clientTypeIndicator: {
    height: 3, // Reduced height
    width: '100%',
  },
  singleRowHeader: {
    padding: 8,
    paddingHorizontal: 12,
    paddingBottom: 10, // Slightly more padding at the bottom for the inline info
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...(Platform.OS !== 'web' && {
      padding: 6, // Smaller padding for mobile
      paddingHorizontal: 10, // Smaller horizontal padding for mobile
    }),
  },
  headerLeftSection: {
    flex: 4, // Give more space to the left section
    paddingRight: 4,
  },
  headerRightSection: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  clientNameContainer: {
    marginBottom: 4,
  },
  clientTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  clientTitle: {
    fontSize: 16,
    fontWeight: '600',
    flexShrink: 1,
  },
  typeChip: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  typeChipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  inlineInfoContainer: {
    flexDirection: 'row',
    flexWrap: 'nowrap', // Prevent wrapping to keep all items in one row
    marginTop: 4,
    marginBottom: 4,
    justifyContent: 'space-between', // Equal spacing between items
    ...(Platform.OS !== 'web' && {
      marginTop: 2, // Smaller margin for mobile
      marginBottom: 2, // Smaller margin for mobile
      flexDirection: 'column', // Stack items vertically on mobile for better readability
    }),
  },
  inlineInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    width: '20%', // Five items with equal width
    paddingRight: 4, // Add padding to prevent text from touching next item
    ...(Platform.OS !== 'web' && {
      width: '100%', // Full width on mobile since we're stacking vertically
      marginBottom: 4, // Slightly larger margin for mobile
      paddingVertical: 2, // Add some vertical padding for better spacing
    }),
  },
  inlineInfoLabel: {
    fontSize: 10, // Smaller font size to fit in one row
    opacity: 0.7,
    marginRight: 2,
    minWidth: 0, // Remove minimum width to allow more flexibility
    ...(Platform.OS !== 'web' && {
      fontSize: 11, // Keep font size readable on mobile
      minWidth: 100, // Wider labels on mobile for better readability
      marginRight: 8, // More space between label and value on mobile
    }),
  },
  inlineInfoValue: {
    fontSize: 11, // Smaller font size to fit in one row
    fontWeight: '500',
    flex: 1,
    ...(Platform.OS === 'web' && {
      // Web-specific styles
      // @ts-ignore - These are valid CSS properties for web
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    }),
    ...(Platform.OS !== 'web' && {
      fontSize: 12, // Keep font size readable on mobile
      fontWeight: '600', // Make it slightly bolder on mobile
    }),
  },
  dosyayaGitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3182CE', // Blue color for the button
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
    marginTop: 4,
    ...(Platform.OS !== 'web' && {
      paddingHorizontal: 8, // Smaller padding for mobile
      paddingVertical: 4, // Smaller padding for mobile
    }),
  },
  dosyayaGitButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
    marginLeft: 6,
    ...(Platform.OS !== 'web' && {
      fontSize: 11, // Smaller font for mobile
    }),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingMoreText: {
    marginLeft: 8,
    fontSize: 14,
  },
  loadMoreButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'center',
    marginVertical: 16,
  },
  loadMoreButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  modalContainerDark: {
    backgroundColor: '#1e293b',
  },
  modalHeader: {
    marginBottom: 16,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  modalSubtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
  },
  modalCloseButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 4,
  },
  modalContent: {
    marginBottom: 8,
  },
  modalSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  activeModalOption: {
    backgroundColor: `${Colors.light.tint}10`,
  },
  modalOptionIcon: {
    marginRight: 12,
  },
  modalOptionTextContainer: {
    flex: 1,
  },
  modalOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeModalOptionText: {
    color: Colors.light.tint,
  },
  modalOptionDescription: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  modalDivider: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginVertical: 12,
  },
  sortDirectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sortDirectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    width: '48%',
  },
  activeSortDirectionButton: {
    backgroundColor: Colors.light.tint,
    borderColor: Colors.light.tint,
  },
  sortDirectionText: {
    marginLeft: 8,
    fontSize: 14,
  },
  activeSortDirectionText: {
    color: 'white',
    fontWeight: '500',
  },
});