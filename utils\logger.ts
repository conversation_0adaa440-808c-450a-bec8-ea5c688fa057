/**
 * Logger utility to control console output across the application
 * Allows for easy toggling of logs in different environments
 */

import { Platform } from 'react-native';

// Configuration
const config = {
  // Set to true to enable all logs in mobile environments
  enableMobileLogs: true,

  // Set to true to enable all logs in web environments
  enableWebLogs: true,

  // Set log levels to enable/disable
  levels: {
    debug: true,
    info: true,
    warn: true,
    error: true,
    api: true, // Enable API logs for debugging
  }
};

// Check if we should log based on platform and configuration
const shouldLog = (level: keyof typeof config.levels): boolean => {
  // Always log errors regardless of platform
  if (level === 'error') return true;

  // Check if logs are enabled for this platform
  const platformEnabled = Platform.OS === 'web'
    ? config.enableWebLogs
    : config.enableMobileLogs;

  // Check if this log level is enabled
  const levelEnabled = config.levels[level];

  return platformEnabled && levelEnabled;
};

// Logger functions
const logger = {
  debug: (...args: any[]): void => {
    if (shouldLog('debug')) {
      console.debug('[DEBUG]', ...args);
    }
  },

  info: (...args: any[]): void => {
    if (shouldLog('info')) {
      console.info('[INFO]', ...args);
    }
  },

  warn: (...args: any[]): void => {
    if (shouldLog('warn')) {
      console.warn('[WARN]', ...args);
    }
  },

  error: (...args: any[]): void => {
    if (shouldLog('error')) {
      console.error('[ERROR]', ...args);
    }
  },

  api: (...args: any[]): void => {
    if (shouldLog('api')) {
      console.log('[API]', ...args);
    }
  },

  // Enable/disable logs at runtime
  configure: (options: Partial<typeof config>) => {
    Object.assign(config, options);
  }
};

export default logger;
