// This file is a fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight } from 'expo-symbols';
import React from 'react';
import { OpaqueColorValue, StyleProp, ViewStyle } from 'react-native';

// Add your SFSymbol to MaterialIcons mappings here.
const MAPPING = {
  // See MaterialIcons here: https://icons.expo.fyi
  // See SF Symbols in the SF Symbols app on Mac.
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',

  // Tab bar icons
  'folder.fill': 'folder',
  'person.2.fill': 'people',
  'calendar': 'event',
  'building.columns.fill': 'account-balance',
  'bubble.left.fill': 'chat',
  'person.crop.circle.fill': 'account-circle',

  // Other common icons
  'bell.fill': 'notifications',
  'bell': 'notifications-none',
  'gear': 'settings',
  'plus': 'add',
  'plus.circle': 'add-circle',
  'plus.circle.fill': 'add-circle-outline',
  'checkmark.circle': 'check-circle',
  'checkmark.circle.fill': 'check-circle-outline',
  'xmark': 'close',
  'xmark.circle': 'cancel',
  'xmark.circle.fill': 'cancel-outline',
  'arrow.left': 'arrow-back',
  'arrow.right': 'arrow-forward',
  'arrow.up': 'arrow-upward',
  'arrow.down': 'arrow-downward',
  'magnifyingglass': 'search',
  'trash': 'delete',
  'trash.fill': 'delete-outline',
  'pencil': 'edit',
  'pencil.circle': 'edit-outline',
  'info.circle': 'info',
  'info.circle.fill': 'info-outline',
  'exclamationmark.triangle': 'warning',
  'exclamationmark.triangle.fill': 'warning-outline',
  'star': 'star',
  'star.fill': 'star-outline',
  'heart': 'favorite',
  'heart.fill': 'favorite-outline',
  'person': 'person',
  'person.fill': 'person-outline',
  'envelope': 'email',
  'envelope.fill': 'email-outline',
  'phone': 'phone',
  'phone.fill': 'phone-outline',
  'lock': 'lock',
  'lock.fill': 'lock-outline',
  'unlock': 'lock-open',
  'unlock.fill': 'lock-open-outline',
  'clock': 'access-time',
  'clock.fill': 'access-time-filled',
  'calendar.badge.plus': 'event-available',
  'calendar.badge.minus': 'event-busy',
  'doc': 'description',
  'doc.fill': 'description-outline',
  'doc.text': 'article',
  'doc.text.fill': 'article-outline',
  'list.bullet': 'list',
  'list.number': 'format-list-numbered',
  'chart.bar': 'bar-chart',
  'chart.pie': 'pie-chart',
  'location': 'place',
  'location.fill': 'place-outline',
  'link': 'link',
  'photo': 'photo',
  'photo.fill': 'photo-outline',
  'camera': 'camera',
  'camera.fill': 'camera-outline',
  'video': 'videocam',
  'video.fill': 'videocam-outline',
  'mic': 'mic',
  'mic.fill': 'mic-outline',
  'speaker': 'volume-up',
  'speaker.fill': 'volume-up-outline',
  'speaker.slash': 'volume-off',
  'speaker.slash.fill': 'volume-off-outline',
  'play': 'play-arrow',
  'play.fill': 'play-arrow-outline',
  'pause': 'pause',
  'pause.fill': 'pause-outline',
  'stop': 'stop',
  'stop.fill': 'stop-outline',
  'forward': 'fast-forward',
  'forward.fill': 'fast-forward-outline',
  'backward': 'fast-rewind',
  'backward.fill': 'fast-rewind-outline',
  'repeat': 'repeat',
  'shuffle': 'shuffle',
  'wifi': 'wifi',
  'wifi.slash': 'wifi-off',
  'bluetooth': 'bluetooth',
  'bluetooth.slash': 'bluetooth-disabled',
  'battery.100': 'battery-full',
  'battery.25': 'battery-alert',
  'battery.0': 'battery-unknown',
} as Partial<
  Record<
    import('expo-symbols').SymbolViewProps['name'],
    React.ComponentProps<typeof MaterialIcons>['name']
  >
>;

// Burada keyof typeof MAPPING kullanmak yerine, string olarak tanımlayalım
// Böylece olmayan bir ikon adı kullanıldığında varsayılan ikon gösterilecek
export type IconSymbolName = string;

/**
 * An icon component that uses native SFSymbols on iOS, and MaterialIcons on Android and web. This ensures a consistent look across platforms, and optimal resource usage.
 *
 * Icon `name`s are based on SFSymbols and require manual mapping to MaterialIcons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<ViewStyle>;
  weight?: SymbolWeight;
}) {
  // Eğer ikon eşleştirmesi bulunamazsa, varsayılan bir ikon göster ve konsola uyarı yaz
  const iconName = MAPPING[name];
  if (!iconName) {
    console.warn(`IconSymbol: No mapping found for SF Symbol "${name}". Using default icon.`);
    return <MaterialIcons color={color} size={size} name="help-outline" style={style} />;
  }

  return <MaterialIcons color={color} size={size} name={iconName} style={style} />;
}
