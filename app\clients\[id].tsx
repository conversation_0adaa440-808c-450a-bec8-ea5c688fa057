import { StyleSheet, TouchableOpacity, View, TextInput, FlatList, Platform } from 'react-native';
import React, { useState, useRef } from 'react';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Örnek müvekkil verileri - gerçek uygulamada API'den alınacak
const CLIENTS_DATA = [
  {
    id: '1',
    name: '<PERSON>',
    phone: '+90 ************',
    email: '<EMAIL>',
    address: 'Atatürk Cad. No:123, Beşiktaş, İstanbul',
    clientType: 'Bireysel',
    powerOfAttorney: {
      number: 'VEK-2023-1234',
      date: '10.01.2023',
      isActive: true,
    },
    notes: [
      { id: '1', date: '15.03.2023', content: 'İlk görüşme yapıldı. Dava açılması için gerekli belgeler istendi.' },
      { id: '2', date: '20.03.2023', content: 'Belgeler teslim alındı, dava dilekçesi hazırlanıyor.' },
    ],
    cases: ['1', '6'],
    activeCases: 2,
  },
  {
    id: '2',
    name: 'Sarah Williams',
    phone: '+90 ************',
    email: '<EMAIL>',
    address: 'Bağdat Cad. No:456, Kadıköy, İstanbul',
    clientType: 'Bireysel',
    powerOfAttorney: {
      number: 'VEK-2023-5678',
      date: '15.02.2023',
      isActive: true,
    },
    notes: [
      { id: '1', date: '10.02.2023', content: 'Boşanma davası için ilk görüşme yapıldı.' },
      { id: '2', date: '25.02.2023', content: 'Dava açıldı, duruşma tarihi bekleniyor.' },
    ],
    cases: ['2'],
    activeCases: 1,
  },
  {
    id: '3',
    name: 'Michael Davis',
    phone: '+90 ************',
    email: '<EMAIL>',
    address: 'Cumhuriyet Cad. No:321, Şişli, İstanbul',
    clientType: 'Bireysel',
    powerOfAttorney: {
      number: 'VEK-2023-3456',
      date: '20.01.2023',
      isActive: true,
    },
    notes: [
      { id: '1', date: '20.01.2023', content: 'Miras davası için ilk görüşme yapıldı.' },
      { id: '2', date: '10.02.2023', content: 'Dava açıldı, duruşma tarihi bekleniyor.' },
    ],
    cases: ['3'],
    activeCases: 1,
  },
  {
    id: '4',
    name: 'Thompson LLC',
    phone: '+90 ************',
    email: '<EMAIL>',
    address: 'Büyükdere Cad. No:789, Şişli, İstanbul',
    clientType: 'Kurumsal',
    powerOfAttorney: {
      number: 'VEK-2023-9012',
      date: '05.03.2023',
      isActive: true,
    },
    notes: [
      { id: '1', date: '05.03.2023', content: 'Şirket yetkilisi ile iflas davası hakkında görüşüldü.' },
      { id: '2', date: '15.03.2023', content: 'Gerekli belgeler teslim alındı, dava hazırlıkları başladı.' },
    ],
    cases: ['4'],
    activeCases: 1,
  },
  {
    id: '5',
    name: 'Carlos Martinez',
    phone: '+90 ************',
    email: '<EMAIL>',
    address: 'İstiklal Cad. No:654, Beyoğlu, İstanbul',
    clientType: 'Bireysel',
    powerOfAttorney: {
      number: 'VEK-2023-7890',
      date: '15.03.2023',
      isActive: true,
    },
    notes: [
      { id: '1', date: '15.03.2023', content: 'Ceza davası için ilk görüşme yapıldı.' },
      { id: '2', date: '25.03.2023', content: 'Savunma hazırlıkları başladı.' },
    ],
    cases: ['5'],
    activeCases: 1,
  },
];

export default function ClientDetailScreen() {
  const { id } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [activeTab, setActiveTab] = useState('bilgiler');
  const [newNote, setNewNote] = useState('');
  const flatListRef = useRef(null);

  // Müvekkil bilgilerini ID'ye göre bul
  const client = CLIENTS_DATA.find(c => c.id === id);

  if (!client) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
          </TouchableOpacity>
          <ThemedText type="title">Müvekkil Bulunamadı</ThemedText>
          <View style={{ width: 24 }} />
        </ThemedView>
        <ThemedView style={styles.errorContainer}>
          <ThemedText>Müvekkil bilgileri bulunamadı.</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  const handleAddNote = () => {
    if (newNote.trim()) {
      // Gerçek uygulamada API'ye gönderilecek
      console.log('Yeni not eklendi:', newNote);
      setNewNote('');
    }
  };

  // Navigate to case detail page with case data
  const navigateToCaseDetail = (caseId, caseData = null) => {
    console.log('Navigating to case detail:', caseId, caseData);

    // Format the dosyaNo for the API
    const dosyaNo = caseData?.dosyaNo || `${caseId}/2023`;

    // Store the case data in localStorage if available
    if (caseData && Platform.OS === 'web') {
      localStorage.setItem('selectedCaseData', JSON.stringify(caseData));
    }

    // Navigate to the case detail page with the dosyaNo as a query parameter
    router.push(`/cases/${caseId}?dosyaNo=${encodeURIComponent(dosyaNo)}`);
  };

  // Create sections for the FlatList
  const sections = [
    {
      id: 'header',
      type: 'header',
    },
    {
      id: 'tabs',
      type: 'tabs',
    },
    {
      id: 'content',
      type: 'content',
    }
  ];

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Müvekkil Detayı</ThemedText>
        <TouchableOpacity>
          <Ionicons name="create-outline" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
      </ThemedView>

      <FlatList
        ref={flatListRef}
        data={sections}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => {
          // Header section
          if (item.type === 'header') {
            return (
              <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.clientHeaderCard}>
                <View style={styles.clientIconLarge}>
                  {client.clientType === 'Bireysel' ? (
                    <Ionicons name="person" size={40} color={Colors[colorScheme ?? 'light'].tint} />
                  ) : (
                    <Ionicons name="business" size={40} color={Colors[colorScheme ?? 'light'].tint} />
                  )}
                </View>
                <ThemedText type="title" style={styles.clientName}>{client.name}</ThemedText>
                <View style={styles.clientTypeBadge}>
                  <ThemedText style={styles.clientTypeText}>{client.clientType}</ThemedText>
                </View>

                <View style={styles.quickActions}>
                  <TouchableOpacity style={styles.quickActionButton}>
                    <View style={styles.actionIconContainer}>
                      <Ionicons name="call" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                    </View>
                    <ThemedText style={styles.actionText}>Ara</ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.quickActionButton}>
                    <View style={styles.actionIconContainer}>
                      <Ionicons name="mail" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                    </View>
                    <ThemedText style={styles.actionText}>E-posta</ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.quickActionButton}>
                    <View style={styles.actionIconContainer}>
                      <Ionicons name="document-text" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                    </View>
                    <ThemedText style={styles.actionText}>Vekâletname</ThemedText>
                  </TouchableOpacity>
                </View>
              </BlurView>
            );
          }

          // Tabs section
          if (item.type === 'tabs') {
            return (
              <View style={styles.tabContainer}>
                <TouchableOpacity
                  style={[styles.tabButton, activeTab === 'bilgiler' && styles.activeTabButton]}
                  onPress={() => setActiveTab('bilgiler')}
                >
                  <ThemedText style={activeTab === 'bilgiler' ? styles.activeTabText : null}>Bilgiler</ThemedText>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tabButton, activeTab === 'notlar' && styles.activeTabButton]}
                  onPress={() => setActiveTab('notlar')}
                >
                  <ThemedText style={activeTab === 'notlar' ? styles.activeTabText : null}>Görüşme Notları</ThemedText>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tabButton, activeTab === 'davalar' && styles.activeTabButton]}
                  onPress={() => setActiveTab('davalar')}
                >
                  <ThemedText style={activeTab === 'davalar' ? styles.activeTabText : null}>Davalar</ThemedText>
                </TouchableOpacity>
              </View>
            );
          }

          // Content section
          if (item.type === 'content') {
            // Bilgiler Sekmesi
            if (activeTab === 'bilgiler') {
              return (
                <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">İletişim Bilgileri</ThemedText>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="call-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Telefon</ThemedText>
                      <ThemedText>{client.phone}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="mail-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>E-posta</ThemedText>
                      <ThemedText>{client.email}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="location-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Adres</ThemedText>
                      <ThemedText>{client.address}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.divider} />

                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">Vekâletname Bilgileri</ThemedText>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="document-text-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Vekâletname No</ThemedText>
                      <ThemedText>{client.powerOfAttorney.number}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons name="calendar-outline" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Vekâletname Tarihi</ThemedText>
                      <ThemedText>{client.powerOfAttorney.date}</ThemedText>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Ionicons
                      name={client.powerOfAttorney.isActive ? "checkmark-circle-outline" : "close-circle-outline"}
                      size={20}
                      color={client.powerOfAttorney.isActive ? '#4CAF50' : '#F44336'}
                    />
                    <View style={styles.infoContent}>
                      <ThemedText style={styles.infoLabel}>Durum</ThemedText>
                      <ThemedText>{client.powerOfAttorney.isActive ? 'Aktif' : 'Pasif'}</ThemedText>
                    </View>
                  </View>
                </BlurView>
              );
            }

            // Görüşme Notları Sekmesi
            if (activeTab === 'notlar') {
              return (
                <>
                  <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
                    <View style={styles.sectionHeader}>
                      <ThemedText type="subtitle">Yeni Not Ekle</ThemedText>
                    </View>

                    <TextInput
                      style={[styles.noteInput, isDark && styles.noteInputDark]}
                      placeholder="Görüşme notunuzu buraya yazın..."
                      placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
                      multiline
                      numberOfLines={4}
                      value={newNote}
                      onChangeText={setNewNote}
                    />

                    <TouchableOpacity
                      style={[styles.addNoteButton, !newNote.trim() && styles.addNoteButtonDisabled]}
                      onPress={handleAddNote}
                      disabled={!newNote.trim()}
                    >
                      <ThemedText style={styles.addNoteButtonText}>Not Ekle</ThemedText>
                    </TouchableOpacity>
                  </BlurView>

                  <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
                    <View style={styles.sectionHeader}>
                      <ThemedText type="subtitle">Görüşme Notları</ThemedText>
                    </View>

                    {client.notes.length > 0 ? (
                      <View>
                        {client.notes.map((item, index) => (
                          <View key={item.id} style={[styles.noteItem, index < client.notes.length - 1 && styles.noteItemBorder]}>
                            <View style={styles.noteHeader}>
                              <ThemedText style={styles.noteDate}>{item.date}</ThemedText>
                              <TouchableOpacity>
                                <Ionicons name="ellipsis-horizontal" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
                              </TouchableOpacity>
                            </View>
                            <ThemedText style={styles.noteContent}>{item.content}</ThemedText>
                          </View>
                        ))}
                      </View>
                    ) : (
                      <ThemedText style={styles.emptyText}>Henüz görüşme notu bulunmuyor.</ThemedText>
                    )}
                  </BlurView>
                </>
              );
            }

            // Davalar Sekmesi
            if (activeTab === 'davalar') {
              return (
                <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.contentCard}>
                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">Müvekkilin Davaları</ThemedText>
                    <TouchableOpacity style={styles.addButton}>
                      <Ionicons name="add-circle" size={20} color={Colors[colorScheme ?? 'light'].tint} />
                      <ThemedText style={styles.addButtonText}>Yeni Dava</ThemedText>
                    </TouchableOpacity>
                  </View>

                  {client.cases.length > 0 ? (
                    <View>
                      {client.cases.map((caseId, index) => {
                        // Create case data object with dosyaNo
                        const caseData = {
                          id: caseId,
                          dosyaNo: `${caseId}/2023`,  // Format as dosyaNo for API
                          dosyaTur: caseId === '1' ? 'Hukuk' :
                                    caseId === '2' ? 'Aile' :
                                    caseId === '3' ? 'Miras' :
                                    caseId === '4' ? 'İflas' :
                                    caseId === '5' ? 'Ceza' : 'Hukuk',
                          birimAdi: 'Asliye Hukuk Mahkemesi',
                          dosyaDurum: 'Açık'
                        };

                        return (
                          <TouchableOpacity
                            key={caseId}
                            style={[styles.caseItem, index < client.cases.length - 1 && styles.caseItemBorder]}
                            onPress={() => navigateToCaseDetail(caseId, caseData)}
                          >
                            <View style={styles.caseIconContainer}>
                              <Ionicons name="folder" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                            </View>
                            <View style={styles.caseInfo}>
                              <ThemedText type="defaultSemiBold">Dava #{caseId}</ThemedText>
                              <ThemedText style={styles.caseType}>
                                {caseData.dosyaTur}
                              </ThemedText>
                            </View>
                            <Ionicons name="chevron-forward" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
                          </TouchableOpacity>
                        );
                      })}
                    </View>
                  ) : (
                    <ThemedText style={styles.emptyText}>Henüz dava bulunmuyor.</ThemedText>
                  )}
                </BlurView>
              );
            }

            return null;
          }

          return null;
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  clientHeaderCard: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  clientIconLarge: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientName: {
    marginBottom: 8,
  },
  clientTypeBadge: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginBottom: 16,
  },
  clientTypeText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '600',
  },
  quickActions: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  quickActionButton: {
    alignItems: 'center',
  },
  actionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: Colors.light.tint,
  },
  activeTabText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  contentCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 2,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: 16,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: 16,
    color: '#000',
  },
  noteInputDark: {
    color: '#fff',
  },
  addNoteButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  addNoteButtonDisabled: {
    opacity: 0.5,
  },
  addNoteButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  noteItem: {
    paddingVertical: 12,
  },
  noteItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  noteDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  noteContent: {
    lineHeight: 20,
  },
  emptyText: {
    textAlign: 'center',
    opacity: 0.7,
    marginVertical: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    marginLeft: 4,
    color: Colors.light.tint,
    fontWeight: '600',
  },
  caseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  caseItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  caseIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  caseInfo: {
    flex: 1,
  },
  caseType: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
});
