import { StyleSheet, TouchableOpacity, FlatList, View, TextInput, ActivityIndicator, Platform, ScrollView, ImageBackground } from 'react-native';
import React, { useState, useEffect, useCallback, useLayoutEffect, useRef, useMemo } from 'react';
import { router, useFocusEffect, usePathname } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import taskService from '@/services/taskService';
import Pagination from '@/components/Pagination';
import Footer from '@/components/layout/Footer';
import AddTaskModal from '@/components/tasks/AddTaskModal';
import { formatRelativeDate } from '@/utils/dateUtils';
import { showSuccessMessage, showErrorMessage, showInfoMessage, showConfirmDialog } from '@/utils/flashMessageUtils';
import logger from '@/utils/logger';


// Öncelik renkleri
type PriorityType = 'DÜŞÜK' | 'ORTA' | 'YÜKSEK' | 'KRİTİK';
type PriorityColorType = {
  bg: string;
  text: string;
};

// Define Task interface
interface Task {
  id: string;
  title: string;
  description: string;
  priority: PriorityType | string;
  status: string;
  completed: boolean;
  dueDate: string;
  startDate: string;
  type: string;
  location: string;
  repeat: number;
  relatedCase: string | null;
  relatedClient: string | null;
  caseData: any | null;
  clientData: any | null;
}

const PRIORITY_COLORS: Record<PriorityType, PriorityColorType> = {
  'DÜŞÜK': { bg: 'rgba(16, 185, 129, 0.1)', text: '#10B981' },
  'ORTA': { bg: 'rgba(245, 158, 11, 0.1)', text: '#F59E0B' },
  'YÜKSEK': { bg: 'rgba(239, 68, 68, 0.1)', text: '#EF4444' },
  'KRİTİK': { bg: 'rgba(124, 58, 237, 0.1)', text: '#7C3AED' },
};

// Fallback görev verileri (API bağlantısı başarısız olduğunda kullanılır)
const TASKS_DATA: Task[] = [
  {
    id: '1',
    title: 'Örnek Görev 1',
    description: 'Bu bir örnek görev açıklamasıdır.',
    priority: 'ORTA',
    status: 'OPEN',
    completed: false,
    dueDate: new Date(Date.now() + 86400000).toISOString(), // Yarın
    startDate: new Date().toISOString(),
    type: 'GÖREV',
    location: '',
    repeat: 0,
    relatedCase: null,
    relatedClient: null,
    caseData: null,
    clientData: null
  },
  {
    id: '2',
    title: 'Örnek Duruşma',
    description: 'Bu bir örnek duruşma görevidir.',
    priority: 'YÜKSEK',
    status: 'OPEN',
    completed: false,
    dueDate: new Date(Date.now() + 172800000).toISOString(), // 2 gün sonra
    startDate: new Date().toISOString(),
    type: 'DURUŞMA',
    location: 'Ankara Adliyesi',
    repeat: 0,
    relatedCase: '2023/123',
    relatedClient: null,
    caseData: null,
    clientData: null
  },
  {
    id: '3',
    title: 'Örnek Toplantı',
    description: 'Bu bir örnek toplantı görevidir.',
    priority: 'DÜŞÜK',
    status: 'OPEN',
    completed: false,
    dueDate: new Date(Date.now() + 259200000).toISOString(), // 3 gün sonra
    startDate: new Date().toISOString(),
    type: 'TOPLANTI',
    location: 'Ofis',
    repeat: 0,
    relatedCase: null,
    relatedClient: null,
    caseData: null,
    clientData: null
  }
];

export default function TasksScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  usePathname(); // Get current pathname for navigation detection
  const flatListRef = useRef<FlatList>(null); // FlatList reference
  interface Task {
    id: string;
    title: string;
    description: string;
    priority: PriorityType | string;
    status: string;
    completed: boolean;
    dueDate: string;
    startDate: string;
    type: string;
    location: string;
    repeat: number;
    relatedCase: string | null;
    relatedClient: string | null;
    caseData: any | null;
    clientData: any | null;
  }

  // State
  const [activeFilter, setActiveFilter] = useState('TÜMÜ');
  const [searchQuery, setSearchQuery] = useState('');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedTasks, setDisplayedTasks] = useState<Task[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [isSortModalVisible, setIsSortModalVisible] = useState(false);
  const [isAddTaskModalVisible, setIsAddTaskModalVisible] = useState(false);
  const [sortBy, setSortBy] = useState('dueDate');
  const [sortOrder, setSortOrder] = useState('asc'); // Default to ascending (earliest first)
  const itemsPerPage = 10; // Same as Dosyalar page
  const [needsReset, setNeedsReset] = useState(false);
  const [initialMount, setInitialMount] = useState(true);

  // Check if reset is needed when navigating from other tabs
  useEffect(() => {
    const checkNeedsReset = async () => {
      console.log('🔍 Tasks page: Checking if reset needed...');
      if (Platform.OS === 'web') {
        // For web, check localStorage
        const needsResetFlag = localStorage.getItem('tasksNeedsReset');
        console.log('🏷️ Tasks reset flag:', needsResetFlag);
        if (needsResetFlag === 'true') {
          console.log('✅ Tasks page needs reset - setting needsReset to true');
          logger.info('Görevler page needs reset');
          setNeedsReset(true);
          // Clear the flag
          localStorage.removeItem('tasksNeedsReset');
        } else {
          console.log('❌ Tasks page does not need reset');
        }
      } else {
        // For mobile, check AsyncStorage
        try {
          const needsResetFlag = await AsyncStorage.getItem('tasksNeedsReset');
          if (needsResetFlag === 'true') {
            logger.info('Mobile: Görevler page needs reset');
            setNeedsReset(true);
            // Clear the flag
            await AsyncStorage.removeItem('tasksNeedsReset');
          }
        } catch (error) {
          logger.error('Error checking reset state:', error);
        }
      }
    };

    checkNeedsReset();
  }, []);

  // Sayfa yüklenirken sayfayı en üste kaydır ve kayıtlı filtreleri yükle
  useLayoutEffect(() => {
    if (Platform.OS === 'web') {
      window.scrollTo(0, 0);
    }

    // This effect runs when component mounts or when needsReset changes
    console.log('🔄 Tasks reset effect triggered:', { initialMount, needsReset });
    if (initialMount || needsReset) {
      console.log('🚀 Tasks page: Starting reset process...');
      logger.info('Görevler page mounted or needs reset - checking if reset needed');

      // For web: Check if we're coming from pagination (URL has query parameters)
      const hasUrlParams = Platform.OS === 'web' && window.location.href.includes('?');
      console.log('🔗 Tasks pagination navigation detected:', hasUrlParams);

      // For mobile: Always reset if needed
      // For web: Reset if not pagination navigation or if we need to reset
      if (Platform.OS !== 'web' || !hasUrlParams || needsReset) {
        console.log('✅ Tasks page: Performing reset to default state...');
        logger.info('Resetting Görevler page to default state');

        // Reset to default values
        setCurrentPage(1);
        setSearchQuery('');
        setActiveFilter('TÜMÜ');
        setSortBy('dueDate');
        setSortOrder('asc');

        // Clear any stored pagination state in web
        if (Platform.OS === 'web') {
          localStorage.removeItem('tasksCurrentPage');
          localStorage.removeItem('tasksSearchQuery');
          localStorage.removeItem('tasksActiveFilter');
          localStorage.removeItem('tasksSortBy');
          localStorage.removeItem('tasksSortOrder');

          // Clear the needs reset flag
          setNeedsReset(false);
        }

        // Clear any stored state in mobile
        if (Platform.OS !== 'web') {
          const clearMobileState = async () => {
            try {
              await AsyncStorage.removeItem('tasksCurrentPage');
              await AsyncStorage.removeItem('tasksSearchQuery');
              await AsyncStorage.removeItem('tasksActiveFilter');
              await AsyncStorage.removeItem('tasksSortBy');
              await AsyncStorage.removeItem('tasksSortOrder');
              await AsyncStorage.removeItem('tasksNeedsReset');
              await AsyncStorage.removeItem('lastVisitedPath');
            } catch (error) {
              logger.error('Error clearing mobile state:', error);
            }
          };
          clearMobileState();
        }
      } else {
        // We're navigating with pagination on web, restore state
        if (Platform.OS === 'web') {
          const savedActiveFilter = localStorage.getItem('tasksActiveFilter');
          const savedSearchQuery = localStorage.getItem('tasksSearchQuery');
          const savedSortBy = localStorage.getItem('tasksSortBy');
          const savedSortOrder = localStorage.getItem('tasksSortOrder');
          const savedCurrentPage = localStorage.getItem('tasksCurrentPage');

          if (savedActiveFilter) setActiveFilter(savedActiveFilter);
          if (savedSearchQuery) setSearchQuery(savedSearchQuery);
          if (savedSortBy) setSortBy(savedSortBy);
          if (savedSortOrder) setSortOrder(savedSortOrder);
          if (savedCurrentPage) setCurrentPage(parseInt(savedCurrentPage, 10));
        }
      }

      // Mark initial mount as complete only if this was the initial mount
      if (initialMount) {
        setInitialMount(false);
      }
    }
  }, [initialMount, needsReset]);

  // Görevleri getir fonksiyonu - yeniden kullanılabilir
  const fetchTasks = async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      }
      setError('');

      console.log('Görevler yükleniyor...');
      const data = await taskService.getAllTasks();

      // Veri kontrolü
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('Görev verisi bulunamadı veya boş');
        setTasks([]);
        return [];
      }

      console.log(`${data.length} görev yüklendi`);

      // API'den gelen verileri UI formatına dönüştür
      const formattedTasks = data.map(task => {
        if (!task) return null; // Geçersiz görev verisi kontrolü

        return {
          id: task.id,
          title: task.title || 'Başlıksız Görev',
          description: task.description || '',
          priority: taskService.mapPriorityFromApi(task.priority),
          status: task.status || 'OPEN',
          completed: task.status === 'COMPLETED',
          dueDate: task.dueDate || new Date().toISOString(),
          startDate: task.startDate || new Date().toISOString(),
          type: task.type === 'TASK' ? 'GÖREV' :
                task.type === 'HEARING' ? 'DURUŞMA' :
                task.type === 'MEETING' ? 'TOPLANTI' : 'HATIRLATMA',
          location: task.location || '',
          repeat: task.repeat || 0,
          relatedCase: task.caseNumber || null,
          relatedClient: task.clientId || null,
          // Dava ve müvekkil bilgilerini ekle
          caseData: task.caseData || null,
          clientData: task.clientData || null
        };
      }).filter(task => task !== null); // Geçersiz görevleri filtrele

      setTasks(formattedTasks);

      // Görev hatırlatıcılarını planla
      try {
        const taskReminderService = require('@/services/taskReminderService').default;
        await taskReminderService.scheduleAllTaskReminders();
      } catch (reminderErr) {
        console.error('Error scheduling task reminders:', reminderErr);
        // Hatırlatıcı hatası görev listesini etkilemez
      }

      return formattedTasks;
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError('Görevler yüklenirken bir hata oluştu.');
      // Show error message
      showErrorMessage(
        'Hata',
        'Görevler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.'
      );
      // Geliştirme aşamasında örnek verileri kullan
      setTasks(TASKS_DATA);
      return [];
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  // Sayfa yüklenirken görevleri getir ve web için CSS ayarlarını yap
  useEffect(() => {
    fetchTasks();

    // Set up CSS to hide scrollbars on web
    if (Platform.OS === 'web') {
      const style = document.createElement('style');
      style.textContent = `
        /* Hide scrollbars on the tasks page */
        #tasks-container::-webkit-scrollbar {
          display: none !important;
        }

        #tasks-container {
          -ms-overflow-style: none !important;  /* IE and Edge */
          scrollbar-width: none !important;  /* Firefox */
          overflow: hidden !important;
        }

        /* Hide scrollbars on all elements in the tasks page */
        #tasks-container * {
          -ms-overflow-style: none !important;
          scrollbar-width: none !important;
          overflow: visible !important;
        }

        #tasks-container *::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
          height: 0 !important;
        }

        /* Ensure the body doesn't show scrollbars either */
        body::-webkit-scrollbar {
          width: 0 !important;
          height: 0 !important;
        }

        body {
          overflow-y: auto !important;
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
        }
      `;
      document.head.append(style);

      return () => {
        style.remove();
      };
    }
  }, []);

  // Sayfa odaklandığında (görev ekleme sayfasından dönüldüğünde) görevleri kontrol et
  useFocusEffect(
    useCallback(() => {
      const checkRefreshFlag = async () => {
        try {
          const refreshFlag = await AsyncStorage.getItem('refresh_tasks');
          if (refreshFlag === 'true') {
            console.log('Görevleri yenileme bayrağı bulundu, görevler yenileniyor...');
            // Flag'i temizle
            await AsyncStorage.removeItem('refresh_tasks');
            // Görevleri yenile (yükleme göstergesini göstermeden)
            fetchTasks(false);
          }
        } catch (error) {
          console.error('AsyncStorage error:', error);
        }
      };

      checkRefreshFlag();

      return () => {
        // Temizleme işlemi gerekirse burada yapılabilir
      };
    }, [])
  );

  // Sort tasks function
  const sortTasks = useCallback((tasksToSort: Task[]) => {
    return [...tasksToSort].sort((a, b) => {
      let comparison = 0;

      // Sort by the selected field
      if (sortBy === 'dueDate') {
        comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      } else if (sortBy === 'title') {
        comparison = a.title.localeCompare(b.title);
      } else if (sortBy === 'priority') {
        // Priority order: KRİTİK > YÜKSEK > ORTA > DÜŞÜK
        const priorityOrder: Record<string, number> = { 'KRİTİK': 0, 'YÜKSEK': 1, 'ORTA': 2, 'DÜŞÜK': 3 };
        comparison = (priorityOrder[a.priority] || 999) - (priorityOrder[b.priority] || 999);
      } else if (sortBy === 'type') {
        comparison = a.type.localeCompare(b.type);
      }

      // Apply sort order (ascending or descending)
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [sortBy, sortOrder]);

  // Arama ve filtreleme işlemi - useMemo ile optimize edildi
  const filteredTasks = useMemo(() => {
    let result = tasks;

    // Arama sorgusuna göre filtreleme
    if (searchQuery) {
      result = result.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (task.location && task.location.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (task.type && task.type.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Görev tipine göre filtreleme
    if (activeFilter !== 'TÜMÜ') {
      result = result.filter(task => task.type === activeFilter);
    }

    // Apply sorting
    return sortTasks(result);
  }, [searchQuery, activeFilter, tasks, sortTasks]);

  // Filtrelenmiş görevler değiştiğinde mobil için displayedTasks'i güncelle
  useEffect(() => {
    if (Platform.OS !== 'web') {
      // Mobil için ilk sayfayı yükle
      setDisplayedTasks(filteredTasks.slice(0, itemsPerPage));
    }
  }, [filteredTasks, itemsPerPage]);

  // Sayfalama için geçerli sayfadaki görevler
  const currentTasks = Platform.OS === 'web'
    ? filteredTasks.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : displayedTasks;

  // Toplam sayfa sayısı
  const totalPages = Math.ceil(filteredTasks.length / itemsPerPage);

  // Define sections for FlatList
  const sections = [
    { id: 'content', type: 'content' },
    { id: 'pagination', type: 'pagination' },
    { id: 'footer', type: 'footer' }
  ];

  // Mobil için daha fazla yükle
  const loadMoreItems = () => {
    if (isLoadingMore || displayedTasks.length >= filteredTasks.length) return;

    setIsLoadingMore(true);

    // Simule edilmiş yükleme gecikmesi (gerçek uygulamada API çağrısı olabilir)
    setTimeout(() => {
      // Sıralanmış filteredTasks'ten sonraki sayfayı al
      const nextItems = filteredTasks.slice(
        displayedTasks.length,
        displayedTasks.length + itemsPerPage
      );

      setDisplayedTasks([...displayedTasks, ...nextItems]);
      setIsLoadingMore(false);

      // Mobil cihazlarda da sayfayı en üste kaydır
      if (Platform.OS !== 'web' && displayedTasks.length === 0) {
        // FlatList referansı varsa en üste kaydır
        if (flatListRef.current) {
          flatListRef.current.scrollToOffset({ offset: 0, animated: true });
        }
      }
    }, 300);
  };



  // These pagination functions are now handled by the Pagination component

  const navigateToAddTask = () => {
    setIsAddTaskModalVisible(true);
  };

  const navigateToTaskDetail = (id: string) => {
    router.push(`/tasks/${id}`);
  };

  const navigateToCalendar = () => {
    router.push('/tasks/calendar');
  };


  // Tarih formatını düzenle - Unix timestamp veya ISO string destekler
  const formatDate = (dateInput: string | number) => {
    // Use the utility function from dateUtils.js
    return formatRelativeDate(dateInput);
  };

  // Helper function to check if a priority is valid
  const isPriorityValid = (priority: string): priority is PriorityType => {
    return ['DÜŞÜK', 'ORTA', 'YÜKSEK', 'KRİTİK'].includes(priority);
  };

  // Get task type color
  const getTaskTypeColor = (type: string): string => {
    switch (type) {
      case 'GÖREV':
        return '#3B82F6'; // Blue
      case 'DURUŞMA':
        return '#EF4444'; // Red
      case 'TOPLANTI':
        return '#10B981'; // Green
      case 'HATIRLATMA':
        return '#F59E0B'; // Amber
      default:
        return '#6B7280'; // Gray
    }
  };

  const renderTaskItem = ({ item }: { item: Task }) => {
    // Get color based on task type
    const taskTypeColor = getTaskTypeColor(item.type);

    return (
      <TouchableOpacity
        style={styles.taskCard}
        onPress={() => navigateToTaskDetail(item.id)}
        activeOpacity={0.7}
      >
        <BlurView intensity={isDark ? 25 : 40} tint={isDark ? 'dark' : 'light'} style={styles.cardBlur}>
          {/* Compact single row view */}
          <View style={styles.singleRowHeader}>
            {/* Icon */}
            <View style={[
              styles.compactIconContainer,
              {
                backgroundColor: `${taskTypeColor}20`,
                width: 40,
                height: 40,
                borderRadius: 20,
                marginRight: 14
              }
            ]}>
              <Ionicons
                name={
                  item.type === 'GÖREV' ? "checkmark-circle" :
                  item.type === 'DURUŞMA' ? "briefcase" :
                  item.type === 'TOPLANTI' ? "people" : "calendar"
                }
                size={20}
                color={taskTypeColor}
              />
            </View>

            {/* Main info */}
            <View style={[styles.compactInfo, { flex: 1 }]}>
              <ThemedText style={[styles.taskTitle, { fontSize: 16, fontWeight: '600', marginBottom: 4 }]} numberOfLines={1} ellipsizeMode="tail">
                {item.title}
              </ThemedText>
              <View style={styles.compactMeta}>
                <Ionicons name="calendar-outline" size={14} color={isDark ? '#9ca3af' : '#64748b'} />
                <ThemedText style={[styles.taskDate, { fontSize: 13, color: isDark ? '#9ca3af' : '#64748b', marginLeft: 4 }]} numberOfLines={1} ellipsizeMode="tail">
                  {formatDate(item.dueDate)}
                </ThemedText>
              </View>
            </View>

            {/* Type badge */}
            <View style={[
              styles.typeChip,
              {
                backgroundColor: `${taskTypeColor}20`,
                paddingHorizontal: 8,
                paddingVertical: 3,
                borderRadius: 12,
                marginRight: 10
              }
            ]}>
              <ThemedText style={[styles.typeChipText, { color: taskTypeColor, fontSize: 12, fontWeight: '600' }]}>
                {item.type}
              </ThemedText>
            </View>

            {/* Priority badge */}
            <View style={[
              styles.priorityBadge,
              {
                backgroundColor: item.priority && isPriorityValid(item.priority) ? PRIORITY_COLORS[item.priority as PriorityType].bg : 'rgba(156, 163, 175, 0.1)',
                marginRight: 10
              }
            ]}>
              <ThemedText style={[
                styles.priorityText,
                { color: item.priority && isPriorityValid(item.priority) ? PRIORITY_COLORS[item.priority as PriorityType].text : '#9ca3af' }
              ]}>
                {item.priority || 'ORTA'}
              </ThemedText>
            </View>


          </View>


        </BlurView>
      </TouchableOpacity>
    );
  };

  return (
    <ThemedView style={styles.container} id="tasks-container">
      <ImageBackground
        source={require('../../assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        {/* Modern Centered Header */}
        <View style={[
          styles.modernHeader,
          isDark && {
            backgroundColor: 'rgba(17, 24, 39, 0.8)',
            borderBottomColor: 'rgba(255, 255, 255, 0.1)'
          }
        ]}>
          <View style={styles.headerContent}>
            <View style={[
              styles.headerIconContainer,
              isDark && { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
            ]}>
              <Ionicons
                name="calendar"
                size={24}
                color={isDark ? Colors.dark.tint : Colors.light.tint}
              />
            </View>
            <ThemedText style={styles.headerTitle}></ThemedText>
            <View style={[
              styles.headerStatsContainer,
              isDark && { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
            ]}>
              <ThemedText style={styles.headerStats}>
                Görevler
              </ThemedText>
            </View>
          </View>
        </View>

        {/* Ultra Compact Filter Bar - Same as Dosyalar */}
        <View style={styles.ultraCompactFilterBar}>
          {/* Search with Integrated Filters */}
          <View className="search-container" style={[styles.searchContainer, isDark && styles.searchInputContainerDark]}>
            <Ionicons name="search" size={20} color={isDark ? '#60A5FA' : Colors.light.tint} />
            <TextInput
              className="search-input"
              style={[
                styles.searchInput,
                isDark && styles.searchInputDark
              ]}
              placeholder="Görev ara..."
              placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
              value={searchQuery}
              // No special focus handling needed
              onChangeText={(text) => {
                // Just update the search text state - no reloading
                setSearchQuery(text);

                // Reset to page 1 when search changes
                if (currentPage !== 1) {
                  setCurrentPage(1);
                }

                // Save the search text for persistence
                if (Platform.OS === 'web') {
                  localStorage.setItem('tasksSearchQuery', text);
                  localStorage.setItem('tasksCurrentPage', '1');
                }
              }}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={18} color={isDark ? '#9ca3af' : '#64748b'} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.filterControls}>
            {/* Status Filter Button */}
            <TouchableOpacity
              style={styles.miniFilterButton}
              onPress={() => setIsFilterModalVisible(true)}
            >
              <Ionicons
                name={activeFilter === 'TÜMÜ' ? 'options-outline' :
                      activeFilter === 'GÖREV' ? 'checkmark-circle' :
                      activeFilter === 'DURUŞMA' ? 'briefcase' : 'people'}
                size={20}
                color={activeFilter !== 'TÜMÜ' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
              />
              {activeFilter !== 'TÜMÜ' && (
                <View style={styles.activeDot} />
              )}
            </TouchableOpacity>

            {/* Sort Button */}
            <TouchableOpacity
              style={styles.miniFilterButton}
              onPress={() => setIsSortModalVisible(true)}
            >
              <Ionicons
                name={sortOrder === 'asc' ? 'arrow-up' : 'arrow-down'}
                size={20}
                color={(sortBy !== 'dueDate' || sortOrder !== 'asc') ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
              />
              {(sortBy !== 'dueDate' || sortOrder !== 'asc') && (
                <View style={styles.activeDot} />
              )}
            </TouchableOpacity>

            {/* Add Task Button */}
            <TouchableOpacity style={styles.miniActionButton} onPress={navigateToAddTask}>
              <Ionicons name="add-circle" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Main FlatList as the scrollable component */}
        <FlatList
          ref={flatListRef}
          data={sections}
          keyExtractor={item => item.id}
          style={{ flex: 1, overflow: 'hidden' }} // Hide overflow to prevent scrollbars
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          scrollEnabled={Platform.OS !== 'web'} // Disable scrolling on web to prevent scrollbars
          renderItem={({ item }) => {
            // Content section
            if (item.type === 'content') {
              // Loading and Error States
              if (loading) {
                return (
                  <ThemedView style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
                    <ThemedText style={styles.loadingText}>Görevler yükleniyor...</ThemedText>
                  </ThemedView>
                );
              } else if (error) {
                return (
                  <ThemedView style={styles.errorContainer}>
                    <Ionicons name="alert-circle" size={48} color="#EF4444" />
                    <ThemedText style={styles.errorText}>{error}</ThemedText>
                    <TouchableOpacity
                      style={styles.retryButton}
                      onPress={() => fetchTasks()}
                    >
                      <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
                    </TouchableOpacity>
                  </ThemedView>
                );
              } else if (filteredTasks.length === 0) {
                return (
                  <ThemedView style={styles.emptyContainer}>
                    <Ionicons name="calendar-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
                    <ThemedText style={styles.emptyText}>Görev bulunamadı</ThemedText>
                    <TouchableOpacity style={styles.addEmptyButton} onPress={navigateToAddTask}>
                      <ThemedText style={styles.addEmptyButtonText}>Yeni Görev Ekle</ThemedText>
                    </TouchableOpacity>
                  </ThemedView>
                );
              } else {
                // Render the task list
                return (
                  <View style={{ flex: 1, display: 'flex', flexDirection: 'column', width: '100%' }}>
                    {/* Main content area */}
                    <View style={{ flex: 1, width: '100%' }}>
                      {/* Kart Görünümü */}
                      <View style={styles.contentContainer}>
                        <View style={styles.cardsContainer}>
                          <FlatList
                        data={Platform.OS === 'web' ? currentTasks : displayedTasks}
                        renderItem={renderTaskItem}
                        keyExtractor={item => item.id}
                        contentContainerStyle={{
                          width: '100%',
                          ...(Platform.OS !== 'web' && { paddingBottom: 80 })
                        }}
                        style={{ overflow: 'hidden' }} // Hide overflow to prevent scrollbars
                        showsVerticalScrollIndicator={false}
                        showsHorizontalScrollIndicator={false}
                        scrollEnabled={false} // Disable scrolling to prevent nested scrolling
                        onEndReached={Platform.OS !== 'web' ? loadMoreItems : undefined}
                        onEndReachedThreshold={0.3}
                        ListFooterComponent={Platform.OS !== 'web' && isLoadingMore ? (
                          <View style={styles.loadMoreContainer}>
                            <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                            <ThemedText style={styles.loadMoreText}>Daha fazla yükleniyor...</ThemedText>
                          </View>
                        ) : null}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                );
              }
            }

            // Pagination section - Web only
            if (item.type === 'pagination' && Platform.OS === 'web' && totalPages > 1 && !loading && !error && filteredTasks.length > 0) {
              return (
                <View style={styles.contentContainer}>
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={filteredTasks.length}
                    onPageChange={(page) => {
                      // For both web and mobile, just update the state without page reload
                      setCurrentPage(page);

                      // Save all filter states for pagination in localStorage for persistence
                      if (Platform.OS === 'web') {
                        localStorage.setItem('tasksCurrentPage', String(page));
                        localStorage.setItem('tasksSearchQuery', searchQuery);
                        localStorage.setItem('tasksActiveFilter', activeFilter);
                        localStorage.setItem('tasksSortBy', sortBy);
                        localStorage.setItem('tasksSortOrder', sortOrder);

                        // Scroll to top when changing pages
                        window.scrollTo(0, 0);
                      }
                    }}
                    itemsPerPage={itemsPerPage}
                  />
                </View>
              );
            }

            // Footer section - Web only
            if (item.type === 'footer' && Platform.OS === 'web' && !loading && !error) {
              return (
                <View style={styles.fullWidthContainer}>
                  <Footer />
                </View>
              );
            }

            return null;
          }}
        />
      {/* Status Filter Modal */}
      {isFilterModalVisible && (
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            activeOpacity={1}
            onPress={() => setIsFilterModalVisible(false)}
          />
          <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Görev Tipi</ThemedText>
              <ThemedText style={styles.modalSubtitle}>
                Seçili: {activeFilter === 'TÜMÜ' ? 'Tümü' :
                         activeFilter === 'GÖREV' ? 'Görev' :
                         activeFilter === 'DURUŞMA' ? 'Duruşma' : 'Toplantı'}
              </ThemedText>
              <TouchableOpacity onPress={() => setIsFilterModalVisible(false)} style={styles.modalCloseButton}>
                <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
              </TouchableOpacity>
            </View>

            <ScrollView style={{padding: 16, maxHeight: Platform.OS === 'web' ? '80%' : '70%'}}>
              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'TÜMÜ' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'TÜMÜ') {
                    setActiveFilter('TÜMÜ');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('tasksActiveFilter', 'TÜMÜ');
                      // Reset to page 1
                      localStorage.setItem('tasksCurrentPage', '1');
                      // Add a query parameter to indicate this is a filter navigation
                      window.location.href = window.location.pathname + '?filter=TÜMÜ';
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="list"
                  size={20}
                  color={activeFilter === 'TÜMÜ' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'TÜMÜ' && styles.activeModalOptionText
                  ]}>
                    Tümü
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Tüm görev tiplerini göster
                  </ThemedText>
                </View>
                {activeFilter === 'TÜMÜ' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'GÖREV' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'GÖREV') {
                    setActiveFilter('GÖREV');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('tasksActiveFilter', 'GÖREV');
                      // Reset to page 1
                      localStorage.setItem('tasksCurrentPage', '1');
                      // Add a query parameter to indicate this is a filter navigation
                      window.location.href = window.location.pathname + '?filter=GÖREV';
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={activeFilter === 'GÖREV' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'GÖREV' && styles.activeModalOptionText
                  ]}>
                    Görev
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Sadece görevleri göster
                  </ThemedText>
                </View>
                {activeFilter === 'GÖREV' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'DURUŞMA' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'DURUŞMA') {
                    setActiveFilter('DURUŞMA');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('tasksActiveFilter', 'DURUŞMA');
                      // Reset to page 1
                      localStorage.setItem('tasksCurrentPage', '1');
                      // Add a query parameter to indicate this is a filter navigation
                      window.location.href = window.location.pathname + '?filter=DURUŞMA';
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="briefcase"
                  size={20}
                  color={activeFilter === 'DURUŞMA' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'DURUŞMA' && styles.activeModalOptionText
                  ]}>
                    Duruşma
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Sadece duruşmaları göster
                  </ThemedText>
                </View>
                {activeFilter === 'DURUŞMA' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, activeFilter === 'TOPLANTI' && styles.activeModalOption]}
                onPress={() => {
                  // Only reload if changing the filter
                  if (activeFilter !== 'TOPLANTI') {
                    setActiveFilter('TOPLANTI');
                    setIsFilterModalVisible(false);

                    if (Platform.OS === 'web') {
                      // Save the new filter state
                      localStorage.setItem('tasksActiveFilter', 'TOPLANTI');
                      // Reset to page 1
                      localStorage.setItem('tasksCurrentPage', '1');
                      // Add a query parameter to indicate this is a filter navigation
                      window.location.href = window.location.pathname + '?filter=TOPLANTI';
                    }
                  } else {
                    setIsFilterModalVisible(false);
                  }
                }}
              >
                <Ionicons
                  name="people"
                  size={20}
                  color={activeFilter === 'TOPLANTI' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    activeFilter === 'TOPLANTI' && styles.activeModalOptionText
                  ]}>
                    Toplantı
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Sadece toplantıları göster
                  </ThemedText>
                </View>
                {activeFilter === 'TOPLANTI' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      )}

      {/* Sort Modal */}
      {isSortModalVisible && (
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            activeOpacity={1}
            onPress={() => setIsSortModalVisible(false)}
          />
          <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Sıralama</ThemedText>
              <ThemedText style={styles.modalSubtitle}>
                Seçili: {sortBy === 'dueDate' ? 'Tarih' :
                         sortBy === 'title' ? 'Başlık' :
                         sortBy === 'priority' ? 'Öncelik' : 'Görev Tipi'}
                {' '}{sortOrder === 'asc' ? '(Artan)' : '(Azalan)'}
              </ThemedText>
              <TouchableOpacity onPress={() => setIsSortModalVisible(false)} style={styles.modalCloseButton}>
                <Ionicons name="close" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
              </TouchableOpacity>
            </View>

            <ScrollView style={{padding: 16, maxHeight: Platform.OS === 'web' ? '80%' : '70%'}}>
              {/* Sort by Date */}
              <View style={styles.modalSectionTitle}>
                <ThemedText style={styles.modalSectionTitleText}>Tarih</ThemedText>
              </View>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'dueDate' && sortOrder === 'asc' && styles.activeModalOption]}
                onPress={() => {
                  setSortBy('dueDate');
                  setSortOrder('asc');
                  setIsSortModalVisible(false);

                  if (Platform.OS === 'web') {
                    localStorage.setItem('tasksSortBy', 'dueDate');
                    localStorage.setItem('tasksSortOrder', 'asc');
                  }
                }}
              >
                <Ionicons
                  name="arrow-up"
                  size={20}
                  color={sortBy === 'dueDate' && sortOrder === 'asc' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'dueDate' && sortOrder === 'asc' && styles.activeModalOptionText
                  ]}>
                    Önce En Yakın
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Tarihe göre artan sıralama
                  </ThemedText>
                </View>
                {sortBy === 'dueDate' && sortOrder === 'asc' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'dueDate' && sortOrder === 'desc' && styles.activeModalOption]}
                onPress={() => {
                  setSortBy('dueDate');
                  setSortOrder('desc');
                  setIsSortModalVisible(false);

                  if (Platform.OS === 'web') {
                    localStorage.setItem('tasksSortBy', 'dueDate');
                    localStorage.setItem('tasksSortOrder', 'desc');
                  }
                }}
              >
                <Ionicons
                  name="arrow-down"
                  size={20}
                  color={sortBy === 'dueDate' && sortOrder === 'desc' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'dueDate' && sortOrder === 'desc' && styles.activeModalOptionText
                  ]}>
                    Önce En Uzak
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Tarihe göre azalan sıralama
                  </ThemedText>
                </View>
                {sortBy === 'dueDate' && sortOrder === 'desc' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              {/* Sort by Title */}
              <View style={styles.modalSectionTitle}>
                <ThemedText style={styles.modalSectionTitleText}>Başlık</ThemedText>
              </View>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'title' && sortOrder === 'asc' && styles.activeModalOption]}
                onPress={() => {
                  setSortBy('title');
                  setSortOrder('asc');
                  setIsSortModalVisible(false);

                  if (Platform.OS === 'web') {
                    localStorage.setItem('tasksSortBy', 'title');
                    localStorage.setItem('tasksSortOrder', 'asc');
                  }
                }}
              >
                <Ionicons
                  name="arrow-up"
                  size={20}
                  color={sortBy === 'title' && sortOrder === 'asc' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'title' && sortOrder === 'asc' && styles.activeModalOptionText
                  ]}>
                    A-Z
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Başlığa göre alfabetik sıralama
                  </ThemedText>
                </View>
                {sortBy === 'title' && sortOrder === 'asc' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'title' && sortOrder === 'desc' && styles.activeModalOption]}
                onPress={() => {
                  setSortBy('title');
                  setSortOrder('desc');
                  setIsSortModalVisible(false);

                  if (Platform.OS === 'web') {
                    localStorage.setItem('tasksSortBy', 'title');
                    localStorage.setItem('tasksSortOrder', 'desc');
                  }
                }}
              >
                <Ionicons
                  name="arrow-down"
                  size={20}
                  color={sortBy === 'title' && sortOrder === 'desc' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'title' && sortOrder === 'desc' && styles.activeModalOptionText
                  ]}>
                    Z-A
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Başlığa göre ters alfabetik sıralama
                  </ThemedText>
                </View>
                {sortBy === 'title' && sortOrder === 'desc' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              {/* Sort by Priority */}
              <View style={styles.modalSectionTitle}>
                <ThemedText style={styles.modalSectionTitleText}>Öncelik</ThemedText>
              </View>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'priority' && sortOrder === 'asc' && styles.activeModalOption]}
                onPress={() => {
                  setSortBy('priority');
                  setSortOrder('asc');
                  setIsSortModalVisible(false);

                  if (Platform.OS === 'web') {
                    localStorage.setItem('tasksSortBy', 'priority');
                    localStorage.setItem('tasksSortOrder', 'asc');
                  }
                }}
              >
                <Ionicons
                  name="arrow-up"
                  size={20}
                  color={sortBy === 'priority' && sortOrder === 'asc' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'priority' && sortOrder === 'asc' && styles.activeModalOptionText
                  ]}>
                    Düşük → Kritik
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Önceliğe göre artan sıralama
                  </ThemedText>
                </View>
                {sortBy === 'priority' && sortOrder === 'asc' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalOption, sortBy === 'priority' && sortOrder === 'desc' && styles.activeModalOption]}
                onPress={() => {
                  setSortBy('priority');
                  setSortOrder('desc');
                  setIsSortModalVisible(false);

                  if (Platform.OS === 'web') {
                    localStorage.setItem('tasksSortBy', 'priority');
                    localStorage.setItem('tasksSortOrder', 'desc');
                  }
                }}
              >
                <Ionicons
                  name="arrow-down"
                  size={20}
                  color={sortBy === 'priority' && sortOrder === 'desc' ? Colors.light.tint : (isDark ? '#9ca3af' : '#64748b')}
                  style={styles.modalOptionIcon}
                />
                <View style={styles.modalOptionTextContainer}>
                  <ThemedText style={[
                    styles.modalOptionText,
                    sortBy === 'priority' && sortOrder === 'desc' && styles.activeModalOptionText
                  ]}>
                    Kritik → Düşük
                  </ThemedText>
                  <ThemedText style={styles.modalOptionDescription}>
                    Önceliğe göre azalan sıralama
                  </ThemedText>
                </View>
                {sortBy === 'priority' && sortOrder === 'desc' && (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.light.tint} />
                )}
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      )}

      {/* Calendar Button - Fixed position */}
      <TouchableOpacity
        style={styles.calendarFloatingButton}
        onPress={navigateToCalendar}
      >
        <Ionicons name="calendar" size={24} color="#fff" />
      </TouchableOpacity>
      </ImageBackground>

      {/* Add Task Modal */}
      <AddTaskModal
        visible={isAddTaskModalVisible}
        onClose={() => setIsAddTaskModalVisible(false)}
        onSuccess={() => {
          setIsAddTaskModalVisible(false);
          fetchTasks(false);
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 10, // Reduced to match Dosyalar page
    height: '100%', // Ensure container takes full height
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden', // Hide overflow to prevent scrollbars
    position: 'relative', // Ensure proper positioning
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'absolute', // Make it absolute to ensure it covers the entire screen
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  // Modern centered header
  modernHeader: {
    paddingVertical: 8, // Reduced from 12 to make it more compact
    paddingHorizontal: 16,
    marginBottom: 6, // Reduced from 8 to make it more compact
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    ...(Platform.OS !== 'web' && {
      paddingVertical: 6, // Reduced from 10 to make it more compact
      paddingHorizontal: 12,
    }),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerStatsContainer: {
    marginLeft: 0, // Removed margin since we removed the title
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'transparent', // Made transparent since we're using it as the main title now
  },
  headerStats: {
    fontSize: 22, // Increased from 13 to make it more prominent
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  // Ultra Compact Filter Bar
  ultraCompactFilterBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 6,
    marginBottom: 2,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to match duruşmalar page
      maxWidth: 1000, // Reduced from 1200 to 1000 to match duruşmalar page
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
    ...(Platform.OS !== 'web' && {
      paddingHorizontal: 12, // Smaller padding for mobile
      paddingBottom: 4, // Smaller padding for mobile
    }),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 14,
    height: 40,
    flex: 1,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
    // Web-specific styles added via custom CSS in useEffect
  },
  searchInputContainerDark: {
    backgroundColor: 'rgba(30, 41, 59, 0.8)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 15,
    color: '#1F2937',
    marginLeft: 10,
    paddingVertical: 0,
    fontWeight: '500',
    letterSpacing: 0.2, // Slightly improved letter spacing
    // outline is not supported in React Native Web
  },
  searchInputDark: {
    color: '#fff',
  },
  filterControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8, // Use gap for spacing
  },
  miniFilterButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    position: 'relative',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
  },
  miniActionButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }
    ),
    elevation: 2,
  },
  activeDot: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.tint,
    borderWidth: 1,
    borderColor: 'white',
  },
  // Legacy styles kept for compatibility
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    marginRight: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  filterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  activeFilter: {
    backgroundColor: Colors.light.tint,
  },
  activeFilterText: {
    color: 'white',
    fontWeight: '600',
  },
  addButton: {
    padding: 8,
    marginLeft: 8,
  },
  calendarFloatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.tint,
    justifyContent: 'center',
    alignItems: 'center',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  // Content container - Same as Dosyalar and Kişiler
  contentContainer: {
    flex: 1,
    padding: 16,
    paddingTop: 16,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to make it narrower
      maxWidth: 1000, // Reduced from 1200 to 1000 to make it narrower
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
      overflow: 'hidden', // Hide overflow to prevent scrollbars
    }),
  },
  // Kart listesi - Exactly the same as Duruşmalar
  cardsContainer: {
    width: '100%',
    marginBottom: 16,
    ...(Platform.OS !== 'web' && {
      padding: 12, // Less padding for mobile
      paddingTop: 6, // Less top padding for mobile
    }),
  },
  fullWidthContainer: {
    width: '100%',
    maxWidth: '100%',
    marginLeft: 0,
    marginRight: 0,
    overflow: 'hidden', // Hide overflow to prevent scrollbars
    marginTop: 0, // Changed from 16 to 0 to remove gap
  },

  // Task card - Same as Dosyalar
  taskCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: 'white',
    ...(Platform.OS === 'web'
      ? {
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
          width: '100%', // Take full width of container
          marginLeft: 'auto', // Center the card
          marginRight: 'auto', // Center the card
        }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          maxWidth: '100%', // Ensure cards don't overflow on mobile
        }
    ),
    elevation: 2,
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  cardBlur: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 0,
    backgroundColor: 'white',
    ...(Platform.OS !== 'web' && {
      borderRadius: 16, // Same radius for mobile
    }),
  },
  taskTypeIndicator: {
    height: 3, // Reduced height to match cases.tsx
    width: '100%',
  },
  singleRowHeader: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...(Platform.OS !== 'web' && {
      paddingVertical: 14, // Smaller padding for mobile
      paddingHorizontal: 14, // Smaller horizontal padding for mobile
    }),
  },
  headerLeftSection: {
    flex: 4, // Give more space to the left section
    paddingRight: 4,
  },
  taskTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  taskTitle: {
    fontSize: 16, // Increased font size
    fontWeight: '700',
    marginRight: 8, // Added margin
    ...(Platform.OS !== 'web' && {
      fontSize: 14, // Smaller font for mobile
      marginRight: 4, // Smaller margin for mobile
    }),
  },
  typeChip: {
    paddingVertical: 2, // Slightly increased padding
    paddingHorizontal: 6, // Slightly increased padding
    borderRadius: 4, // Smallest radius
    marginLeft: 4, // Minimal margin
  },
  typeChipText: {
    fontSize: 11, // Increased font size
    fontWeight: '600',
  },
  headerRightSection: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  taskMetaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  taskDate: {
    fontSize: 13,
    marginLeft: 4,
    color: '#64748b',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 11,
    fontWeight: '600',
  },


  // Compact styles for client-like cards
  compactIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  compactInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  compactMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  addEmptyButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addEmptyButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  loadMoreContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loadMoreText: {
    marginLeft: 8,
    fontSize: 14,
  },
  // Pagination styles moved to Pagination component

  // Modal styles - Same as Dosyalar page
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: Platform.OS === 'web' ? '40%' : '90%',
    maxWidth: 500,
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  modalContainerDark: {
    backgroundColor: '#1f2937',
  },
  modalHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    flexDirection: 'column',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  modalSubtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
  modalCloseButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 4,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  activeModalOption: {
    backgroundColor: `${Colors.light.tint}10`,
  },
  modalOptionIcon: {
    marginRight: 12,
  },
  modalOptionTextContainer: {
    flex: 1,
  },
  modalOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  activeModalOptionText: {
    color: Colors.light.tint,
  },
  modalOptionDescription: {
    fontSize: 13,
    opacity: 0.7,
    marginTop: 2,
  },
  modalSectionTitle: {
    marginTop: 8,
    marginBottom: 8,
    paddingVertical: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  modalSectionTitleText: {
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.7,
  },
});
