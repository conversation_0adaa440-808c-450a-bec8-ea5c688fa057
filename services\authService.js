import apiClient from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '../utils/logger';
import tokenManager from '../utils/tokenManager';

// Auth servisi
const authService = {
  // Kullanıcı girişi
  login: async (email, password) => {
    try {
      logger.info('Attempting login for email:', email);

      // Validate input
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      // Log request details (without password)
      logger.info('Login request to:', '/auth/user/login');

      const response = await apiClient.post('/auth/user/login', {
        email,
        password
      });

      // Log response (without sensitive data)
      logger.info('Login response status:', response.status);

      // Check if response has expected data
      if (!response.data) {
        logger.error('Login response missing data');
        throw new Error('Invalid response from server');
      }

      if (!response.data.jwt) {
        logger.error('Login response missing JWT token');
        throw new Error('Authentication token not received');
      }

      // JWT token'ı kaydet
      const userData = {
        id: response.data.id,
        name: response.data.name,
        surname: response.data.surname,
        email: email,
        isNewUser: response.data.isNewUser
      };

      // Log user data (without sensitive info)
      logger.info('User authenticated:', {
        id: userData.id,
        name: userData.name,
        email: userData.email
      });

      // Use token manager to set tokens
      await tokenManager.setTokens(response.data.jwt, userData);

      return response.data;
    } catch (error) {
      // Enhanced error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        logger.error('Login error response:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        // The request was made but no response was received
        logger.error('Login error - no response received:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        logger.error('Login error during request setup:', error.message);
      }

      // Re-throw the error for the component to handle
      throw error;
    }
  },

  // Kullanıcı kaydı
  signup: async (userData) => {
    try {
      logger.info('Attempting to register user with email:', userData.email);
      logger.info('Registration data:', {
        ...userData,
        password: '******' // Mask password for security
      });

      // Validate required fields
      const requiredFields = ['name', 'surname', 'email', 'password', 'identityNumber', 'birthDate', 'mobilePhone'];
      for (const field of requiredFields) {
        if (!userData[field]) {
          logger.error(`Signup error: Missing required field: ${field}`);
          throw new Error(`${field} is required`);
        }
      }

      // Ensure identityNumber is a string
      if (typeof userData.identityNumber !== 'string') {
        userData.identityNumber = String(userData.identityNumber);
        logger.info('Converted identityNumber to string:', userData.identityNumber);
      }

      // Log the request URL and data
      logger.info('Sending registration request to:', '/auth/user/create');
      logger.info('Request data:', {
        ...userData,
        password: '******' // Mask password for security
      });

      // Send registration request
      const response = await apiClient.post('/auth/user/create', userData);

      // Log the response
      logger.info('Registration response status:', response.status);
      logger.info('Registration response data:', response.data);

      logger.info('Registration successful for:', userData.email);
      return response.data;
    } catch (error) {
      // Enhanced error logging
      logger.error('Signup error:', error.message);

      if (error.response) {
        logger.error('Signup error response:', {
          status: error.response.status,
          data: JSON.stringify(error.response.data)
        });
      } else if (error.request) {
        logger.error('Signup error - no response received:', error.request);
      } else {
        logger.error('Signup error during request setup:', error.message);
      }

      // Log the stack trace
      logger.error('Error stack trace:', error.stack);

      throw error;
    }
  },

  // Şifremi unuttum
  forgotPassword: async (email) => {
    try {
      // Make sure the email is in the correct format
      const cleanEmail = String(email).trim().toLowerCase();

      logger.info('Sending forgot password OTP to email:', cleanEmail);

      // Make sure we're not sending any auth token with this request
      // since it's part of the unauthenticated flow
      const response = await apiClient.post('/auth/user/forgot-password/send-otp',
        { email: cleanEmail },
        {
          headers: {
            'Content-Type': 'application/json',
            // Explicitly remove Authorization header for this request
            'Authorization': undefined
          }
        }
      );

      logger.info('Forgot password response:', response.data);

      return response.data;
    } catch (error) {
      logger.error('Forgot password error:', error);

      // Log more details about the error
      if (error.response) {
        logger.error('Error response data:', JSON.stringify(error.response.data));
        logger.error('Error response status:', error.response.status);
      }

      throw error;
    }
  },

  // OTP doğrulama
  validateOtp: async (otpValue, otpEmail) => {
    try {
      logger.info('Validating OTP with:', { otpValue, otpEmail });

      // Make sure the OTP and email are in the correct format
      const cleanOtp = String(otpValue).trim();
      const cleanEmail = String(otpEmail).trim().toLowerCase();

      const requestBody = {
        otpValue: cleanOtp,
        otpEmail: cleanEmail
      };

      logger.info('Request body for validate-otp:', JSON.stringify(requestBody));

      // Use react-native-axios directly to bypass any interceptors or middleware
      const axios = require('react-native-axios');

      // Make a direct API call to the endpoint
      const response = await axios.post(
        'http://193.35.154.97:4244/auth/user/forgot-password/validate-otp',
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      logger.info('Validate OTP direct response:', JSON.stringify(response.data));

      // Check if the response contains a JWT token, which indicates success
      if (response.data && response.data.jwt) {
        logger.info('OTP validation successful, JWT token received');

        // Create a validatedOtp object from the response data
        // This format must match exactly what the update-password endpoint expects
        const validatedOtp = {
          otpValue: cleanOtp,
          otpEmail: cleanEmail
        };

        // Also add additional fields for our internal use
        validatedOtp.id = "otp-" + Date.now();
        validatedOtp.createdAt = new Date().toISOString();
        validatedOtp.jwt = response.data.jwt;

        // Return a response with the validatedOtp property that the UI expects
        return {
          validatedOtp: validatedOtp,
          responseMessage: response.data.responseMessage || "OTP geçerli.",
          timestamp: response.data.timestamp || new Date().toISOString()
        };
      }

      // If we got a response but no JWT, it means the OTP is invalid
      return {
        validatedOtp: null,
        responseMessage: response.data?.responseMessage || 'Doğrulama kodu geçersiz veya süresi dolmuş.',
        timestamp: new Date().toISOString(),
        error: true
      };
    } catch (error) {
      logger.error('Validate OTP error:', error);

      // Log more details about the error
      if (error.response) {
        logger.error('Error response data:', JSON.stringify(error.response.data));
        logger.error('Error response status:', error.response.status);

        // No mock OTP creation for 401 errors - always return proper error

        // Convert error response to a more user-friendly format
        if (error.response.status === 401) {
          return {
            validatedOtp: null,
            responseMessage: error.response.data?.responseMessage ||
                            error.response.data?.message ||
                            'Doğrulama kodu geçersiz veya süresi dolmuş.',
            timestamp: new Date().toISOString(),
            error: true
          };
        }
      }

      throw error;
    }
  },

  // Şifre güncelleme
  updatePassword: async (password, validatedOtp) => {
    try {
      // No mock OTP handling - always use real API

      // Use react-native-axios directly to bypass any interceptors or middleware
      const axios = require('react-native-axios');

      logger.info('Updating password with validatedOtp:', {
        id: validatedOtp.id,
        otpEmail: validatedOtp.otpEmail,
        // Don't log the actual OTP value for security
        hasOtpValue: !!validatedOtp.otpValue,
        hasJwt: !!validatedOtp.jwt
      });

      // Use only Content-Type header without Authorization
      // The API doesn't expect an Authorization header for this endpoint
      const headers = {
        'Content-Type': 'application/json'
      };

      // Log that we're not using JWT token for this endpoint
      logger.info('Not using JWT token for authorization as this endpoint does not require it');

      // Log the full validatedOtp object for debugging (except sensitive data)
      logger.info('Full validatedOtp object:', {
        id: validatedOtp.id,
        otpEmail: validatedOtp.otpEmail,
        createdAt: validatedOtp.createdAt,
        hasOtpValue: !!validatedOtp.otpValue,
        hasJwt: !!validatedOtp.jwt,
        keys: Object.keys(validatedOtp)
      });

      // Prepare the request body according to the expected format
      const requestBody = {
        password,
        validatedOtp: {
          otpValue: validatedOtp.otpValue || validatedOtp.otp || '',
          otpEmail: validatedOtp.otpEmail || ''
        }
      };

      // Ensure the validatedOtp has the required fields
      if (!requestBody.validatedOtp.otpValue || !requestBody.validatedOtp.otpEmail) {
        logger.error('ValidatedOtp is missing required fields');
        throw new Error('ValidatedOtp is missing required fields: otpValue or otpEmail');
      }

      logger.info('Update password request body:', {
        password: '********', // Don't log the actual password
        validatedOtp: {
          otpEmail: requestBody.validatedOtp.otpEmail,
          hasOtpValue: !!requestBody.validatedOtp.otpValue
        }
      });

      // Make a direct API call to the endpoint
      const response = await axios.post(
        'http://193.35.154.97:4244/auth/user/forgot-password/update-password',
        requestBody,
        { headers }
      );

      logger.info('Update password direct response:', JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      logger.error('Update password error:', error);

      // Log more details about the error
      if (error.response) {
        logger.error('Error response data:', JSON.stringify(error.response.data));
        logger.error('Error response status:', error.response.status);
      }

      // No mock success response on error - always throw the real error

      throw error;
    }
  },

  // Kullanıcı çıkışı
  logout: async () => {
    try {
      // Use token manager to clear tokens
      await tokenManager.clearTokens();
      return true;
    } catch (error) {
      logger.error('Logout error:', error);
      throw error;
    }
  },

  // Kullanıcı bilgilerini getir
  getCurrentUser: async () => {
    try {
      // Use token manager to get user data
      return await tokenManager.getUserData();
    } catch (error) {
      logger.error('Get current user error:', error);
      return null;
    }
  },

  // Kullanıcının giriş yapmış olup olmadığını kontrol et
  isAuthenticated: async () => {
    try {
      // Use token manager to get token
      const token = await tokenManager.getToken();
      return !!token;
    } catch (error) {
      logger.error('Is authenticated error:', error);
      return false;
    }
  },

  // E-posta doğrulama
  verifyEmail: async (otp, email) => {
    try {
      logger.info('Verifying email with OTP for:', email, 'OTP:', otp);

      // Send verification request to the API
      const response = await apiClient.post('/auth/user/verify-email', {
        otp,
        email
      });

      // Log the complete response for debugging
      logger.info('Email verification response:', JSON.stringify(response.data));

      // Check if the response has the expected format
      if (!response.data) {
        logger.error('Verification response is empty or invalid');
        throw new Error('Invalid response from server');
      }

      // If verification is successful (verified is true), log the success
      if (response.data.verified === true) {
        logger.info('Verification successful, personId:', response.data.personId);
        logger.info('User will be redirected to login screen');
      } else {
        logger.warn('Verification failed, verified=false in response');
      }

      // Return the original response data
      return response.data;
    } catch (error) {
      logger.error('Email verification error:', error.message);

      // Handle 401 errors specially - convert them to a verified:false response
      // instead of throwing an error
      if (error.response && error.response.status === 401) {
        logger.warn('Received 401 error, converting to verified:false response');

        // Create a response object that mimics a verified:false response
        return {
          verified: false,
          responseMessage: error.response.data?.responseMessage ||
                          error.response.data?.message ||
                          'Doğrulama kodu hatalı veya geçersiz.',
          timestamp: new Date().toISOString()
        };
      }

      // For other errors, log them and rethrow
      if (error.response) {
        logger.error('Error response data:', JSON.stringify(error.response.data));
      }
      throw error;
    }
  },

  // OTP kodunu gönder (ilk kez)
  sendOtp: async (email) => {
    try {
      logger.info('Sending initial OTP for email:', email);

      try {
        // First try the dedicated send-otp endpoint
        const response = await apiClient.post('/auth/user/send-otp', {
          email
        });

        logger.info('Send OTP response:', response.data);
        return response.data;
      } catch (endpointError) {
        // If the dedicated endpoint doesn't exist, try the resend endpoint
        logger.warn('send-otp endpoint not available, trying resend-otp instead:', endpointError.message);

        try {
          const fallbackResponse = await apiClient.post('/auth/user/resend-otp', {
            email
          });

          logger.info('Send OTP fallback response:', fallbackResponse.data);
          return fallbackResponse.data;
        } catch (resendError) {
          // If both endpoints fail, try the forgot-password OTP endpoint as a last resort
          logger.warn('resend-otp endpoint also failed, trying forgot-password endpoint:', resendError.message);

          const lastResortResponse = await apiClient.post('/auth/user/forgot-password/send-otp', {
            email
          });

          logger.info('Send OTP last resort response:', lastResortResponse.data);
          return lastResortResponse.data;
        }
      }
    } catch (error) {
      logger.error('Send OTP error:', error);
      throw error;
    }
  },

  // OTP kodunu yeniden gönder
  resendOtp: async (email) => {
    try {
      logger.info('Resending OTP for email:', email);

      const response = await apiClient.post('/auth/user/resend-otp', {
        email
      });

      logger.info('Resend OTP response:', response.data);

      return response.data;
    } catch (error) {
      logger.error('Resend OTP error:', error);
      throw error;
    }
  }
};

export default authService;
