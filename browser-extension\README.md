# HukApp Tarayıcı Extension'ı

Bu extension, HukApp kullanıcılarının UYAP sistemindeki oturum bilgilerini (JSESSIONID) otomatik olarak HukApp sistemine aktarmasını sağlar.

## Özellikler

- Kullanıcı kaydı ve girişi
- UYAP sayfalarında otomatik JSESSIONID yakalama
- JSESSIONID'yi HukApp API'sine gönderme
- KVKK aydınlatma metni

## Kurulum

### Geliştirici Modu

1. Bu repository'yi bilgisayarınıza klonlayın
2. Chrome tarayıcısında `chrome://extensions/` adresine gidin
3. Sağ üst köşedeki "Geliştirici modu" seçeneğini etkinleştirin
4. "Paketlenmemiş öğe yükle" butonuna tıklayın
5. Klonladığınız repository'deki `browser-extension` klasörünü seçin

### Chrome Web Store (Yayınlandığında)

1. Chrome Web Store'da "HukApp Extension" araması yapın
2. "Chrome'a Ekle" butonuna tıklayın

## Kullanım

1. Extension'ı yükledikten sonra, tarayıcı araç çubuğundaki HukApp ikonuna tıklayın
2. HukApp hesabınızla giriş yapın veya yeni bir hesap oluşturun
3. UYAP sistemine giriş yaptığınızda, extension otomatik olarak JSESSIONID'nizi HukApp sistemine gönderecektir

## Geliştirme

### Proje Yapısı

- `manifest.json`: Extension yapılandırması
- `background.js`: Arka planda çalışan script
- `content.js`: UYAP sayfalarında çalışan script
- `popup.html`: Extension popup arayüzü
- `popup.css`: Popup stilleri
- `popup.js`: Popup işlevselliği

### API Entegrasyonu

Extension, HukApp API'si ile iletişim kurar:

- `/api/auth/login`: Kullanıcı girişi
- `/api/auth/register`: Kullanıcı kaydı
- `/api/extension/session`: JSESSIONID gönderimi

## Güvenlik

- Extension, kullanıcı bilgilerini ve JSESSIONID'yi güvenli bir şekilde saklar ve iletir
- Tüm API istekleri HTTPS üzerinden yapılır
- Kullanıcı bilgileri yerel olarak Chrome'un güvenli depolama alanında saklanır

## KVKK Uyumluluğu

Extension, KVKK (Kişisel Verilerin Korunması Kanunu) gerekliliklerine uygun olarak tasarlanmıştır:

- Kullanıcılar kayıt sırasında KVKK aydınlatma metnini kabul etmelidir
- Kişisel veriler yalnızca belirtilen amaçlar doğrultusunda işlenir
- Kullanıcılar istedikleri zaman hesaplarını silebilir ve verilerinin silinmesini talep edebilir
