// Background script - extension'ın arka planda çalışan kısmı

// API endpoint
const API_URL = 'http://*************:4244';

// JSESSIONID'yi dinle ve ekrana yazdır
chrome.cookies.onChanged.addListener(function(changeInfo) {
  console.log('<PERSON><PERSON>:', changeInfo);
  console.log('Cookie domain:', changeInfo.cookie.domain);
  console.log('Cookie name:', changeInfo.cookie.name);
  console.log('Cookie removed:', changeInfo.removed);

  // Herhangi bir JSESSIONID cookie'si değiştiğinde
  if (changeInfo.cookie.name === 'JSESSIONID' &&
      changeInfo.cookie.domain.includes('uyap.gov.tr') &&
      !changeInfo.removed) {
    console.log('JSESSIONID cookie değişikliği tespit edildi');

    // JSESSIONID değerini konsola yazdır
    console.log('JSESSIONID bulundu:', {
      value: changeInfo.cookie.value,
      domain: changeInfo.cookie.domain,
      path: changeInfo.cookie.path,
      secure: changeInfo.cookie.secure,
      httpOnly: changeInfo.cookie.httpOnly,
      timestamp: new Date().toISOString()
    });

    // Bildirim göster - JSESSIONID'nin ilk 10 karakterini göster
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon128.png',
      title: 'JSESSIONID Bulundu',
      message: `JSESSIONID: ${changeInfo.cookie.value.substring(0, 10)}... (${changeInfo.cookie.domain})`,
      contextMessage: 'Test amaçlı gösterim'
    });

    // Content script'e mesaj gönder
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'jsessionIdFound',
          jsessionId: changeInfo.cookie.value,
          domain: changeInfo.cookie.domain
        });
      }
    });

    // Kullanıcı giriş yapmış mı kontrol et
    chrome.storage.local.get(['token', 'userId'], function(result) {
      if (result.token && result.userId) {
        console.log('Kullanıcı giriş yapmış, JSESSIONID API\'ye gönderiliyor');

        // JSESSIONID'yi API'ye gönder - yeni endpoint: /api/user/sync
        fetch(`${API_URL}/api/user/sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${result.token}`
          },
          body: JSON.stringify({
            jsid: changeInfo.cookie.value
          })
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          console.log('JSESSIONID başarıyla gönderildi:', data);

          // Bildirim göster
          chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon128.png',
            title: 'HukApp Extension',
            message: 'Oturum bilgileriniz başarıyla kaydedildi.',
            contextMessage: 'HukApp ile senkronize edildi'
          });
        })
        .catch(error => {
          console.error('JSESSIONID gönderme hatası:', error);
        });
      } else {
        console.warn('Kullanıcı giriş yapmamış, JSESSIONID gönderilmedi');
      }
    });
  }
});

// Extension ilk yüklendiğinde
chrome.runtime.onInstalled.addListener(function() {
  console.log('HukApp Extension yüklendi');

  // Mevcut tüm cookie'leri kontrol et
  checkAllCookies();
});

// Tüm cookie'leri kontrol et
function checkAllCookies() {
  console.log('Tüm cookie\'ler kontrol ediliyor...');

  chrome.cookies.getAll({}, function(allCookies) {
    console.log('Tüm cookie\'ler:', allCookies);

    // UYAP domain'indeki cookie'leri filtrele
    const uyapCookies = allCookies.filter(cookie =>
      cookie.domain.includes('uyap.gov.tr')
    );
    console.log('UYAP cookie\'ler:', uyapCookies);

    // JSESSIONID cookie'sini bul
    const jsessionCookie = uyapCookies.find(cookie =>
      cookie.name === 'JSESSIONID'
    );
    console.log('JSESSIONID cookie:', jsessionCookie);

    // Guest cookie'sini bul
    const guestCookie = uyapCookies.find(cookie =>
      cookie.name === 'guest'
    );
    console.log('Guest cookie:', guestCookie);

    // Eğer JSESSIONID cookie'si varsa ve guest cookie'si yoksa, kullanıcı giriş yapmış demektir
    const isLoggedIn = jsessionCookie && !guestCookie;
    console.log('Kullanıcı giriş durumu:', isLoggedIn ? 'Giriş yapılmış' : 'Giriş yapılmamış');

    if (isLoggedIn && jsessionCookie) {
      console.log('Kullanıcı giriş yapmış, JSESSIONID bulundu:', jsessionCookie.value);

      // Kullanıcı giriş yapmış mı kontrol et
      chrome.storage.local.get(['token', 'userId'], function(result) {
        if (result.token && result.userId) {
          console.log('Kullanıcı giriş yapmış, JSESSIONID API\'ye gönderiliyor');

          // JSESSIONID'yi API'ye gönder
          fetch(`${API_URL}/api/user/sync`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${result.token}`
            },
            body: JSON.stringify({
              jsid: jsessionCookie.value
            })
          })
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log('JSESSIONID başarıyla gönderildi:', data);

            // Bildirim göster
            chrome.notifications.create({
              type: 'basic',
              iconUrl: 'icons/icon128.png',
              title: 'HukApp Extension',
              message: 'Oturum bilgileriniz başarıyla kaydedildi.',
              contextMessage: 'HukApp ile senkronize edildi'
            });
          })
          .catch(error => {
            console.error('JSESSIONID gönderme hatası:', error);
          });
        } else {
          console.warn('Kullanıcı giriş yapmamış, JSESSIONID gönderilmedi');
        }
      });
    }
  });
}

// Browser başladığında
chrome.runtime.onStartup.addListener(function() {
  console.log('Browser başladı, cookie\'ler kontrol ediliyor...');
  checkAllCookies();
});

// Tab güncellendiğinde
chrome.tabs.onUpdated.addListener(function(_tabId, changeInfo, tab) {
  // Sayfa tamamen yüklendiğinde
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('uyap.gov.tr')) {
    console.log('UYAP sayfası yüklendi, cookie\'ler kontrol ediliyor...', tab.url);
    checkAllCookies();
  }
});

// Extension simgesine tıklandığında
chrome.action.onClicked.addListener(function() {
  // Kullanıcı giriş yapmış mı kontrol et
  chrome.storage.local.get(['token'], function(result) {
    if (!result.token) {
      // Giriş yapmamışsa popup'ı aç
      chrome.action.setPopup({ popup: 'popup.html' });
    }
  });
});

// Content script'ten gelen mesajları dinle
chrome.runtime.onMessage.addListener(function(request, _sender, sendResponse) {
  console.log('Background script mesaj aldı:', request);

  if (request.action === 'getJsessionCookies') {
    // Tüm cookie'leri al
    chrome.cookies.getAll({}, function(allCookies) {
      // UYAP domain'indeki cookie'leri filtrele
      const uyapCookies = allCookies.filter(cookie =>
        cookie.domain.includes('uyap.gov.tr')
      );
      console.log('UYAP cookie\'ler alındı, content script\'e gönderiliyor:', uyapCookies);

      // JSESSIONID cookie'sini bul
      const jsessionCookie = uyapCookies.find(cookie =>
        cookie.name === 'JSESSIONID'
      );
      console.log('JSESSIONID cookie:', jsessionCookie);

      // Guest cookie'sini bul
      const guestCookie = uyapCookies.find(cookie =>
        cookie.name === 'guest'
      );
      console.log('Guest cookie:', guestCookie);

      // Eğer JSESSIONID cookie'si varsa ve guest cookie'si yoksa, kullanıcı giriş yapmış demektir
      const isLoggedIn = jsessionCookie && !guestCookie;

      // Cookie'lerin value değerlerini string olarak gönder
      const simplifiedCookies = jsessionCookie ? [{
        name: jsessionCookie.name,
        value: jsessionCookie.value,
        domain: jsessionCookie.domain,
        path: jsessionCookie.path,
        isLoggedIn: isLoggedIn
      }] : [];

      console.log('Sadeleştirilmiş JSESSIONID cookie\'leri:', simplifiedCookies);
      sendResponse({ cookies: simplifiedCookies });
    });

    // Asenkron yanıt için true döndür
    return true;
  }

  if (request.action === 'checkJsessionId') {
    // Bu mesaj için özel bir işlem yapmıyoruz, sadece log
    console.log('checkJsessionId mesajı alındı, domain:', request.domain);
  }

  if (request.action === 'checkLoginStatus') {
    console.log('checkLoginStatus mesajı alındı, giriş durumu kontrol ediliyor...');

    // Tüm cookie'leri al
    chrome.cookies.getAll({}, function(allCookies) {
      // UYAP domain'indeki cookie'leri filtrele
      const uyapCookies = allCookies.filter(cookie =>
        cookie.domain.includes('uyap.gov.tr')
      );
      console.log('UYAP cookie\'ler (background):', uyapCookies);

      // JSESSIONID cookie'sini bul
      const jsessionCookie = uyapCookies.find(cookie =>
        cookie.name === 'JSESSIONID'
      );
      console.log('JSESSIONID cookie (background):', jsessionCookie);

      // Guest cookie'sini bul
      const guestCookie = uyapCookies.find(cookie =>
        cookie.name === 'guest'
      );
      console.log('Guest cookie (background):', guestCookie);

      // Eğer JSESSIONID cookie'si varsa ve guest cookie'si yoksa, kullanıcı giriş yapmış demektir
      const isLoggedIn = jsessionCookie && !guestCookie;

      console.log('Kullanıcı giriş durumu:', isLoggedIn ? 'Giriş yapılmış' : 'Giriş yapılmamış');

      // Yanıt gönder
      sendResponse({
        isLoggedIn: isLoggedIn,
        jsessionCookie: jsessionCookie,
        guestCookie: guestCookie
      });
    });

    // Asenkron yanıt için true döndür
    return true;
  }
});
