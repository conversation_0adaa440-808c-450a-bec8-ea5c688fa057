import { StyleSheet, TouchableOpacity, View, Text, Dimensions, Animated, Platform, ImageBackground, FlatList } from 'react-native';
import React, { useContext, useEffect, useState, useRef } from 'react';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthContext } from '../_layout';
import Footer from '@/components/layout/Footer';

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const { logout } = useContext(AuthContext);
  const isDark = colorScheme === 'dark';
  const { width, height } = Dimensions.get('window');
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];
  const flatListRef = useRef<FlatList>(null);

  // Define sections for FlatList
  const sections = [
    { id: 'stats', type: 'stats' },
    { id: 'quickActions', type: 'quickActions' },
    { id: 'upcomingHearings', type: 'upcomingHearings' },
    { id: 'recentDocuments', type: 'recentDocuments' },
    { id: 'pendingTasks', type: 'pendingTasks' },
    { id: 'recentActivities', type: 'recentActivities' },
    { id: 'footer', type: 'footer' }
  ];

  // Animate the dashboard when it mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const handleLogout = () => {
    logout();
    router.replace('/auth/login');
  };

  const navigateToAddCase = () => {
    router.push('/cases/add');
  };

  const navigateToAddClient = () => {
    router.push('/clients/add');
  };

  const navigateToAddEvent = () => {
    router.push('/events/add');
  };

  // Sample data
  const upcomingHearings = [
    { id: '1', title: 'Smith vs. Johnson', date: '15 Haz 2023', time: '10:00', location: 'Duruşma Salonu 3B' },
    { id: '2', title: 'Williams Boşanma', date: '20 Haz 2023', time: '14:30', location: 'Duruşma Salonu 5A' },
    { id: '3', title: 'Martinez Ceza Davası', date: '18 Haz 2023', time: '09:00', location: 'Duruşma Salonu 2C' },
  ];

  const recentDocuments = [
    { id: '1', title: 'Smith_Şikayet.pdf', date: '10 Haz 2023', case: 'Smith vs. Johnson' },
    { id: '2', title: 'Williams_Anlaşma.docx', date: '8 Haz 2023', case: 'Williams Boşanma' },
    { id: '3', title: 'Martinez_Delil.pdf', date: '5 Haz 2023', case: 'Martinez Ceza Davası' },
  ];

  const pendingTasks = [
    { id: '1', title: 'Smith Davası için Dilekçe Hazırla', dueDate: '14 Haz 2023', priority: 'Yüksek' },
    { id: '2', title: 'Williams Anlaşmasını İncele', dueDate: '16 Haz 2023', priority: 'Orta' },
    { id: '3', title: 'Martinez Duruşmasına Hazırlan', dueDate: '17 Haz 2023', priority: 'Yüksek' },
    { id: '4', title: 'Thompson ile Müvekkil Görüşmesi', dueDate: '15 Haz 2023', priority: 'Orta' },
  ];

  const navigateToLawbot = () => {
    router.push('/(tabs)/lawbot');
  };

  return (
    <ThemedView style={styles.container}>
      <ImageBackground
        source={require('../../assets/images/law-background.jpg')}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <LinearGradient
          colors={isDark ? ['rgba(15, 23, 42, 0.8)', 'rgba(30, 41, 59, 0.8)', 'rgba(51, 65, 85, 0.8)'] : ['rgba(219, 234, 254, 0.8)', 'rgba(239, 246, 255, 0.8)', 'rgba(248, 250, 252, 0.8)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradient}
        >
        {/* Background Design Elements */}
        <View style={styles.backgroundElements}>
          <Animated.View style={[styles.circle, styles.circle1, { opacity: fadeAnim }]} />
          <Animated.View style={[styles.circle, styles.circle2, { opacity: fadeAnim }]} />
          <Animated.View style={[styles.circle, styles.circle3, { opacity: fadeAnim }]} />
        </View>

        <FlatList
          ref={flatListRef}
          data={sections}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => {
            // Stats Overview
            if (item.type === 'stats') {
              return (
                <Animated.View style={[styles.statsContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                  <BlurView intensity={isDark ? 30 : 50} tint={isDark ? 'dark' : 'light'} style={[styles.statCard, isDark && styles.statCardDark]}>
                    <View style={styles.statCardContent}>
                      <View style={styles.statIconContainer}>
                        <Ionicons name="folder" size={28} color={Colors[colorScheme ?? 'light'].tint} />
                      </View>
                      <ThemedText type="subtitle" style={styles.statNumber}>12</ThemedText>
                      <ThemedText style={styles.statLabel}>Aktif Dosya</ThemedText>
                    </View>
                  </BlurView>

                  <BlurView intensity={isDark ? 30 : 50} tint={isDark ? 'dark' : 'light'} style={[styles.statCard, isDark && styles.statCardDark]}>
                    <View style={styles.statCardContent}>
                      <View style={styles.statIconContainer}>
                        <Ionicons name="people" size={28} color={Colors[colorScheme ?? 'light'].tint} />
                      </View>
                      <ThemedText type="subtitle" style={styles.statNumber}>24</ThemedText>
                      <ThemedText style={styles.statLabel}>Müvekkil</ThemedText>
                    </View>
                  </BlurView>

                  <BlurView intensity={isDark ? 30 : 50} tint={isDark ? 'dark' : 'light'} style={[styles.statCard, isDark && styles.statCardDark]}>
                    <View style={styles.statCardContent}>
                      <View style={styles.statIconContainer}>
                        <Ionicons name="calendar" size={28} color={Colors[colorScheme ?? 'light'].tint} />
                      </View>
                      <ThemedText type="subtitle" style={styles.statNumber}>5</ThemedText>
                      <ThemedText style={styles.statLabel}>Yakın Duruşma</ThemedText>
                    </View>
                  </BlurView>
                </Animated.View>
              );
            }

            // Quick Actions
            if (item.type === 'quickActions') {
              return (
                <Animated.View style={[styles.sectionContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>Hızlı Erişim</ThemedText>

                  <View style={styles.quickActionsContainer}>
                    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.quickActionWrapper}>
                      <TouchableOpacity style={styles.quickActionButton} onPress={navigateToAddCase}>
                        <View style={styles.quickActionIconContainer}>
                          <Ionicons name="add-circle" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                        </View>
                        <ThemedText style={styles.quickActionText}>Dosya Ekle</ThemedText>
                      </TouchableOpacity>
                    </BlurView>

                    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.quickActionWrapper}>
                      <TouchableOpacity style={styles.quickActionButton} onPress={navigateToAddClient}>
                        <View style={styles.quickActionIconContainer}>
                          <Ionicons name="person-add" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                        </View>
                        <ThemedText style={styles.quickActionText}>Müvekkil Ekle</ThemedText>
                      </TouchableOpacity>
                    </BlurView>

                    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.quickActionWrapper}>
                      <TouchableOpacity style={styles.quickActionButton} onPress={navigateToAddEvent}>
                        <View style={styles.quickActionIconContainer}>
                          <Ionicons name="calendar" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                        </View>
                        <ThemedText style={styles.quickActionText}>Etkinlik Ekle</ThemedText>
                      </TouchableOpacity>
                    </BlurView>
                  </View>
                </Animated.View>
              );
            }

            // Upcoming Hearings
            if (item.type === 'upcomingHearings') {
              return (
                <Animated.View style={[styles.sectionContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">Yaklaşan Duruşmalar</ThemedText>
                    <TouchableOpacity style={styles.viewAllButton}>
                      <ThemedText style={styles.viewAllText}>Tümünü Gör</ThemedText>
                      <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                    </TouchableOpacity>
                  </View>

                  <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.overviewCard}>
                    {upcomingHearings.map((hearing) => (
                      <TouchableOpacity key={hearing.id} style={styles.hearingItem}>
                        <View style={styles.hearingDateContainer}>
                          <Text style={styles.hearingDate}>{hearing.date.split(' ')[0]}</Text>
                          <Text style={styles.hearingMonth}>{hearing.date.split(' ')[1]}</Text>
                        </View>
                        <View style={styles.hearingDetails}>
                          <ThemedText type="defaultSemiBold">{hearing.title}</ThemedText>
                          <View style={styles.hearingTimeLocation}>
                            <Ionicons name="time-outline" size={14} color={Colors[colorScheme ?? 'light'].tint} />
                            <ThemedText style={styles.hearingDetailText}>{hearing.time}</ThemedText>
                            <Ionicons name="location-outline" size={14} color={Colors[colorScheme ?? 'light'].tint} />
                            <ThemedText style={styles.hearingDetailText}>{hearing.location}</ThemedText>
                          </View>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </BlurView>
                </Animated.View>
              );
            }

            // Recent Documents
            if (item.type === 'recentDocuments') {
              return (
                <Animated.View style={[styles.sectionContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">Son Belgeler</ThemedText>
                    <TouchableOpacity style={styles.viewAllButton}>
                      <ThemedText style={styles.viewAllText}>Tümünü Gör</ThemedText>
                      <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                    </TouchableOpacity>
                  </View>

                  <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.overviewCard}>
                    {recentDocuments.map((doc) => (
                      <TouchableOpacity key={doc.id} style={styles.documentItem}>
                        <View style={styles.documentIconContainer}>
                          <Ionicons name="document-text" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                        </View>
                        <View style={styles.documentDetails}>
                          <ThemedText type="defaultSemiBold">{doc.title}</ThemedText>
                          <ThemedText style={styles.documentCase}>{doc.case}</ThemedText>
                        </View>
                        <ThemedText style={styles.documentDate}>{doc.date}</ThemedText>
                      </TouchableOpacity>
                    ))}
                  </BlurView>
                </Animated.View>
              );
            }

            // Pending Tasks
            if (item.type === 'pendingTasks') {
              return (
                <Animated.View style={[styles.sectionContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">Bekleyen Görevler</ThemedText>
                    <TouchableOpacity style={styles.viewAllButton}>
                      <ThemedText style={styles.viewAllText}>Tümünü Gör</ThemedText>
                      <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                    </TouchableOpacity>
                  </View>

                  <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.overviewCard}>
                    {pendingTasks.map((task) => (
                      <View key={task.id} style={styles.taskItem}>
                        <View style={[styles.priorityIndicator,
                          task.priority === 'Yüksek' ? styles.highPriority :
                          task.priority === 'Orta' ? styles.mediumPriority :
                          styles.lowPriority]} />
                        <View style={styles.taskDetails}>
                          <ThemedText type="defaultSemiBold">{task.title}</ThemedText>
                          <View style={styles.taskMeta}>
                            <Ionicons name="calendar-outline" size={14} color={Colors[colorScheme ?? 'light'].tint} />
                            <ThemedText style={styles.taskDueDate}>Son Tarih: {task.dueDate}</ThemedText>
                            <View style={[styles.priorityBadge,
                              task.priority === 'Yüksek' ? styles.highPriorityBadge :
                              task.priority === 'Orta' ? styles.mediumPriorityBadge :
                              styles.lowPriorityBadge]}>
                              <ThemedText style={[styles.taskPriority,
                                task.priority === 'Yüksek' ? styles.highPriorityText :
                                task.priority === 'Orta' ? styles.mediumPriorityText :
                                styles.lowPriorityText]}>{task.priority}</ThemedText>
                            </View>
                          </View>
                        </View>
                        <TouchableOpacity style={styles.taskCheckbox}>
                          <Ionicons name="checkmark-circle-outline" size={24} color={Colors[colorScheme ?? 'light'].tint} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </BlurView>
                </Animated.View>
              );
            }

            // Recent Activities
            if (item.type === 'recentActivities') {
              return (
                <Animated.View style={[styles.sectionContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                  <View style={styles.sectionHeader}>
                    <ThemedText type="subtitle">Son Aktiviteler</ThemedText>
                    <TouchableOpacity style={styles.viewAllButton}>
                      <ThemedText style={styles.viewAllText}>Tümünü Gör</ThemedText>
                      <Ionicons name="chevron-forward" size={16} color={Colors[colorScheme ?? 'light'].tint} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.activitiesContainer}>
                    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.activityCard}>
                      <View style={styles.activityHeader}>
                        <View style={styles.activityIconContainer}>
                          <Ionicons name="document-text" size={22} color={Colors[colorScheme ?? 'light'].tint} />
                        </View>
                        <ThemedText type="defaultSemiBold">Dosya #1234 Güncellendi</ThemedText>
                      </View>
                      <ThemedText style={styles.activityDescription}>Smith vs. Johnson davasına yeni belgeler eklendi</ThemedText>
                      <View style={styles.activityFooter}>
                        <Ionicons name="time-outline" size={14} color={isDark ? '#9ca3af' : '#64748b'} />
                        <ThemedText style={styles.timestamp}>Bugün, 14:30</ThemedText>
                      </View>
                    </BlurView>

                    <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.activityCard}>
                      <View style={styles.activityHeader}>
                        <View style={styles.activityIconContainer}>
                          <Ionicons name="calendar" size={22} color={Colors[colorScheme ?? 'light'].tint} />
                        </View>
                        <ThemedText type="defaultSemiBold">Mahkeme Duruşması Planlandı</ThemedText>
                      </View>
                      <ThemedText style={styles.activityDescription}>Williams davası duruşması 15 Haziran 2023 tarihine planlandı</ThemedText>
                      <View style={styles.activityFooter}>
                        <Ionicons name="time-outline" size={14} color={isDark ? '#9ca3af' : '#64748b'} />
                        <ThemedText style={styles.timestamp}>Dün, 10:15</ThemedText>
                      </View>
                    </BlurView>
                  </View>
                </Animated.View>
              );
            }

            // Footer - Web only
            if (item.type === 'footer' && Platform.OS === 'web') {
              return (
                <View style={styles.fullWidthContainer}>
                  <Footer />
                </View>
              );
            }

            return null;
          }}
        />

        {/* Floating Lawbot Button */}
        <TouchableOpacity
          style={[styles.lawbotButton, { bottom: Platform.OS === 'ios' ? 90 : 30 }]}
          onPress={navigateToLawbot}
        >
          <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.lawbotButtonInner}>
            <Ionicons name="chatbubble-ellipses" size={28} color={Colors[colorScheme ?? 'light'].tint} />
          </BlurView>
        </TouchableOpacity>
        </LinearGradient>
      </ImageBackground>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundImageStyle: {
    opacity: 0.05,
    resizeMode: 'cover',
  },
  lawbotButton: {
    position: 'absolute',
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
    elevation: 5,
  },
  lawbotButtonInner: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 30,
  },
  gradient: {
    flex: 1,
    position: 'relative',
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  circle: {
    position: 'absolute',
    borderRadius: 999,
  },
  circle1: {
    width: 300,
    height: 300,
    backgroundColor: 'rgba(96, 165, 250, 0.2)',
    top: -50,
    right: -100,
  },
  circle2: {
    width: 200,
    height: 200,
    backgroundColor: 'rgba(139, 92, 246, 0.15)',
    bottom: 100,
    left: -50,
  },
  circle3: {
    width: 250,
    height: 250,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    bottom: -100,
    right: 50,
  },
  contentContainer: {
    flexGrow: 1,
    padding: 16,
    paddingTop: 60,
    paddingBottom: 40,
    ...(Platform.OS === 'web' && {
      width: '70%', // Reduced from 80% to 70% to match duruşmalar page
      maxWidth: 1000, // Reduced from 1200 to 1000 to match duruşmalar page
      marginLeft: 'auto', // Center the container
      marginRight: 'auto', // Center the container
    }),
  },
  fullWidthContainer: {
    width: '100%',
    maxWidth: '100%',
    marginLeft: 0,
    marginRight: 0,
  },
  headerContainer: {
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  headerBlur: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    gap: 4,
  },
  logoutText: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAllText: {
    color: Colors.light.tint,
    fontWeight: '600',
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statCardDark: {
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  statCardContent: {
    padding: 16,
    alignItems: 'center',
  },
  statIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.8,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 12,
  },
  quickActionWrapper: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  quickActionButton: {
    padding: 16,
    alignItems: 'center',
  },
  quickActionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionText: {
    fontWeight: '600',
  },
  overviewCard: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginBottom: 8,
  },
  // Hearing items
  hearingItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  hearingDateContainer: {
    width: 50,
    height: 50,
    backgroundColor: Colors.light.tint,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  hearingDate: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  hearingMonth: {
    color: 'white',
    fontSize: 12,
  },
  hearingDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  hearingTimeLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    gap: 4,
  },
  hearingDetailText: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  // Document items
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  documentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  documentDetails: {
    flex: 1,
  },
  documentCase: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  documentDate: {
    fontSize: 12,
    color: '#888',
  },
  // Task items
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  priorityIndicator: {
    width: 4,
    height: 36,
    borderRadius: 2,
    marginRight: 16,
  },
  highPriority: {
    backgroundColor: '#EF4444',
  },
  mediumPriority: {
    backgroundColor: '#F59E0B',
  },
  lowPriority: {
    backgroundColor: '#10B981',
  },
  taskDetails: {
    flex: 1,
  },
  taskMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    gap: 4,
  },
  taskDueDate: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  highPriorityBadge: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  mediumPriorityBadge: {
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
  },
  lowPriorityBadge: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  taskPriority: {
    fontSize: 11,
    fontWeight: 'bold',
  },
  highPriorityText: {
    color: '#EF4444',
  },
  mediumPriorityText: {
    color: '#F59E0B',
  },
  lowPriorityText: {
    color: '#10B981',
  },
  taskCheckbox: {
    padding: 8,
  },
  // Activity items
  activitiesContainer: {
    gap: 12,
  },
  activityCard: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    padding: 16,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  activityIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityDescription: {
    marginBottom: 12,
    fontSize: 14,
  },
  activityFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  timestamp: {
    fontSize: 12,
    color: '#888',
  },
});
