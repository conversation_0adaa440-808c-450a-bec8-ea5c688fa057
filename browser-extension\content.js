// Content script - UYAP sayfalarında çalışacak script

// JSESSIONID bilgisini gösteren element
let jsessionInfoElement = null;

// Sayfa yüklendiğinde çalışır
(function() {
  console.log('HukApp Extension content script loaded on:', window.location.href);

  // Sayfa URL'sini kontrol et
  console.log('Şu anki URL:', window.location.href);

  // JSESSIONID kontrolü yap - herhangi bir URL kontrolü yapmadan
  console.log('JSESSIONID kontrolü yapılıyor...');

  // JSESSIONID bilgi elementini oluştur
  createJsessionInfoElement();

  // Mevcut JSESSIONID'leri kontrol et
  checkExistingJsessionId();

  // JSESSIONID'yi kontrol et
  checkJsessionId();

  // Sayfadaki değişiklikleri dinle
  observeUrlChanges();

  // Sayfa tamamen yüklendiğinde çalıştır
  window.addEventListener('load', function() {
    console.log('<PERSON>fa tamamen yüklendi, JSESSIONID kontrolü tekrar yapılıyor...');

    // JSESSIONID'yi tekrar kontrol et
    checkJsessionId();
  });
})();

// Mevcut JSESSIONID'leri kontrol et
function checkExistingJsessionId() {
  console.log('Mevcut JSESSIONID\'leri kontrol ediliyor...');

  // Background script'e mesaj gönder
  chrome.runtime.sendMessage({
    action: 'getJsessionCookies'
  }, function(response) {
    if (response && response.cookies) {
      console.log('Background\'dan gelen cookie\'ler:', response.cookies);

      // JSESSIONID cookie'lerini kontrol et
      console.log('JSESSIONID cookie\'leri:', response.cookies);

      // Eğer JSESSIONID cookie'si varsa ve kullanıcı giriş yapmışsa, bilgi kutusunu güncelle
      if (response.cookies.length > 0 && response.cookies[0].isLoggedIn) {
        const cookie = response.cookies[0]; // İlk bulunan JSESSIONID cookie'sini kullan
        console.log('JSESSIONID bulundu ve kullanıcı giriş yapmış:', cookie);
        updateJsessionInfo(cookie.value, cookie.domain);
      } else {
        console.log('JSESSIONID bulunamadı veya kullanıcı giriş yapmamış');
      }
    } else {
      console.error('Background\'dan cookie bilgisi alınamadı');
    }
  });
}

// JSESSIONID bilgi elementini oluştur
function createJsessionInfoElement() {
  // Eğer element zaten varsa, tekrar oluşturma
  if (jsessionInfoElement) return;

  console.log('JSESSIONID bilgi elementini oluşturuluyor...');

  // Yeni element oluştur
  jsessionInfoElement = document.createElement('div');
  jsessionInfoElement.id = 'hukapp-jsession-info';

  // Başlık ekle
  const title = document.createElement('div');
  title.className = 'jsession-title';
  title.textContent = 'HukApp JSESSIONID Bilgisi';

  // Kapat butonu
  const closeButton = document.createElement('span');
  closeButton.className = 'jsession-close-button';
  closeButton.textContent = '×';
  closeButton.onclick = function() {
    jsessionInfoElement.style.display = 'none';
  };

  title.appendChild(closeButton);
  jsessionInfoElement.appendChild(title);

  // İçerik alanı
  const content = document.createElement('div');
  content.id = 'hukapp-jsession-content';
  content.textContent = 'JSESSIONID bekleniyor...';
  jsessionInfoElement.appendChild(content);

  // Sayfaya ekle
  try {
    if (document.body) {
      document.body.appendChild(jsessionInfoElement);
      console.log('JSESSIONID bilgi elementi sayfaya eklendi');
    } else {
      console.error('document.body bulunamadı, element eklenemedi');
      // body yoksa, DOM yüklendiğinde tekrar dene
      document.addEventListener('DOMContentLoaded', function() {
        if (document.body) {
          document.body.appendChild(jsessionInfoElement);
          console.log('JSESSIONID bilgi elementi sayfaya eklendi (DOMContentLoaded sonrası)');
        } else {
          console.error('DOMContentLoaded sonrası bile document.body bulunamadı');
        }
      });
    }
  } catch (error) {
    console.error('JSESSIONID bilgi elementi eklenirken hata oluştu:', error);
  }
}

// JSESSIONID bilgisini güncelle
function updateJsessionInfo(jsessionId, domain) {
  console.log('JSESSIONID bilgisi güncelleniyor:', jsessionId, domain);

  // JSESSIONID'nin string olduğundan emin ol
  let jsessionIdStr = '';

  if (typeof jsessionId === 'string') {
    jsessionIdStr = jsessionId;
  } else if (jsessionId && typeof jsessionId === 'object') {
    // Eğer bir obje ise, value özelliğini kullan
    jsessionIdStr = jsessionId.value || JSON.stringify(jsessionId);
  } else {
    // Diğer durumlar için string'e çevir
    jsessionIdStr = String(jsessionId || '');
  }

  console.log('JSESSIONID string değeri:', jsessionIdStr);

  if (!jsessionInfoElement) {
    console.log('JSESSIONID bilgi elementi yok, oluşturuluyor...');
    createJsessionInfoElement();
  }

  const content = document.getElementById('hukapp-jsession-content');
  if (content) {
    console.log('JSESSIONID içerik elementi bulundu, güncelleniyor...');
    const timestamp = new Date().toLocaleTimeString();

    // JSESSIONID'nin tamamını göster
    const fullJsessionId = jsessionIdStr;
    // Kısaltılmış versiyonu
    const shortJsessionId = jsessionIdStr.length > 20 ?
      jsessionIdStr.substring(0, 10) + '...' + jsessionIdStr.substring(jsessionIdStr.length - 10) :
      jsessionIdStr;

    // Clear existing content
    content.innerHTML = '';

    // JSESSIONID item
    const jsessionItem = document.createElement('div');
    jsessionItem.className = 'jsession-item';

    const jsessionLabel = document.createElement('strong');
    jsessionLabel.className = 'jsession-label';
    jsessionLabel.textContent = 'JSESSIONID: ';

    const jsessionValue = document.createElement('span');
    jsessionValue.className = 'jsession-value';
    jsessionValue.title = fullJsessionId;
    jsessionValue.textContent = shortJsessionId;

    const copyButton = document.createElement('button');
    copyButton.id = 'copy-jsessionid';
    copyButton.className = 'jsession-copy-button';
    copyButton.textContent = 'Kopyala';

    jsessionItem.appendChild(jsessionLabel);
    jsessionItem.appendChild(jsessionValue);
    jsessionItem.appendChild(copyButton);
    content.appendChild(jsessionItem);

    // Domain item
    const domainItem = document.createElement('div');
    domainItem.className = 'jsession-item';

    const domainLabel = document.createElement('strong');
    domainLabel.className = 'jsession-label';
    domainLabel.textContent = 'Domain: ';

    const domainValue = document.createElement('span');
    domainValue.className = 'jsession-value';
    domainValue.textContent = domain || 'Bilinmiyor';

    domainItem.appendChild(domainLabel);
    domainItem.appendChild(domainValue);
    content.appendChild(domainItem);

    // URL item
    const urlItem = document.createElement('div');
    urlItem.className = 'jsession-item';

    const urlLabel = document.createElement('strong');
    urlLabel.className = 'jsession-label';
    urlLabel.textContent = 'URL: ';

    const urlValue = document.createElement('span');
    urlValue.className = 'jsession-value';
    urlValue.textContent = window.location.href.substring(0, 30) + '...';

    urlItem.appendChild(urlLabel);
    urlItem.appendChild(urlValue);
    content.appendChild(urlItem);

    // Timestamp
    const timestampItem = document.createElement('div');
    timestampItem.className = 'jsession-timestamp';
    timestampItem.textContent = 'Son güncelleme: ' + timestamp;
    content.appendChild(timestampItem);

    // Textarea container
    const textareaContainer = document.createElement('div');
    textareaContainer.className = 'jsession-textarea-container';

    const textarea = document.createElement('textarea');
    textarea.id = 'full-jsessionid';
    textarea.className = 'jsession-textarea';
    textarea.value = fullJsessionId;

    textareaContainer.appendChild(textarea);
    content.appendChild(textareaContainer);

    // Setup copy button functionality using the external script
    if (window.setupCopyButton) {
      window.setupCopyButton();
    } else {
      console.error('setupCopyButton function not found');
    }
  } else {
    console.error('JSESSIONID içerik elementi bulunamadı');
  }

  // JSESSIONID bilgi elementini görünür yap
  if (jsessionInfoElement) {
    jsessionInfoElement.style.display = 'block';
  }
}

// JSESSIONID'yi kontrol et
function checkJsessionId() {
  console.log('JSESSIONID kontrol ediliyor...');

  // Background script'e mesaj gönder
  chrome.runtime.sendMessage({
    action: 'checkJsessionId',
    domain: window.location.hostname
  });
}

// URL değişikliklerini izle
function observeUrlChanges() {
  let lastUrl = window.location.href;

  // MutationObserver ile DOM değişikliklerini izle
  const observer = new MutationObserver(() => {
    if (lastUrl !== window.location.href) {
      lastUrl = window.location.href;
      console.log('URL değişti:', lastUrl);

      // URL değiştiğinde JSESSIONID'yi tekrar kontrol et
      checkJsessionId();
    }
  });

  // Tüm DOM değişikliklerini izle
  observer.observe(document, { subtree: true, childList: true });
}

// Kullanıcının giriş yapmış olup olmadığını kontrol et
function checkIfLoggedIn() {
  console.log('Kullanıcının giriş durumu kontrol ediliyor...');

  // Background script'e mesaj gönder
  chrome.runtime.sendMessage({
    action: 'checkLoginStatus'
  }, function(response) {
    if (response && response.isLoggedIn) {
      console.log('Kullanıcı giriş yapmış:', response);

      // Kullanıcı giriş yapmışsa JSESSIONID bilgilerini kontrol et
      createJsessionInfoElement();
      checkExistingJsessionId();
      checkJsessionId();
      observeUrlChanges();
    } else {
      console.log('Kullanıcı henüz giriş yapmamış veya yanıt alınamadı:', response);
    }
  });
}

// Background script'ten gelen mesajları dinle
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.action === 'jsessionIdFound') {
    console.log('JSESSIONID bulundu:', message.jsessionId);
    updateJsessionInfo(message.jsessionId, message.domain);
  }
});
