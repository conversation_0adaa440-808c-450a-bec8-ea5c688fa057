{"name": "h<PERSON><PERSON>-frontend", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/slider": "^4.5.6", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "assert": "^2.1.0", "axios": "^0.27.2", "browserify-zlib": "^0.2.0", "crypto-browserify": "^3.12.1", "date-fns": "^2.30.0", "dotenv": "^16.4.7", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.1.5", "expo-notifications": "^0.31.2", "expo-router": "~5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "^0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "framer-motion": "^10.18.0", "https-browserify": "^1.0.0", "lightningcss": "1.24.0", "lightningcss-win32-x64-msvc": "1.29.3", "nativewind": "^4.1.23", "openai": "^4.93.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "^0.79.2", "react-native-axios": "^0.17.1", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.24.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^3.3.2", "url": "^0.11.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-native-css-transformer": "^2.0.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}