<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>AVAS Extension</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
  <div class="main-container">
      <!-- Sol Taraf - Logo ve Motto -->
      <div class="left-section">
        <div class="logo-container">
          <div class="logo-icon-container">
            <i class="fas fa-balance-scale"></i>
          </div>
          <h1 class="app-title">AVAS</h1>
          <p class="motto">Adaletin Dijital Asistanı</p>
          <div class="divider"></div>
          <p class="description-text">
            Modern avukatlık süreçlerinizi dijitalleştirin,
            dava ve müvekkil yönetimini kolaylaştırın.
          </p>
        </div>
      </div>

      <!-- Sağ Taraf - Giriş Formu -->
      <div class="right-section">
        <div id="loginForm" class="form">
          <div class="form-header">
            <h2 class="form-title">Giriş Yapın</h2>
            <div class="form-title-underline"></div>
          </div>

          <div class="input-group">
            <div class="label-container">
              <i class="fas fa-envelope input-icon"></i>
              <label for="email">E-posta</label>
            </div>
            <div class="input-wrapper">
              <input type="email" id="email" placeholder="E-posta adresinizi girin">
            </div>
          </div>

          <div class="input-group">
            <div class="label-container">
              <i class="fas fa-lock input-icon"></i>
              <label for="password">Şifre</label>
            </div>
            <div class="input-wrapper">
              <input type="password" id="password" placeholder="Şifrenizi girin">
              <div class="eye-icon" id="togglePassword">
                <i class="fas fa-eye"></i>
              </div>
            </div>
          </div>

          <div class="kvkk-consent">
            <input type="checkbox" id="loginKvkkConsent">
            <label for="loginKvkkConsent">
              <a href="#" id="showLoginKvkk">KVKK Aydınlatma Metni</a>'ni okudum ve kabul ediyorum.
            </label>
          </div>

          <div class="error-message" id="loginError"></div>
          <button id="loginButton" class="login-button">Giriş Yap</button>
        </div>

    <!-- User Info -->
    <div id="userInfo" class="form hidden">
      <div class="form-header">
        <h2 class="form-title">Hoş Geldiniz</h2>
        <div class="form-title-underline"></div>
      </div>

      <div class="user-info-container">
        <div class="user-avatar">
          <i class="fas fa-user-circle"></i>
        </div>
        <div class="user-details">
          <p id="userFullName" class="user-name"></p>
          <p id="userEmail" class="user-email"></p>
        </div>
      </div>

      <div class="status">
        <p>Bağlantı Durumu: <span id="connectionStatus">Bağlı Değil</span></p>
      </div>

      <div class="sync-container">
        <button id="syncButton" class="sync-button">
          <i class="fas fa-sync-alt"></i> Senkronize Et
        </button>
        <p class="sync-info">UYAP/KamuSM oturumunuzu senkronize etmek için önce ilgili siteye giriş yapın, sonra bu butona tıklayın.</p>
      </div>

      <div class="kvkk-info">
        <p>Bu extension, UYAP ve KamuSM sistemlerindeki oturum bilgilerinizi AVAS uygulamasıyla senkronize etmek için kullanılır. Bilgileriniz KVKK kapsamında korunmaktadır. <a href="#" id="showUserKvkk">Detaylı bilgi</a></p>
      </div>

      <button id="logoutButton" class="logout-button">Çıkış Yap</button>
    </div>

    <!-- KVKK Modal -->
    <div id="kvkkModal" class="modal hidden">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h2>KVKK Aydınlatma Metni</h2>
        <div class="kvkk-text">
          <p>
            <strong>Kişisel Verilerin Korunması Kanunu Kapsamında Aydınlatma Metni</strong>
          </p>
          <p>
            Bu aydınlatma metni, 6698 sayılı Kişisel Verilerin Korunması Kanunu ("KVKK") uyarınca, AVAS tarafından işlenen kişisel verileriniz hakkında sizi bilgilendirmek amacıyla hazırlanmıştır.
          </p>
          <p>
            <strong>1. Veri Sorumlusu</strong><br>
            AVAS (Adaletin Dijital Asistanı), KVKK kapsamında veri sorumlusu olarak hareket etmektedir.
          </p>
          <p>
            <strong>2. İşlenen Kişisel Veriler</strong><br>
            AVAS Extension kullanımı sırasında aşağıdaki kişisel verileriniz işlenmektedir:
            <ul>
              <li>Ad, soyad, e-posta adresi gibi kimlik ve iletişim bilgileriniz</li>
              <li>UYAP ve KamuSM sistemlerine erişim için kullanılan oturum bilgileriniz (JSESSIONID)</li>
              <li>Extension kullanımınıza ilişkin log kayıtları</li>
            </ul>
          </p>
          <p>
            <strong>3. Kişisel Verilerin İşlenme Amacı</strong><br>
            Kişisel verileriniz aşağıdaki amaçlarla işlenmektedir:
            <ul>
              <li>AVAS hizmetlerinin sunulması ve geliştirilmesi</li>
              <li>UYAP ve KamuSM sistemlerindeki dava ve belge bilgilerinize erişim sağlanması</li>
              <li>Hukuki süreçlerinizin takibi ve yönetimi</li>
              <li>Yasal yükümlülüklerin yerine getirilmesi</li>
            </ul>
          </p>
          <p>
            <strong>4. Kişisel Verilerin Aktarılması</strong><br>
            Kişisel verileriniz, yukarıda belirtilen amaçlar doğrultusunda, AVAS hizmetlerinin sunulması için gerekli olduğu ölçüde, yasal yükümlülüklerimiz kapsamında yetkili kamu kurum ve kuruluşlarına aktarılabilir.
          </p>
          <p>
            <strong>5. Kişisel Veri Sahibinin Hakları</strong><br>
            KVKK'nın 11. maddesi uyarınca, kişisel veri sahibi olarak aşağıdaki haklara sahipsiniz:
            <ul>
              <li>Kişisel verilerinizin işlenip işlenmediğini öğrenme</li>
              <li>Kişisel verileriniz işlenmişse buna ilişkin bilgi talep etme</li>
              <li>Kişisel verilerinizin işlenme amacını ve bunların amacına uygun kullanılıp kullanılmadığını öğrenme</li>
              <li>Yurt içinde veya yurt dışında kişisel verilerinizin aktarıldığı üçüncü kişileri bilme</li>
              <li>Kişisel verilerinizin eksik veya yanlış işlenmiş olması hâlinde bunların düzeltilmesini isteme</li>
              <li>KVKK'nın 7. maddesinde öngörülen şartlar çerçevesinde kişisel verilerinizin silinmesini veya yok edilmesini isteme</li>
              <li>Kişisel verilerinizin aktarıldığı üçüncü kişilere yukarıda sayılan işlemlerin bildirilmesini isteme</li>
              <li>İşlenen verilerin münhasıran otomatik sistemler vasıtasıyla analiz edilmesi suretiyle aleyhinize bir sonucun ortaya çıkmasına itiraz etme</li>
              <li>Kişisel verilerinizin kanuna aykırı olarak işlenmesi sebebiyle zarara uğramanız hâlinde zararın giderilmesini talep etme</li>
            </ul>
          </p>
          <p>
            Bu haklarınızı kullanmak için <strong><EMAIL></strong> adresine e-posta gönderebilirsiniz.
          </p>
          <p>
            <strong>6. Oturum Bilgilerinin Kullanımı</strong><br>
            AVAS Extension, UYAP ve KamuSM sistemlerindeki oturum bilgilerinizi (JSESSIONID) yalnızca sizin adınıza ve sizin yetkilendirmenizle kullanmaktadır. Bu bilgiler, AVAS uygulamasının UYAP ve KamuSM sistemlerindeki verilerinize erişebilmesi ve bu verileri sizin için düzenleyebilmesi amacıyla kullanılmaktadır. Oturum bilgileriniz, güvenli bir şekilde saklanmakta ve üçüncü taraflarla paylaşılmamaktadır.
          </p>
        </div>
        <button id="acceptKvkk">Kabul Ediyorum</button>
      </div>

      <!-- Dekoratif Daireler -->
      <div class="decorative-circle decorative-circle1"></div>
      <div class="decorative-circle decorative-circle2"></div>
      <div class="decorative-circle decorative-circle3"></div>
    </div>

  <script src="popup.js"></script>
  <script src="popup-animations.js"></script>
</body>
</html>
