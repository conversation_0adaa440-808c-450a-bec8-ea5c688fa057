import { StyleSheet, TouchableOpacity, View, FlatList, ActivityIndicator, Alert, TextInput, Platform } from 'react-native';
import React, { useState, useEffect, useMemo } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import transactionService from '@/services/transactionService';
import Pagination from '@/components/Pagination';

export default function TransactionsScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // API state
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('ALL'); // ALL, INCOME, EXPENSE

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Show 10 items per page

  // Toplam değerler
  const [totalIncome, setTotalIncome] = useState(0);
  const [totalExpense, setTotalExpense] = useState(0);

  // API'den işlemleri getir
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        setError('');
        const data = await transactionService.getAllTransactions();
        setTransactions(data);

        // Toplam gelir ve giderleri hesapla
        calculateTotals(data);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching transactions:', err);
        setError('İşlemler yüklenirken bir hata oluştu.');
        setLoading(false);
      }
    };

    fetchTransactions();
  }, []);

  // Toplam gelir ve giderleri hesapla
  const calculateTotals = (data) => {
    let income = 0;
    let expense = 0;

    data.forEach(transaction => {
      if (transaction.type === 'INCOME') {
        income += transaction.amount;
      } else {
        expense += transaction.amount;
      }
    });

    setTotalIncome(income);
    setTotalExpense(expense);
  };

  // Yeni işlem ekleme sayfasına git
  const navigateToAddTransaction = () => {
    router.push('/transactions/add');
  };

  // İşlem detay sayfasına git
  const navigateToTransactionDetail = (id) => {
    router.push(`/transactions/${id}`);
  };

  // İşlem silme
  const handleDeleteTransaction = (id) => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel'
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await transactionService.deleteTransaction(id);
              const updatedTransactions = transactions.filter(transaction => transaction.id !== id);
              setTransactions(updatedTransactions);
              calculateTotals(updatedTransactions);
            } catch (err) {
              console.error('Error deleting transaction:', err);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Tarih formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Para formatla
  const formatCurrency = (amount) => {
    return amount.toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }) + ' ₺';
  };

  // Filtrelenmiş işlemler
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      // Tip filtreleme
      if (filterType !== 'ALL' && transaction.type !== filterType) {
        return false;
      }

      // Arama filtreleme
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          transaction.description.toLowerCase().includes(query) ||
          transaction.category.toLowerCase().includes(query) ||
          (transaction.client && transaction.client.toLowerCase().includes(query)) ||
          (transaction.case && transaction.case.toLowerCase().includes(query))
        );
      }

      return true;
    });
  }, [transactions, filterType, searchQuery]);

  // Calculate total pages for pagination
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);

  // Get paginated transactions
  const paginatedTransactions = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredTransactions.slice(startIndex, endIndex);
  }, [filteredTransactions, currentPage, itemsPerPage]);

  // İşlem öğesi render
  const renderTransactionItem = ({ item }) => (
    <TouchableOpacity
      style={styles.transactionItem}
      onPress={() => navigateToTransactionDetail(item.id)}
    >
      <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.transactionCard}>
        <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>
            <View style={[
              styles.typeBadge,
              { backgroundColor: item.type === 'INCOME' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)' }
            ]}>
              <Ionicons
                name={item.type === 'INCOME' ? 'arrow-down' : 'arrow-up'}
                size={16}
                color={item.type === 'INCOME' ? '#22C55E' : '#EF4444'}
              />
              <ThemedText style={[
                styles.typeText,
                { color: item.type === 'INCOME' ? '#22C55E' : '#EF4444' }
              ]}>
                {item.type === 'INCOME' ? 'Gelir' : 'Gider'}
              </ThemedText>
            </View>
            <ThemedText style={styles.transactionDate}>{formatDate(item.date)}</ThemedText>
          </View>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteTransaction(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#EF4444" />
          </TouchableOpacity>
        </View>

        <ThemedText type="defaultSemiBold" style={styles.transactionDescription}>
          {item.description}
        </ThemedText>

        <View style={styles.transactionDetails}>
          <View style={styles.detailRow}>
            <ThemedText style={styles.detailLabel}>Kategori:</ThemedText>
            <ThemedText style={styles.detailValue}>{item.category}</ThemedText>
          </View>

          {item.client && (
            <View style={styles.detailRow}>
              <ThemedText style={styles.detailLabel}>Müvekkil:</ThemedText>
              <ThemedText style={styles.detailValue}>{item.client}</ThemedText>
            </View>
          )}

          {item.case && (
            <View style={styles.detailRow}>
              <ThemedText style={styles.detailLabel}>Dava:</ThemedText>
              <ThemedText style={styles.detailValue}>{item.case}</ThemedText>
            </View>
          )}
        </View>

        <View style={styles.transactionFooter}>
          <ThemedText style={[
            styles.transactionAmount,
            { color: item.type === 'INCOME' ? '#22C55E' : '#EF4444' }
          ]}>
            {item.type === 'INCOME' ? '+' : '-'} {formatCurrency(item.amount)}
          </ThemedText>
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Ofis İşlemleri</ThemedText>
        <TouchableOpacity onPress={navigateToAddTransaction}>
          <Ionicons name="add-circle" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
      </ThemedView>

      {/* Özet Kartı */}
      <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.summaryCard}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Toplam Gelir</ThemedText>
            <ThemedText style={[styles.summaryValue, styles.incomeValue]}>
              {formatCurrency(totalIncome)}
            </ThemedText>
          </View>

          <View style={styles.summaryDivider} />

          <View style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Toplam Gider</ThemedText>
            <ThemedText style={[styles.summaryValue, styles.expenseValue]}>
              {formatCurrency(totalExpense)}
            </ThemedText>
          </View>
        </View>

        <View style={styles.balanceContainer}>
          <ThemedText style={styles.balanceLabel}>Bakiye</ThemedText>
          <ThemedText style={[
            styles.balanceValue,
            { color: totalIncome - totalExpense >= 0 ? '#22C55E' : '#EF4444' }
          ]}>
            {formatCurrency(totalIncome - totalExpense)}
          </ThemedText>
        </View>
      </BlurView>

      {/* Arama ve Filtre */}
      <View style={styles.searchFilterContainer}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
          <TextInput
            style={[styles.searchInput, isDark && styles.searchInputDark]}
            placeholder="İşlem ara..."
            placeholderTextColor={isDark ? '#9ca3af' : '#64748b'}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={isDark ? '#9ca3af' : '#64748b'} />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={styles.filterButtons}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === 'ALL' && styles.filterButtonActive
            ]}
            onPress={() => setFilterType('ALL')}
          >
            <ThemedText style={filterType === 'ALL' ? styles.filterTextActive : null}>
              Tümü
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === 'INCOME' && styles.filterButtonActive
            ]}
            onPress={() => setFilterType('INCOME')}
          >
            <ThemedText style={filterType === 'INCOME' ? styles.filterTextActive : null}>
              Gelir
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === 'EXPENSE' && styles.filterButtonActive
            ]}
            onPress={() => setFilterType('EXPENSE')}
          >
            <ThemedText style={filterType === 'EXPENSE' ? styles.filterTextActive : null}>
              Gider
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      {loading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>İşlemler yükleniyor...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <ThemedText style={styles.errorText}>{error}</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={async () => {
              try {
                setLoading(true);
                setError('');
                const data = await transactionService.getAllTransactions();
                setTransactions(data);
                calculateTotals(data);
                setLoading(false);
              } catch (err) {
                console.error('Error retrying fetch transactions:', err);
                setError('İşlemler yüklenirken bir hata oluştu.');
                setLoading(false);
              }
            }}
          >
            <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : filteredTransactions.length === 0 ? (
        <ThemedView style={styles.emptyContainer}>
          <Ionicons name="cash-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
          <ThemedText style={styles.emptyText}>
            {searchQuery || filterType !== 'ALL'
              ? 'Arama kriterlerine uygun işlem bulunamadı'
              : 'Henüz işlem bulunmuyor'}
          </ThemedText>
          <TouchableOpacity style={styles.addEmptyButton} onPress={navigateToAddTransaction}>
            <ThemedText style={styles.addEmptyButtonText}>Yeni İşlem Ekle</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : (
        <>
          <FlatList
            data={Platform.OS === 'web' ? paginatedTransactions : filteredTransactions}
            renderItem={renderTransactionItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.transactionList}
            showsVerticalScrollIndicator={false}
          />

          {/* Modern Pagination - Web only - Appears after the item cards end */}
          {Platform.OS === 'web' && filteredTransactions.length > itemsPerPage && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={filteredTransactions.length}
              onPageChange={(page) => {
                setCurrentPage(page);
                // Scroll to top when changing pages
                window.scrollTo(0, 0);
              }}
              itemsPerPage={itemsPerPage}
            />
          )}
        </>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  summaryCard: {
    margin: 16,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryDivider: {
    width: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 16,
  },
  summaryLabel: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  incomeValue: {
    color: '#22C55E',
  },
  expenseValue: {
    color: '#EF4444',
  },
  balanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  balanceLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  balanceValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  searchFilterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    marginLeft: 8,
    color: '#000',
  },
  searchInputDark: {
    color: '#fff',
  },
  filterButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  filterButtonActive: {
    backgroundColor: Colors.light.tint,
    borderColor: Colors.light.tint,
  },
  filterTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  transactionList: {
    padding: 16,
    paddingTop: 0,
  },
  transactionItem: {
    marginBottom: 16,
  },
  transactionCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  transactionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  transactionDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  deleteButton: {
    padding: 4,
  },
  transactionDescription: {
    marginBottom: 12,
  },
  transactionDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  detailLabel: {
    opacity: 0.7,
    marginRight: 4,
  },
  detailValue: {
    fontWeight: '500',
  },
  transactionFooter: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 12,
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  addEmptyButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addEmptyButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
