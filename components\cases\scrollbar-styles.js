/**
 * JavaScript alternative to scrollbar-styles.css
 * This file provides the same functionality but without requiring CSS processing
 */
import { Platform } from 'react-native';

// Apply these styles directly to React Native components
export const scrollbarStyles = {
  noScrollbar: Platform.OS === 'web' ? {
    // Web-only styles
  } : {
    // React Native styles
  },

  customScrollbar: Platform.OS === 'web' ? {
    // Web-only styles
  } : {
    // React Native styles
  }
};

// For web only - inject CSS into the document head
if (typeof document !== 'undefined') {
  // Create a style element
  const style = document.createElement('style');

  // Add the CSS content
  style.textContent = `
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    /* Custom scrollbar styles */
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 0 0 8px 0;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .custom-scrollbar {
      scrollbar-width: thin;
      scrollbar-color: #888 #f1f1f1;
    }
  `;

  // Append the style element to the head
  document.head.appendChild(style);
}
