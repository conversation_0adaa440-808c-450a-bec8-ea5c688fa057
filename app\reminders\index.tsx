import { StyleSheet, TouchableOpacity, View, FlatList, ActivityIndicator, Alert, Platform } from 'react-native';
import React, { useState, useEffect, useMemo } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import reminderService from '@/services/reminderService';
import Pagination from '@/components/Pagination';

// Öncelik renkleri
const PRIORITY_COLORS = {
  'LOW': { bg: 'rgba(34, 197, 94, 0.1)', text: '#22C55E' },
  'MEDIUM': { bg: 'rgba(249, 115, 22, 0.1)', text: '#F97316' },
  'HIGH': { bg: 'rgba(239, 68, 68, 0.1)', text: '#EF4444' },
  'CRITICAL': { bg: 'rgba(124, 58, 237, 0.1)', text: '#7C3AED' },
};

// Öncelik çevirileri
const PRIORITY_TRANSLATIONS = {
  'LOW': 'DÜŞÜK',
  'MEDIUM': 'ORTA',
  'HIGH': 'YÜKSEK',
  'CRITICAL': 'KRİTİK',
};

export default function RemindersScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // API state
  const [reminders, setReminders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Show 10 items per page

  // API'den hatırlatıcıları getir
  useEffect(() => {
    const fetchReminders = async () => {
      try {
        setLoading(true);
        setError('');
        const data = await reminderService.getAllReminders();
        setReminders(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching reminders:', err);
        setError('Hatırlatıcılar yüklenirken bir hata oluştu.');
        setLoading(false);
      }
    };

    fetchReminders();
  }, []);

  // Yeni hatırlatıcı ekleme sayfasına git
  const navigateToAddReminder = () => {
    router.push('/reminders/add');
  };

  // Hatırlatıcı detay sayfasına git
  const navigateToReminderDetail = (id) => {
    router.push(`/reminders/${id}`);
  };

  // Hatırlatıcı silme
  const handleDeleteReminder = (id) => {
    Alert.alert(
      'Hatırlatıcıyı Sil',
      'Bu hatırlatıcıyı silmek istediğinize emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel'
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderService.deleteReminder(id);
              setReminders(reminders.filter(reminder => reminder.id !== id));
            } catch (err) {
              console.error('Error deleting reminder:', err);
              Alert.alert('Hata', 'Hatırlatıcı silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Tarih formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate total pages for pagination
  const totalPages = Math.ceil(reminders.length / itemsPerPage);

  // Get paginated reminders
  const paginatedReminders = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return reminders.slice(startIndex, endIndex);
  }, [reminders, currentPage, itemsPerPage]);

  // Hatırlatıcı öğesi render
  const renderReminderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.reminderItem}
      onPress={() => navigateToReminderDetail(item.id)}
    >
      <BlurView intensity={isDark ? 40 : 60} tint={isDark ? 'dark' : 'light'} style={styles.reminderCard}>
        <View style={styles.reminderHeader}>
          <View style={styles.reminderTitleContainer}>
            <ThemedText type="defaultSemiBold" style={styles.reminderTitle}>{item.title}</ThemedText>
            <View style={[
              styles.priorityBadge,
              { backgroundColor: PRIORITY_COLORS[item.priority]?.bg || PRIORITY_COLORS.MEDIUM.bg }
            ]}>
              <ThemedText style={[
                styles.priorityText,
                { color: PRIORITY_COLORS[item.priority]?.text || PRIORITY_COLORS.MEDIUM.text }
              ]}>
                {PRIORITY_TRANSLATIONS[item.priority] || 'ORTA'}
              </ThemedText>
            </View>
          </View>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteReminder(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#EF4444" />
          </TouchableOpacity>
        </View>

        <ThemedText style={styles.reminderDescription} numberOfLines={2}>
          {item.description}
        </ThemedText>

        <View style={styles.reminderFooter}>
          <View style={styles.dateContainer}>
            <Ionicons name="calendar-outline" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
            <ThemedText style={styles.dateText}>{formatDate(item.dueDate)}</ThemedText>
          </View>

          {item.repeatIntervalInHours > 0 && (
            <View style={styles.repeatContainer}>
              <Ionicons name="repeat" size={16} color={isDark ? '#9ca3af' : '#64748b'} />
              <ThemedText style={styles.repeatText}>
                {item.repeatIntervalInHours === 24 ? 'Günlük' :
                 item.repeatIntervalInHours === 168 ? 'Haftalık' :
                 item.repeatIntervalInHours === 720 ? 'Aylık' :
                 `${item.repeatIntervalInHours} saat`}
              </ThemedText>
            </View>
          )}
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
        <ThemedText type="title">Hatırlatıcılar</ThemedText>
        <TouchableOpacity onPress={navigateToAddReminder}>
          <Ionicons name="add-circle" size={24} color={Colors[colorScheme ?? 'light'].tint} />
        </TouchableOpacity>
      </ThemedView>

      {loading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>Hatırlatıcılar yükleniyor...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <ThemedText style={styles.errorText}>{error}</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={async () => {
              try {
                setLoading(true);
                setError('');
                const data = await reminderService.getAllReminders();
                setReminders(data);
                setLoading(false);
              } catch (err) {
                console.error('Error retrying fetch reminders:', err);
                setError('Hatırlatıcılar yüklenirken bir hata oluştu.');
                setLoading(false);
              }
            }}
          >
            <ThemedText style={styles.retryButtonText}>Tekrar Dene</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : reminders.length === 0 ? (
        <ThemedView style={styles.emptyContainer}>
          <Ionicons name="notifications-outline" size={48} color={isDark ? '#9ca3af' : '#64748b'} />
          <ThemedText style={styles.emptyText}>Henüz hatırlatıcı bulunmuyor</ThemedText>
          <TouchableOpacity style={styles.addEmptyButton} onPress={navigateToAddReminder}>
            <ThemedText style={styles.addEmptyButtonText}>Yeni Hatırlatıcı Ekle</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : (
        <>
          <FlatList
            data={Platform.OS === 'web' ? paginatedReminders : reminders}
            renderItem={renderReminderItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.reminderList}
            showsVerticalScrollIndicator={false}
          />

          {/* Modern Pagination - Web only - Appears after the item cards end */}
          {Platform.OS === 'web' && reminders.length > itemsPerPage && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={reminders.length}
              onPageChange={(page) => {
                setCurrentPage(page);
                // Scroll to top when changing pages
                window.scrollTo(0, 0);
              }}
              itemsPerPage={itemsPerPage}
            />
          )}
        </>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  reminderList: {
    padding: 16,
  },
  reminderItem: {
    marginBottom: 16,
  },
  reminderCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  reminderTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  reminderTitle: {
    marginRight: 8,
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 'auto',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  deleteButton: {
    marginLeft: 8,
    padding: 4,
  },
  reminderDescription: {
    marginBottom: 12,
  },
  reminderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    marginLeft: 4,
    fontSize: 12,
    opacity: 0.7,
  },
  repeatContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  repeatText: {
    marginLeft: 4,
    fontSize: 12,
    opacity: 0.7,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  addEmptyButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addEmptyButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
